


# Configuration for Partner Tenant Access
$partnerTenantId = "ad926c03-4b20-4cfa-9353-f896a61b0d25"  # Your partner tenant ID

# File paths for saving tokens and configuration
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$tokenFilePath = Join-Path $scriptPath "azure_tokens.json"
$contextFilePath = Join-Path $scriptPath "azure_context.json"

# Admin Agent Security Group ID for DAP access
$adminAgentGroupId = "4e515f36-d3ab-46bb-898e-************"

# Function to save current Azure context and tokens for future use
function Save-AzureTokens {
    [CmdletBinding()]
    param()
    
    try {
        # Simply save the current Azure context which includes refresh tokens
        # This is better than trying to manually extract tokens which can be complicated
        Save-AzContext -Path $contextFilePath -Force
        
        Write-Host "Azure context saved successfully to:" -ForegroundColor Green
        Write-Host "  - Context file: $contextFilePath" -ForegroundColor Green
        
        # Note: The Az module automatically saves refresh tokens in the context file
        # No need to extract them manually which avoids syntax issues
        
        return $true
    }
    catch {
        Write-Error "Failed to save Azure context: $($_.Exception.Message)"
        return $false
    }
}

# Function to connect using saved tokens (non-interactive)
function Connect-AzureWithSavedTokens {
    [CmdletBinding()]
    param()
    
    if (Test-Path $contextFilePath) {
        try {
            Write-Host "Connecting with saved context..." -ForegroundColor Cyan
            Import-AzContext -Path $contextFilePath -ErrorAction Stop
            
            # Verify the connection
            $context = Get-AzContext
            if ($context) {
                Write-Host "Successfully connected using saved context!" -ForegroundColor Green
                Write-Host "Account: $($context.Account.Id)" -ForegroundColor Green
                Write-Host "Tenant: $($context.Tenant.Id)" -ForegroundColor Green
                Write-Host "Subscription: $($context.Subscription.Name)" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Warning "Error connecting with saved context: $_"
        }
    }
    
    Write-Warning "No saved context found or connection failed. A new interactive login is required."
    return $false
}

# Load the Az module if not already loaded
if (-not (Get-Module -Name Az -ListAvailable)) {
    Write-Host "Installing Az module..." -ForegroundColor Cyan
    Install-Module -Name Az -Scope CurrentUser -Repository PSGallery -Force
}

# Main authentication logic - try saved context first, then interactive
try {
    # Try to load from the saved context file first
    if (Test-Path $contextFilePath) {
        Write-Host "Found saved context file. Attempting to connect using stored credentials..." -ForegroundColor Cyan
        Import-AzContext -Path $contextFilePath -ErrorAction Stop
        
        # Verify connection worked
        $context = Get-AzContext
        if ($context) {
            Write-Host "Successfully connected using saved context!" -ForegroundColor Green
            Write-Host "Connected as: $($context.Account.Id)" -ForegroundColor Green
            
            # Save context again to refresh the file
            Save-AzureTokens | Out-Null
        }
    }
    else {
        throw "No saved context found. Interactive login required."
    }
}
catch {
    Write-Host "Initiating interactive login with user account..." -ForegroundColor Yellow
    Write-Host "IMPORTANT: Login with a user account that is a member of the Admin Agents group!" -ForegroundColor Yellow
    Write-Host "This is required for DAP access to customer subscriptions." -ForegroundColor Yellow
    
    try {
        # Connect interactively to the partner tenant
        Connect-AzAccount -TenantId $partnerTenantId -ErrorAction Stop
        
        # Save the tokens for future non-interactive use
        Save-AzureTokens | Out-Null
        Write-Host "Authentication context has been saved for future non-interactive use." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to connect with user account: $($_.Exception.Message)"
        exit 1
    }
}

# Verify Azure connection information and display details
try {
    # Verify connection
    $context = Get-AzContext
    Write-Host "Connected to Azure as: $($context.Account.Id)" -ForegroundColor Green
    Write-Host "Current Tenant: $($context.Tenant.Id)" -ForegroundColor Green
    Write-Host "Current Subscription: $($context.Subscription.Name)" -ForegroundColor Green
}
catch {
    Write-Error "Error getting Azure context: $($_.Exception.Message)"
    exit 1
}

# Common IDs and references
$adminAgentGroupId = "4e515f36-d3ab-46bb-898e-************"  # Admin Agent Security Group for DAP access
$supportRequestContributorRoleId = "cfd33db0-3dd1-45e3-aa9d-cdbdf3b6f24e"  # Role ID

# Function to access a customer subscription using DAP
function Access-CustomerSubscription {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$SubscriptionId,
        
        [Parameter(Mandatory = $false)]
        [string]$TenantId = $null
    )
    
    try {
        Write-Host "`nAttempting to access subscription: ${SubscriptionId}" -ForegroundColor Yellow
        
        # Find the subscription in the list of available subscriptions
        $availableSubs = Get-AzSubscription -ErrorAction SilentlyContinue
        $targetSub = $availableSubs | Where-Object { $_.Id -eq $SubscriptionId }
        
        if (-not $targetSub) {
            Write-Warning "Subscription ${SubscriptionId} not found in list of available subscriptions"
            Write-Host "This indicates your account doesn't have DAP access to this subscription" -ForegroundColor Yellow
            return $null
        }
        
        # If TenantId wasn't provided, use the one from the found subscription
        if (-not $TenantId) {
            $TenantId = $targetSub.TenantId
            Write-Host "Using discovered tenant ID: ${TenantId}" -ForegroundColor Cyan
        }
        
        # Set context to the subscription, explicitly providing the tenant ID
        $subContext = Set-AzContext -Subscription $SubscriptionId -TenantId $TenantId -ErrorAction Stop
        
        Write-Host "Successfully accessed customer subscription!" -ForegroundColor Green
        Write-Host "Subscription: $($subContext.Subscription.Name) [ID: $($subContext.Subscription.Id)]" -ForegroundColor Green
        Write-Host "Tenant: $($subContext.Tenant.Id)" -ForegroundColor Green
        
        return $subContext
    }
    catch {
        Write-Error "Failed to access subscription ${SubscriptionId}: $($_.Exception.Message)"
        return $null
    }
}

# Function to list all accessible customer subscriptions (across all tenants)
function Get-AllAccessibleSubscriptions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ExcludeTenantId = $null
    )
    
    try {
        # List all accessible subscriptions
        Write-Host "Retrieving all accessible subscriptions across all tenants..." -ForegroundColor Cyan
        $allSubs = Get-AzSubscription -ErrorAction Stop
        
        # Filter out the partner tenant if requested
        if ($ExcludeTenantId) {
            $allSubs = $allSubs | Where-Object { $_.TenantId -ne $ExcludeTenantId }
            Write-Host "Found $($allSubs.Count) customer subscriptions (excluding tenant $ExcludeTenantId)" -ForegroundColor Green
        } else {
            Write-Host "Found $($allSubs.Count) accessible subscriptions" -ForegroundColor Green
        }
        
        # Group subscriptions by tenant for better organization
        $subsByTenant = $allSubs | Group-Object -Property TenantId
        
        Write-Host "`nSubscriptions by Tenant:" -ForegroundColor Cyan
        foreach ($tenantGroup in $subsByTenant) {
            Write-Host "Tenant: $($tenantGroup.Name) - $($tenantGroup.Count) subscription(s)" -ForegroundColor Yellow
            $tenantGroup.Group | Select-Object Name, Id | Format-Table
        }
        
        return $allSubs
    }
    catch {
        Write-Error "Failed to get accessible subscriptions: $($_.Exception.Message)"
        return $null
    }
}

# Run enhanced diagnostics for DAP access issues
Write-Host "`n`n=== DAP ACCESS DIAGNOSTIC CHECKS ===" -ForegroundColor Magenta

# Display current connection information
$currentContext = Get-AzContext
Write-Host "Current connection details:" -ForegroundColor Yellow
Write-Host "Account: $($currentContext.Account.Id)" -ForegroundColor White
Write-Host "Tenant: $($currentContext.Tenant.Id)" -ForegroundColor White
Write-Host "Subscription: $($currentContext.Subscription.Name) ($($currentContext.Subscription.Id))" -ForegroundColor White

# Check if current user is in Admin Agents group
Write-Host "`nChecking Admin Agents group membership..." -ForegroundColor Cyan
Write-Host "This requires Azure AD/Entra ID read access in your partner tenant" -ForegroundColor Yellow

# Admin Agent Security Group for DAP access
$adminAgentGroupId = "4e515f36-d3ab-46bb-898e-************"

try {
    # List available subscriptions to understand scope of access
    Write-Host "`nListing all accessible subscriptions..." -ForegroundColor Cyan
    $allSubscriptions = Get-AzSubscription -ErrorAction SilentlyContinue
    Write-Host "Found $($allSubscriptions.Count) accessible subscriptions:" -ForegroundColor Green
    $allSubscriptions | Format-Table Name, Id, TenantId
    
    # Group subscriptions by tenant for better visibility
    $tenantGroups = $allSubscriptions | Group-Object -Property TenantId
    Write-Host "These subscriptions belong to $($tenantGroups.Count) tenant(s)" -ForegroundColor Green
    
    # Check for target customer's subscription
    Write-Host "`nChecking for specific customer subscription..." -ForegroundColor Cyan
    $customerInfo = @{
        TenantId = "98ae562d-ed37-48b0-a9ca-d48df78c56c0"          # Customer Tenant ID
        AzurePlanId = "006a3b3b-3ae2-4d72-df31-43241b9bbbe5"       # Azure Plan ID
        SubscriptionName = "BK - DEV/TEST"                         # Friendly name
        SubscriptionId = "73538796-4166-4ccd-bb7c-0a39306f2290"    # Subscription ID
    }
    
    Write-Host "Target customer details:" -ForegroundColor Yellow
    Write-Host "  Tenant ID: $($customerInfo.TenantId)" -ForegroundColor White
    Write-Host "  Subscription: $($customerInfo.SubscriptionName)" -ForegroundColor White
    Write-Host "  Subscription ID: $($customerInfo.SubscriptionId)" -ForegroundColor White
    
    $customerSub = $allSubscriptions | Where-Object { $_.Id -eq $customerInfo.SubscriptionId }
    
    if ($customerSub) {
        Write-Host "SUCCESS: Target subscription was found in your accessible subscriptions!" -ForegroundColor Green
        Write-Host "Subscription details: $($customerSub.Name) ($($customerSub.Id))" -ForegroundColor Green
        Write-Host "Tenant: $($customerSub.TenantId)" -ForegroundColor Green
        
        # Attempt to access it
        Write-Host "`nAttempting to access customer subscription..." -ForegroundColor Cyan
        $customerContext = Set-AzContext -Subscription $customerInfo.SubscriptionId -TenantId $customerInfo.TenantId -ErrorAction Stop
        
        Write-Host "Successfully set context to customer subscription!" -ForegroundColor Green
        Write-Host "Listing resource groups as verification..." -ForegroundColor Cyan
        
        $resourceGroups = Get-AzResourceGroup -ErrorAction SilentlyContinue
        if ($resourceGroups) {
            Write-Host "SUCCESS: Found $($resourceGroups.Count) resource groups in customer subscription!" -ForegroundColor Green
            $resourceGroups | Select-Object ResourceGroupName, Location | Format-Table
        } 
        else {
            Write-Host "No resource groups found in the customer subscription." -ForegroundColor Yellow
            Write-Host "This could mean the subscription is empty or you have insufficient permissions." -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "ERROR: Customer subscription not found in your accessible subscriptions!" -ForegroundColor Red
        Write-Host "This indicates that DAP (Delegated Admin Privileges) is not properly configured." -ForegroundColor Red
        
        Write-Host "`nTroubleshooting steps:" -ForegroundColor Yellow
        Write-Host "1. Verify DAP relationship in Partner Center" -ForegroundColor White
        Write-Host "   - Log in to Partner Center (https://partner.microsoft.com)" -ForegroundColor White
        Write-Host "   - Go to 'Customers' > select the customer > 'Service management' > 'Microsoft Azure'" -ForegroundColor White
        Write-Host "   - Ensure 'Admin on behalf of' privileges are requested and granted" -ForegroundColor White
        
        Write-Host "`n2. Verify your account has proper permissions" -ForegroundColor White
        Write-Host "   - Your user account must be a member of the 'Admin Agents' group (ID: $adminAgentGroupId)" -ForegroundColor White
        Write-Host "   - This is required for DAP access to work properly" -ForegroundColor White
        
        Write-Host "`n3. Try Partner Center PowerShell for more detailed diagnostics" -ForegroundColor White
        Write-Host "   - Install-Module -Name PartnerCenter" -ForegroundColor White
        Write-Host "   - Connect-PartnerCenter" -ForegroundColor White
        Write-Host "   - Get-PartnerCustomer | Where-Object {$_.Name -like '*name of customer*'}" -ForegroundColor White
        Write-Host "   - Get-PartnerCustomerSubscription -CustomerId <ID>" -ForegroundColor White
    }
    
    # Additional DAP reference guide
    Write-Host "`nDAP Requirements Reference:" -ForegroundColor Magenta
    Write-Host "1. Partner organization must have Partner Center access" -ForegroundColor White
    Write-Host "2. Partner must have an established relationship with customer (customer must accept invitation)" -ForegroundColor White
    Write-Host "3. User must be a member of Admin Agents group in partner tenant" -ForegroundColor White
    Write-Host "4. Customer must have granted Administrative Access to their Azure resources" -ForegroundColor White
}
catch {
    Write-Host "Error during DAP diagnostics: $($_.Exception.Message)" -ForegroundColor Red
}

# If we can access the subscription, perform some operations
if ($customerContext) {
    # List resource groups in the subscription
    Write-Host "`nListing resource groups in the customer subscription:" -ForegroundColor Cyan
    $resourceGroups = Get-AzResourceGroup -ErrorAction SilentlyContinue
    
    if ($resourceGroups) {
        Write-Host "Found $($resourceGroups.Count) resource groups:" -ForegroundColor Green
        $resourceGroups | Select-Object ResourceGroupName, Location | Format-Table
    } else {
        Write-Host "No resource groups found or no access to view them." -ForegroundColor Yellow
    }
    
    # Check role assignments
    Write-Host "`nChecking role assignments for Admin Agent Group:" -ForegroundColor Cyan
    $roleAssignments = Get-AzRoleAssignment -ObjectId $adminAgentGroupId -Scope "/subscriptions/$($customerInfo.SubscriptionId)" -ErrorAction SilentlyContinue
    
    if ($roleAssignments) {
        Write-Host "Found role assignments for Admin Agent Group:" -ForegroundColor Green
        $roleAssignments | Select-Object RoleDefinitionName, Scope | Format-Table
    }
    else {
        Write-Host "No direct role assignments found for Admin Agent Group." -ForegroundColor Yellow
        Write-Host "Access may be through inherited permissions or other means." -ForegroundColor Yellow
    }
}

# Demo: Working with multiple customer subscriptions
Write-Host "`n`n=== WORKING WITH MULTIPLE CUSTOMER SUBSCRIPTIONS ===" -ForegroundColor Magenta

# Define the partner tenant ID to exclude from customer lists
$partnerTenantId = "ad926c03-4b20-4cfa-9353-f896a61b0d25"

# Get all accessible customer subscriptions (excluding partner tenant)
$customerSubscriptions = Get-AllAccessibleSubscriptions -ExcludeTenantId $partnerTenantId

# Process multiple customer subscriptions
if ($customerSubscriptions -and $customerSubscriptions.Count -gt 0) {
    Write-Host "`nDemonstrating how to iterate through multiple customer subscriptions:" -ForegroundColor Cyan
    
    # For demo purposes, we'll process up to 3 subscriptions
    $subscriptionsToProcess = $customerSubscriptions | Select-Object -First 3
    
    Write-Host "Will process the following customer subscriptions:" -ForegroundColor Yellow
    $subscriptionsToProcess | Select-Object Name, Id, TenantId | Format-Table
    
    # Example of batch processing subscriptions
    foreach ($subscription in $subscriptionsToProcess) {
        Write-Host "`nProcessing subscription: $($subscription.Name) [$($subscription.Id)]" -ForegroundColor Yellow
        Write-Host "Customer tenant: $($subscription.TenantId)" -ForegroundColor White
        
        # Set context to this subscription
        $subContext = Set-AzContext -Subscription $subscription.Id -TenantId $subscription.TenantId -ErrorAction SilentlyContinue
        
        if ($subContext) {
            # Example operation: Get basic subscription details
            Write-Host "Successfully switched to subscription" -ForegroundColor Green
            
            # Example: Count resources by type (lightweight operation)
            $resourceSummary = Get-AzResource -ErrorAction SilentlyContinue | 
                Group-Object -Property ResourceType -NoElement | 
                Sort-Object -Property Count -Descending
                
            if ($resourceSummary) {
                Write-Host "Resource summary:" -ForegroundColor Cyan
                $resourceSummary | Select-Object Name, Count | Format-Table
            }
            else {
                Write-Host "No resources found or no access to view them." -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "Failed to access subscription $($subscription.Id)" -ForegroundColor Red
        }
    }
}
else {
    Write-Host "No customer subscriptions found. This could indicate:" -ForegroundColor Red
    Write-Host "1. Your user account doesn't have Delegated Admin Privileges (DAP)" -ForegroundColor Yellow
    Write-Host "2. You aren't a member of the Admin Agents group in your partner tenant" -ForegroundColor Yellow
    Write-Host "3. Your organization hasn't established DAP relationships with any customers" -ForegroundColor Yellow
}

# Partner Center connection parameters (if needed)
$partnerSplat = @{
    ApplicationId        = $clientId
    Credential           = $credential
    TenantId             = $tenantId
    Scopes               = @("https://api.partnercenter.microsoft.com/user_impersonation", "https://management.azure.com/user_impersonation")
    ServicePrincipal     = $true
    UseAuthorizationCode = $true
}

# If you need to use Partner Center API, uncomment the following line
#$token = New-PartnerAccessToken @partnerSplat

# Set a default subscription if needed
# Select-AzSubscription -SubscriptionId "your-subscription-id"