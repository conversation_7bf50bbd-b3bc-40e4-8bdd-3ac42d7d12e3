{"openapi": "3.0.1", "info": {"title": "Cloud Factory: Utility Portal API", "description": "**Cloud Factory: Utility Portal API** can be used to integerate core functionality in your own applications or websites, by directly callin our API. <br/> **Note**: Most features require the user to be a customer of ours and needs to be logged-in.", "contact": {"name": "Cloud Factory", "url": "https://cloudfactory.dk", "email": "<EMAIL>"}, "version": "v1", "x-logo": {"url": "https://assets.cloudfactory.dk/public/cf_redoc_logo.png", "backgroundColor": "#FFFFFF", "altText": "Cloud Factory"}}, "paths": {"/Accounts/partner": {"get": {"tags": ["Accounts"], "summary": "Get partner accounts from Auth0 paginated", "parameters": [{"name": "PageIndex", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "required": true, "schema": {"maximum": 250, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "Filter.IncludeTotals", "in": "query", "schema": {"type": "boolean"}}, {"name": "Filter.PartnerGuid", "in": "query", "schema": {"type": "string", "format": "uuid", "nullable": true}}, {"name": "Filter.Name", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Filter.UserId", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Filter.Email", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Filter.Operator", "in": "query", "schema": {"$ref": "#/components/schemas/AuthService.Models.AccountsFilterOperator"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CF.Pagination.PageResponse`1[[ViewModels.Services.Auth.VM_Account, ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Accounts/customer": {"get": {"tags": ["Accounts"], "summary": "Get customer accounts from Auth0 paginated", "parameters": [{"name": "PageIndex", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "required": true, "schema": {"maximum": 250, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "Filter.IncludeTotals", "in": "query", "schema": {"type": "boolean"}}, {"name": "Filter.PartnerGuid", "in": "query", "schema": {"type": "string", "format": "uuid", "nullable": true}}, {"name": "Filter.Name", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Filter.UserId", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Filter.Email", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Filter.Operator", "in": "query", "schema": {"$ref": "#/components/schemas/AuthService.Models.AccountsFilterOperator"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CF.Pagination.PageResponse`1[[ViewModels.Services.Auth.VM_Account, ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Alert": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get all alerts", "description": "Returns all alerts.", "responses": {"200": {"description": "Returns all alerts", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Interfaces.Models.IAlert"}}}}}}, "security": [{"bearer": []}]}, "post": {"tags": ["<PERSON><PERSON>"], "summary": "Post new alert", "description": "Posts a new alert", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}}}, "responses": {"200": {"description": "Returns id for the newly created alert", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}, "put": {"tags": ["<PERSON><PERSON>"], "summary": "Update alert", "description": "Updates an alert", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.Alert"}}}}, "responses": {"200": {"description": "Successfully updated alert"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Alert/Debug": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get debug information", "description": "Returns the count for connected users.", "responses": {"200": {"description": "Returns the count of connected users", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Alert/{id}": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "Deletes an alert", "description": "Delete an alert from an ID.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"200": {"description": "Successfully deleted alert"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Authenticate": {"get": {"tags": ["Authenticate"], "summary": "Check if logged in.", "responses": {"200": {"description": "Successfully authenticated user"}, "401": {"description": "User is NOT Authenticated or Error in Authentication", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Authenticate/Login": {"get": {"tags": ["Authenticate"], "summary": "Get refresh token", "description": "Using this login, you will generate a refresh token.\r\nBE AWARE: This refresh token is a representation of your credentials\r\nIT SHOULD BE KEPT PRIVATE\r\nThe Audit logs DOES NOT DIFFERNCIATE BETWEEN YOUR LOGIN\r\nAND THE LOGIN GENERATED FROM A REFRESH TOKEN.\r\nIf your token is compromised, ensure to take appropriate steps in securing your account\r\nincluding but not limited to:\r\n1. Revoking compromised refresh token.\r\n2. Get an overview of the severity. Who got a hold of the token, what actions did they take.\r\n3. Contacting CF support, so we can log the incident as well, and be aware of the circomstances incase we audit the logs.\r\n\r\nIf you wish to have a service account created, please contact support.", "parameters": [{"name": "customer", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Returns URL to login with", "content": {"application/json": {"schema": {"type": "string"}}}}}, "security": [{"bearer": []}]}}, "/Authenticate/Token": {"get": {"tags": ["Authenticate"], "summary": "Get refresh token with redirect", "description": "Using this login, you will generate a refresh token.\r\nBE AWARE: This refresh token is a representation of your credentials\r\nIT SHOULD BE KEPT PRIVATE\r\nThe Audit logs DOES NOT DIFFERNCIATE BETWEEN YOUR LOGIN\r\nAND THE LOGIN GENERATED FROM A REFRESH TOKEN.\r\nIf your token is compromised, ensure to take appropriate steps in securing your account\r\nincluding but not limited to:\r\n1. Revoking compromised refresh token.\r\n2. Get an overview of the severity. Who got a hold of the token, what actions did they take (We can help investigate this).\r\n3. Contacting CF support, so we can log the incident as well, and be aware of the circomstances incase we audit the logs.\r\n\r\nIf you wish to have a service account created, please contact support.\r\n\r\nYou will be automatically redirected to this endpoint, after completing the login flow, from the URL generated at the login endpoint.", "parameters": [{"name": "code", "in": "query", "description": "Authentication code generated from login URL", "schema": {"type": "string", "description": "Authentication code generated from login URL", "nullable": true}}, {"name": "customer", "in": "query", "description": "For MineLicenser Tokens", "schema": {"type": "boolean", "description": "For MineLicenser Tokens", "default": false}}], "responses": {"200": {"description": "Returns redirect URL", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Authenticate/ExchangeRefreshToken/{refreshtoken}": {"get": {"tags": ["Authenticate"], "summary": "Get token from refresh", "description": "Returns the token from a refresh token", "parameters": [{"name": "refreshtoken", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}, {"name": "customer", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Returns the token from a refresh token", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "string"}}}}, "504": {"description": "Server Error", "content": {"application/json": {"schema": {"type": "string"}}}}, "502": {"description": "Server Error", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"type": "string"}}}}}, "security": [{"bearer": []}]}}, "/Authenticate/RevokeToken/{refreshtoken}": {"get": {"tags": ["Authenticate"], "summary": "Revoke token", "description": "If you no longer need a token, please revoke it here, to prevent issues in the future.\r\nIf your key has been compromised, please take a minute to evaluate the severity of the issue,\r\nand get a hold of Cloud Factory support, so we can log the incident as well, incase of audit.", "parameters": [{"name": "refreshtoken", "in": "path", "description": "Token to be revoked", "required": true, "schema": {"type": "string", "description": "Token to be revoked", "nullable": true}}, {"name": "isCustomer", "in": "query", "description": "Token to be revoked", "schema": {"type": "boolean", "description": "Token to be revoked", "default": false}}], "responses": {"200": {"description": "Success"}, "204": {"description": "Operation completed without errors. If the refresh-token was valid, it was revoked"}}, "security": [{"bearer": []}]}}, "/Authenticate/RevokeAllTokens": {"get": {"tags": ["Authenticate"], "summary": "Revoke all tokens", "description": "If you need to remove all your tokens, you can do so here.\r\nThis is a scheduled operation, and might take some time to complete.\r\nYou will get a response once your scheduled operation has been started. but not when it has been completed.", "parameters": [{"name": "isCustomer", "in": "query", "description": "Token to be revoked", "schema": {"type": "boolean", "description": "Token to be revoked", "default": false}}], "responses": {"202": {"description": "Success"}, "500": {"description": "Server Error"}, "204": {"description": "Operation completed without errors. All refresh tokens has been revoked"}}, "security": [{"bearer": []}]}}, "/Authenticate/Roles": {"get": {"tags": ["Authenticate"], "summary": "Get roles and permissions", "description": "Returns a list with all roles and permissions.", "responses": {"200": {"description": "Returns a list of roles and permissions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Role"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Authenticate/Customer": {"get": {"tags": ["Authenticate"], "summary": "Get partner profile", "description": "Get the Partner Profile of Logged In Entity", "responses": {"200": {"description": "Returns the partner profile for logged-in entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Partner"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Azure/setOwner/{endCustomerId}": {"put": {"tags": ["Azure"], "summary": "Set end-user as subscription owner", "description": "Sets a user as the owner of a subscription based on supplied customer id, subscription id and user id.\r\nReturns the object of updated subscription.", "parameters": [{"name": "endCustomerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "subscriptionId", "in": "query", "description": "", "required": true, "schema": {"type": "string", "description": ""}}, {"name": "userId", "in": "query", "description": "", "required": true, "schema": {"type": "string", "description": ""}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the updated subscription", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.AzureSetOwnerPropertiesResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Azure/RenameAzureSubscription/{endCustomerId}": {"post": {"tags": ["Azure"], "summary": "Update Azure subscription name", "description": "Updates the Azure subscription name and returns the subscription id when completed.", "parameters": [{"name": "endCustomerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "subscriptionId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}, {"name": "newSubscriptionName", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns id of the updated Azure subscription", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.SubscriptionRenameId"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Azure/GetAllUsersOnASubscription": {"get": {"tags": ["Azure"], "summary": "Get all subscription users", "description": "Returns all users on a subscription from supplied subscription id.", "parameters": [{"name": "endCustomerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "subscriptionId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all subscription users.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.GetAllUsersOnASubscription"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Azure/RemoveOwner": {"delete": {"tags": ["Azure"], "summary": "Remove subscription ownership", "description": "Removes an user as owner for a subscription from suplied subscription id and customer id.", "parameters": [{"name": "endCustomerId", "in": "query", "description": "CF Id of the end customer", "required": true, "schema": {"type": "integer", "description": "CF Id of the end customer", "format": "int32"}}, {"name": "subscriptionId", "in": "query", "description": "Microsoft GUID for the subscription", "required": true, "schema": {"type": "string", "description": "Microsoft GUID for the subscription"}}, {"name": "userId", "in": "query", "description": "Microsoft Resouce GUID", "required": true, "schema": {"type": "string", "description": "Microsoft Resouce GUID"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns updated subscription.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.AzureSetOwnerPropertiesResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Azure/CreateSubscription": {"post": {"tags": ["Azure"], "summary": "Create subscription", "description": "Creates a new subscription for a partner from supplied customer id and subscription DTO.", "parameters": [{"name": "endCustomerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.RequestModel.CreateSubscription.CreateSubscriptionDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.RequestModel.CreateSubscription.CreateSubscriptionDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.RequestModel.CreateSubscription.CreateSubscriptionDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.RequestModel.CreateSubscription.CreateSubscriptionDTO"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created subscription", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.CreatedSubscriptionResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Azure/GetCustomerResellers": {"get": {"tags": ["Azure"], "summary": "Get customer resellers", "description": "Returns all the customers resellers from supplied customer id.", "parameters": [{"name": "endCustomerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns customer resellers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.AzureGetCustomersModels.Customers"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/AzureSupport/GetAllSupportServiceIssues": {"get": {"tags": ["AzureSupport"], "summary": "Get support servies", "description": "Get the issues under technical support issues from the azure support API\r\nRemember to save your desired issues legcayId and name for further calls", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all the support services", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.AzureSupport.IssueTypeDTO"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/AzureSupport/GetAllproblemsFromYourIssue": {"get": {"tags": ["AzureSupport"], "summary": "Get problems", "description": "Get the problems under a issue with its id\r\nYou need the legacy id for the desired probelm or sub problem for further calls", "parameters": [{"name": "IssueID", "in": "query", "description": "example = 484e2236-bc6d-b1bb-76d2-7d09278cf9ea", "schema": {"type": "string", "description": "example = 484e2236-bc6d-b1bb-76d2-7d09278cf9ea", "nullable": true}}, {"name": "subscriptionId", "in": "query", "description": "example = 2986e187-0a26-4322-b36c-4094cca7efec", "schema": {"type": "string", "description": "example = 2986e187-0a26-4322-b36c-4094cca7efec", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all support problems", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.AzureSupport.SupportProblemDTO"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/AzureSupport/GetDetailFormsFormYourProblem": {"get": {"tags": ["AzureSupport"], "summary": "Get issue details", "description": "Get the special formfields from a issue and problem\r\nThis will give you the form fields for rendering a details page", "parameters": [{"name": "issuelegacyId", "in": "query", "description": "example =16251", "schema": {"type": "string", "description": "example =16251", "nullable": true}}, {"name": "problemlegacyId", "in": "query", "description": "example =32684688", "schema": {"type": "string", "description": "example =32684688", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns support form details", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.AzureSupport.SupportFormElementDTO"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/AzureSupport/Write Azure Support Ticket ": {"post": {"tags": ["AzureSupport"], "summary": "Write ticket", "description": "Get the special formfields from a issue and problem\r\nThis will give you the form fields for rendering a details page", "parameters": [{"name": "domainName", "in": "query", "description": "example =vejenautodanmark", "schema": {"type": "string", "description": "example =vejenautodanmark", "nullable": true}}], "requestBody": {"description": "example =32684688", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DTO.AzureSupport.AzureTicketInfoDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DTO.AzureSupport.AzureTicketInfoDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DTO.AzureSupport.AzureTicketInfoDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DTO.AzureSupport.AzureTicketInfoDTO"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the status for written support ticket", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Cache/microsoft/{partnerId}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Flush microsoft tenants cache", "description": "Clear microsoft Tenants Cache. Usefull when about to import or accepting customers.", "parameters": [{"name": "partnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns true for successful operation", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "security": [{"bearer": []}]}}, "/Category": {"get": {"tags": ["Category"], "summary": "Get all Categories", "description": "Returns a list of all categories associated with the partner", "parameters": [{"name": "partnerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of all categories", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Category"}}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "post": {"tags": ["Category"], "summary": "Create new category", "description": "Creates a new category from supplied object and returns the newly created category.", "requestBody": {"description": "Category model to create", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateCategory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateCategory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateCategory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateCategory"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created category", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Category"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Category/{id}": {"get": {"tags": ["Category"], "summary": "Get category", "description": "Returns a specific category from supplied category id.", "parameters": [{"name": "id", "in": "path", "description": "Returns specific category", "required": true, "schema": {"type": "integer", "description": "Returns specific category", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "The category of the specified id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Category"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["Category"], "summary": "Update category name", "description": "Updates an excisting category with a new name from supplied id and object.", "parameters": [{"name": "id", "in": "path", "description": "Id of the category to", "required": true, "schema": {"type": "integer", "description": "Id of the category to", "format": "int32"}}], "requestBody": {"description": "Category Model to update (inc. id)", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateCategory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateCategory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateCategory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateCategory"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully updated category name"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["Category"], "summary": "Delete category", "description": "Deletes a category from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted category"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/CMS/{id}": {"get": {"tags": ["CMS"], "summary": "Get CMS", "description": "Returns the CMS from the supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns specific CMS", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_ContentPage"}}}}}, "security": [{"bearer": []}]}}, "/Data/Countries": {"get": {"tags": ["Data"], "summary": "Get country codes", "description": "Returns a list of all contries and their country codes.", "responses": {"200": {"description": "Returns list of contry and its country-code", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Data.VM_Country"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/EndCustomer": {"get": {"tags": ["EndCustomer"], "summary": "Get end customers", "description": "Returns all end customers from supplied partner id.", "parameters": [{"name": "partnerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all end customers", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_EndCustomer"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "post": {"tags": ["EndCustomer"], "summary": "Create new end customer", "description": "Creates a new customer from supplied object.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateEndCustomer"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateEndCustomer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateEndCustomer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateEndCustomer"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created end customer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_EndCustomer"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/EndCustomer/{id}": {"get": {"tags": ["EndCustomer"], "summary": "Get end customer", "description": "Returns a specific end customer from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns end customer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_EndCustomer"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["EndCustomer"], "summary": "Update end customer", "description": "Update end customer information from supplied end customer id and object.\r\nInputting CVR will override relevant information.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateEndCustomer"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateEndCustomer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateEndCustomer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateEndCustomer"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully updated end customer"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["EndCustomer"], "summary": "Delete end customer", "description": "Deletes an end customer from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted end customer"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Log/v2": {"get": {"tags": ["Log"], "summary": "Get logs v2", "description": "Get logs uses keyset pagination.\r\nwhat page we traverse to should be determined frontend. log id is required to traverse.\r\nfirst log id from the current page to return to previous page,  last id of current page when going next page.\r\nIncrease pagesize while staying on current page by giving first id of current page and leaving nextpage empty", "parameters": [{"name": "CurrentPage", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "NextPage", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Parameters.Id", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "Parameters.PartnerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "Parameters.CustomerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "Parameters.Method", "in": "query", "schema": {"type": "array", "items": {"type": "string"}, "nullable": true}}, {"name": "Parameters.Path", "in": "query", "schema": {"type": "array", "items": {"type": "string"}, "nullable": true}}, {"name": "Parameters.AuthId", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Parameters.StatusCode", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Parameters.From", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Parameters.To", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns logs associated with input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PaginationResponse`1[[ViewModels.Services.Log.VM_LogRequest[], ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}, "security": [{"bearer": []}]}}, "/Log/v1": {"get": {"tags": ["Log"], "summary": "Get logs v1", "description": "Get logs uses keyset pagination.\r\nWhat page we traverse to should be determined frontend. Log id is required to traverse.\r\nFirst log id from the current page to return to previous page, last id of current page when going next page.\r\nIncrease pagesize while staying on current page by giving first id of current page and leaving nextpage empty", "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Parameters.PartnerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "Parameters.CustomerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "Parameters.Method", "in": "query", "schema": {"type": "array", "items": {"type": "string"}, "nullable": true}}, {"name": "Parameters.Path", "in": "query", "schema": {"type": "array", "items": {"type": "string"}, "nullable": true}}, {"name": "Parameters.AuthId", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Parameters.StatusCode", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "Parameters.From", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Parameters.To", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns logs associated with input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PaginationResponse`1[[ViewModels.Services.Log.VM_LogRequest[], ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}, "security": [{"bearer": []}]}}, "/Microsoft/Tenants": {"get": {"tags": ["Microsoft"], "summary": "Get Tenants", "description": "Get Subscriptions of currently signed-in partner. \r\nThis Endpoint is in development and may change significantly.", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the tenants for a partner", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Tenants/{partnerId}": {"get": {"tags": ["Microsoft"], "summary": "Get partner subscriptions", "description": "Get Subscriptions of a partner, by supplied partner Id.", "parameters": [{"name": "partnerId", "in": "path", "description": "Id of partner", "required": true, "schema": {"type": "integer", "description": "Id of partner", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the partner subscriptions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Tenant/{customerId}": {"get": {"tags": ["Microsoft"], "summary": "Get customers tenant", "description": "Get a customer's tenant with seats from supplied customer id.", "parameters": [{"name": "customerId", "in": "path", "description": "Id of customer", "required": true, "schema": {"type": "integer", "description": "Id of customer", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the customers tenants", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Tenant/{endCustomerId}/MCA": {"post": {"tags": ["Microsoft"], "summary": "Post customer MCA", "description": "Posts the customer MCA for the supplied customer id and signed MCA object.", "parameters": [{"name": "endCustomerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.SignMCA"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.SignMCA"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.SignMCA"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.SignMCA"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully posted MCA", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "get": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Tenant/{endCustomerId}/Health": {"get": {"tags": ["Microsoft"], "summary": "Get tenant health status", "description": "Returns the health of the tenant for supplied end customer id.\r\n'mcaNotSigned': The Microsoft Customer Agreement is not signed\r\n'billToMissing': There are errors in the billing information\r\n'invalidRelationship': The relationship is invalid between Cloud Factory and the Tenant\r\n'tenantNotAttached': There is not a tenant attached to this customer\r\n'partnerCenterException': An unknown error occured with connection to the partner center.", "parameters": [{"name": "endCustomerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns strings: ['mcaNotSigned', 'billToMissing', 'invalidRelationship', 'tenantNotAttached', 'partnerCenterException']", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Partner/{partnerId}/Health": {"get": {"tags": ["Microsoft"], "summary": "Get partner health status", "description": "Returns the health of a partner for supplied partner id.", "parameters": [{"name": "partnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the partners health status", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/System.ValueTuple`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Microsoft/Seats/{endCustomerId}": {"get": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "post": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "acceptedTOS", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Orders.Order"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Entitlements/{endCustomerId}": {"get": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Seat/{endCustomerId}/Quantity": {"patch": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "acceptedTOS", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Seat/{endCustomerId}/Status/Suspend": {"patch": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "acceptedTOS", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Seat/{endCustomerId}/Status/Activate": {"patch": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "acceptedTOS", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/SeatStatus": {"get": {"tags": ["Microsoft"], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Products": {"get": {"tags": ["Microsoft"], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.ProductAPI"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Seat/{endCustomerId}/Addon/{parentSeatId}": {"post": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "parentSeatId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "acceptedTOS", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_AddMicrosoftSeat"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Seat/{endCustomerId}/{subscriptionId}": {"get": {"tags": ["Microsoft"], "parameters": [{"name": "endCustomerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Tenant": {"post": {"tags": ["Microsoft"], "summary": "Create tenant", "description": "Create new tenant from supplied tenant object.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_CreateTenant"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_CreateTenant"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_CreateTenant"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_CreateTenant"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created tenant", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Interfaces.ITenant"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Tenant/Billing-Profiles": {"put": {"tags": ["Microsoft"], "summary": "Update billing info for a list of tenants. Will run whole collection parsed in to end.\r\n            \r\nNOTE: Does not stop on errors. Errors happened during a update will be logged and the process wil continue to next tenant update", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_UpdateBillingInfo"}, "description": "", "nullable": true}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_UpdateBillingInfo"}, "description": "", "nullable": true}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_UpdateBillingInfo"}, "description": "", "nullable": true}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_UpdateBillingInfo"}, "description": "", "nullable": true}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/DomainAvailable/{domainPrefix}": {"get": {"tags": ["Microsoft"], "summary": "Check domain prefix", "description": "Checks if the domain is available from supplied domainPrefix string.", "parameters": [{"name": "domainPrefix", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns true if the domain prefix is available", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/CountryValidationInformation/{countryCode}": {"get": {"tags": ["Microsoft"], "summary": "Get the Validation information that will be used to process the Address of creating a tenant", "parameters": [{"name": "countryCode", "in": "path", "description": "ISO 3166 alpha-2 Country Code", "required": true, "schema": {"type": "string", "description": "ISO 3166 alpha-2 Country Code", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Country Validation Rules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.CountryValidationRules.CountryValidationRules"}}}}, "400": {"description": "Country Validation Rules", "content": {"application/json": {"schema": {"type": "string"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Users/<USER>/Available": {"get": {"tags": ["Microsoft"], "summary": "Get customers available licenses", "description": "Returns a customers available licenses from the supplied customer id.", "parameters": [{"name": "endCustomerId", "in": "path", "description": "Customer Id", "required": true, "schema": {"type": "integer", "description": "Customer Id", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns available customer licenses", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_SubscribedSku"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/Users/<USER>/Assigned": {"get": {"tags": ["Microsoft"], "summary": "Get users and assigned licenses", "description": "Returns a customers users and their assigned licenses as a list from supplied end customer id.", "parameters": [{"name": "endCustomerId", "in": "path", "description": "Customer Id", "required": true, "schema": {"type": "integer", "description": "Customer Id", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the user and the assigned licenses", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_CustomerUser"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Microsoft/CreateUser": {"post": {"tags": ["Microsoft"], "summary": "Create customer for user", "description": "Creates an user for an excisting customer from supplied customer id and customer user object.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphCreateUserRequestDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphCreateUserRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphCreateUserRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphCreateUserRequestDTO"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "201": {"description": "Returns the newly created customer user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphCreateUserResponseDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetOneUserOnTenant": {"get": {"tags": ["Microsoft"], "summary": "Get tenant user", "description": "Returns the tenants user from supplied customer id and user id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userID", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the user for a tenant", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetAllUsersOnCustomer": {"get": {"tags": ["Microsoft"], "summary": "Get all users", "description": "Returns all customers for a user from supplied customer id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.CustomCustomerUser"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetAllUsersOnCustomerSimplified": {"get": {"tags": ["Microsoft"], "summary": "Get all users (but less data)", "description": "Returns all customers for a user from supplied customer id. Does not include job title, phone number, etc", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.CustomCustomerUser"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/DeleteUserOnCustomer": {"delete": {"tags": ["Microsoft"], "summary": "Delete user", "description": "Deletes a user on a customer from supplied customer id and user id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userID", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "202": {"description": "Successfully deleted user"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/UpdateUserOnCustomer": {"patch": {"tags": ["Microsoft"], "summary": "Update customer user", "description": "Updates the customer user from supplied customer id, user id and customer user object.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userID", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the updated customer user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetListOfAllAvailableLicensesOnCustomer": {"get": {"tags": ["Microsoft"], "summary": "Get available licenses", "description": "Returns a list of all availabe licenses for a customer from supplied customer id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all available licenses", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.GetAllAvailableLicensesOnCustomer"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetAllAvailableLicensesOnCustomerWithPrerequisiteSkus": {"get": {"tags": ["Microsoft"], "summary": "Get available licenses for skus", "description": "Returns all available licenses on a customer with prerequisite skus from supplied customer id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the available licenses for customer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.GetAllAvailableLicensesOnCustomerWithPrerequisiteSkus"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/AssignLicensesToUser": {"post": {"tags": ["Microsoft"], "summary": "Add license to user", "description": "Assign a license to a user from supplied customer id, user id and list of license assignement object.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userID", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseAssignment"}, "description": "", "nullable": true}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseAssignment"}, "description": "", "nullable": true}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseAssignment"}, "description": "", "nullable": true}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseAssignment"}, "description": "", "nullable": true}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the updated licenses for a user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseUpdate"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/RemoveLicensesFromUser": {"post": {"tags": ["Microsoft"], "summary": "Delete licenses for user", "description": "Removes a list of licenses you want removed from a user from supplied customer id, user id and list of licenses to remove.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userID", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "string = skuId.", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "string = skuId.", "nullable": true}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "string = skuId.", "nullable": true}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "string = skuId.", "nullable": true}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "string = skuId.", "nullable": true}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the licenses that was deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseUpdate"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/UserOnCustomerResetPassword": {"patch": {"tags": ["Microsoft"], "summary": "Reset password for user", "description": "Reset password for user from supplied customer id, user id and password profile object", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userID", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.PasswordProfile"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.PasswordProfile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.PasswordProfile"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.PasswordProfile"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the user which has password reseted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.CustomerUser"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetAzureSubscriptions": {"get": {"tags": ["Microsoft"], "summary": "Get Azure subsriptions", "description": "Return a list of Azure entitlements for subscriptions from supplied customer id and subscription id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "subscriptionId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list with Azure subscriptions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseUpdate"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetAllAssignedLicensesOnAUser": {"get": {"tags": ["Microsoft"], "summary": "Get all licenses for user", "description": "Returns all assigned liceses for a user from supplied customer id and user id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list with all licenses for a user", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.GetAllAssignedLicensesOnAUser"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/GetCustomerDomains": {"get": {"tags": ["Microsoft"], "summary": "Get customer domains", "description": "Returns a list of customers domains.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns customer domains", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/{customerId}/DeletedUsers": {"get": {"tags": ["Microsoft"], "summary": "Get deleted users", "description": "Returns a list of all deleted users from supplied customer id.", "parameters": [{"name": "customerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all deleted user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.PartnerCenterCustomerUserDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/{customerId}/Users/<USER>/Restore": {"patch": {"tags": ["Microsoft"], "summary": "Restore user", "description": "Restores user for a certain customer from supplied customer id and id.", "parameters": [{"name": "customerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "Id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the restored user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.PartnerCenterRestoredCustomerUserDTO"}}}}, "400": {"description": "Returns Bad Request if the input is not correct.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftGraph/GetUser": {"get": {"tags": ["MicrosoftGraph"], "summary": "Get graph user", "description": "Returns the graph user from supplied customer and user id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns graph user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphUserDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftGraph/UpdateGraphUser": {"put": {"tags": ["MicrosoftGraph"], "summary": "Update graph user", "description": "Updates a graph user with supplied id and a new user object.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.VM_GraphUpdateUser"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.VM_GraphUpdateUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.VM_GraphUpdateUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.VM_GraphUpdateUser"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created graph user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphUserDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftGraph/GetGraphSecurityScore": {"get": {"tags": ["MicrosoftGraph"], "summary": "Get graph security score", "description": "Returns the graph security score for supplied customer id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns graph security score", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphSecurityScoreDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftGraph/LicensesDetails": {"get": {"tags": ["MicrosoftGraph"], "summary": "Get license details", "description": "Returns license details for supplied customer id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "userId", "in": "query", "description": "", "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns license details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphLicenseDetailsDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftGraph/MfaStatus": {"get": {"tags": ["MicrosoftGraph"], "summary": "Get MFA status", "description": "Returns the status on a MFA for supplied customer id.", "parameters": [{"name": "customerId", "in": "query", "description": "", "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns MFA status for customer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.GraphMfaStatusOnUsersDTO"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/{tenantId}/nce/convert": {"post": {"tags": ["MicrosoftNce"], "summary": "Queue NCE Conversion", "parameters": [{"name": "tenantId", "in": "path", "description": "Microsoft Tenant Id", "required": true, "schema": {"type": "string", "description": "Microsoft Tenant Id", "nullable": true}}, {"name": "partnerId", "in": "query", "description": "On Behalf of partner", "schema": {"type": "integer", "description": "On Behalf of partner", "format": "int32", "nullable": true}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}}}, "responses": {"204": {"description": "Success"}}, "deprecated": true, "security": [{"bearer": []}]}, "get": {"tags": ["MicrosoftNce"], "summary": "See NCE Conversion Queue", "parameters": [{"name": "tenantId", "in": "path", "description": "Cloud Factory Customer Id", "required": true, "schema": {"type": "string", "description": "Cloud Factory Customer Id", "nullable": true}}, {"name": "partnerId", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "schema": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}}}, "204": {"description": "Success"}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["MicrosoftNce"], "summary": "See NCE Conversion Queue", "parameters": [{"name": "tenantId", "in": "path", "description": "Cloud Factory Customer Id", "required": true, "schema": {"type": "string", "description": "Cloud Factory Customer Id", "nullable": true}}, {"name": "partnerId", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "schema": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}}}, "204": {"description": "Success"}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/{tenantId}/nce/convert2": {"get": {"tags": ["MicrosoftNce"], "summary": "See NCE Conversion Queue", "parameters": [{"name": "tenantId", "in": "path", "description": "Cloud Factory Customer Id", "required": true, "schema": {"type": "string", "description": "Cloud Factory Customer Id", "nullable": true}}, {"name": "partnerId", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "schema": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion"}}}}}, "204": {"description": "Success"}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/nce/subscriptions": {"get": {"tags": ["MicrosoftNce"], "summary": "Get a list of all tenants and subscriptions", "parameters": [{"name": "partnerId", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "schema": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> of partner", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_Tenant"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_Tenant"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_Tenant"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/nce/tenants": {"get": {"tags": ["MicrosoftNce"], "summary": "Get Tenants", "parameters": [{"name": "partnerId", "in": "query", "description": "OnBehalf of Partner", "schema": {"type": "integer", "description": "OnBehalf of Partner", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/nce/tenants/{tenantId}/Seats": {"get": {"tags": ["MicrosoftNce"], "summary": "Get Seats by Tenant Id", "parameters": [{"name": "tenantId", "in": "path", "description": "Microsoft Tenant Id", "required": true, "schema": {"type": "string", "description": "Microsoft Tenant Id", "nullable": true}}, {"name": "partnerId", "in": "query", "description": "OnBehalf of Partner", "schema": {"type": "integer", "description": "OnBehalf of Partner", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/nce/sku/{skuId}/Convertions": {"get": {"tags": ["MicrosoftNce"], "summary": "Recommneded new Products", "description": "Get the list of recomended Cloud Factory Products to replace exisitng microsoft subscription.\r\nReturns empty of no known convertion exists.", "parameters": [{"name": "skuId", "in": "path", "description": "Existing Microsoft SKU", "required": true, "schema": {"type": "string", "description": "Existing Microsoft SKU", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModes.Microsoft.VM_Product"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModes.Microsoft.VM_Product"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModes.Microsoft.VM_Product"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/nce/sku/Convertions": {"get": {"tags": ["MicrosoftNce"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_AllConvertions"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_AllConvertions"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_AllConvertions"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/nce/convertiontable": {"get": {"tags": ["MicrosoftNce"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "post": {"tags": ["MicrosoftNce"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["MicrosoftNce"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Models.MicrosoftSkuConvertion"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Microsoft/{customerId}/nce/provision": {"post": {"tags": ["MicrosoftNce"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "partnerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_ProvisionNew"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_ProvisionNew"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_ProvisionNew"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_ProvisionNew"}}}}, "responses": {"204": {"description": "Success"}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/{partnerGuid}": {"get": {"tags": ["MicrosoftSplaReport"], "summary": "Get the most current SPLA report for a partner.\r\nNOTE: This could be submitted, if the partner has discontinued SPLA reporting, meaning that they have\r\ndeleted a current, un-submitted, report - In that case, the most recent submitted report will be returned here.", "operationId": "Get", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID where the SPLA report should be fetched from", "required": true, "schema": {"type": "string", "description": "Partner GUID where the SPLA report should be fetched from", "format": "uuid", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.SplaReport.VM_SplaReport"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}, "post": {"tags": ["MicrosoftSplaReport"], "summary": "Create or update a SPLA report. The parsed in the model, will be reflected, as is, in the database. If\r\nany customers and products needs to be added to the report, add them to the array. If any customers or products\r\nneeds to be modified or removed, also reflect those changes in the model.", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID where the SPLA report should be fetched from", "required": true, "schema": {"type": "string", "description": "Partner GUID where the SPLA report should be fetched from", "format": "uuid", "nullable": true}}], "requestBody": {"description": "Datamodel that will be reflected - as is - in the database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Interfaces.Models.CreateUpdateSplaReport"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Interfaces.Models.CreateUpdateSplaReport"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Interfaces.Models.CreateUpdateSplaReport"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Interfaces.Models.CreateUpdateSplaReport"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.SplaReport.VM_SplaReport"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}, "delete": {"tags": ["MicrosoftSplaReport"], "summary": "Delete a SPLA report. This will delete the current un-submited report", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID where the SPLA report should be fetched from", "required": true, "schema": {"type": "string", "description": "Partner GUID where the SPLA report should be fetched from", "format": "uuid", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/{partnerGuid}/history": {"get": {"tags": ["MicrosoftSplaReport"], "summary": "Get history of SPLA reports for a partner. This list will contain all submitted SPLA reports.\r\nIf un-submitted SPLA reports should be included, set the Filter.IncludeUnSubmitted to true", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID where the SPLA report should be fetched from", "required": true, "schema": {"type": "string", "description": "Partner GUID where the SPLA report should be fetched from", "format": "uuid", "nullable": true}}, {"name": "PageIndex", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "required": true, "schema": {"maximum": 250, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "Filter.IncludeUnSubmitted", "in": "query", "schema": {"type": "boolean"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CF.Pagination.PageResponse`1[[ViewModels.Services.SplaReport.VM_SplaReport, ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/{partnerGuid}/customers": {"get": {"tags": ["MicrosoftSplaReport"], "summary": "Get a partners customers that has ever been included a spla report before", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID", "required": true, "schema": {"type": "string", "description": "Partner GUID", "format": "uuid", "nullable": true}}, {"name": "includeDeleted", "in": "query", "description": "True = Returns customers that has been deleted from the system. False = Don't include customers that has been deleted", "schema": {"type": "boolean", "description": "True = Returns customers that has been deleted from the system. False = Don't include customers that has been deleted", "default": false}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/partners": {"get": {"tags": ["MicrosoftSplaReport"], "summary": "Get a list of partners with or without a spla report based on the filter parsed in\r\n            \r\nreturns an array with PartnerGuid and PartnerName", "parameters": [{"name": "PartnerQuery", "in": "query", "schema": {"$ref": "#/components/schemas/Interfaces.Models.SplaReportPartnerFilter"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/{partnerGuid}/submit": {"post": {"tags": ["MicrosoftSplaReport"], "summary": "Submit the current SPLA report, that has not been submitted yet. If the report is successfully submitted,\r\nit will automatically create a new current SPLA report.\r\n            \r\nBefore a SPLA report can be submitted, the CanBeSubmitted property on the current SPLA report needs to be true.\r\n            \r\nIt will be false in the following scenarios:\r\n- No current report exists\r\n- A report already has been submitted on the same day\r\n- The previous submitted report already was submitted ahead in time.", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID where the SPLA report should be fetched from", "required": true, "schema": {"type": "string", "description": "Partner GUID where the SPLA report should be fetched from", "format": "uuid"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/{partnerGuid}/submit-day/{day}": {"post": {"tags": ["MicrosoftSplaReport"], "summary": "Set the submitDay of the current SPLA report. On this day every month, the SPLA report will be auto-submitted.", "parameters": [{"name": "partnerGuid", "in": "path", "description": "Partner GUID where the SPLA report should be fetched from", "required": true, "schema": {"type": "string", "description": "Partner GUID where the SPLA report should be fetched from", "format": "uuid"}}, {"name": "day", "in": "path", "description": "a day between 1-31", "required": true, "schema": {"type": "integer", "description": "a day between 1-31", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/billing-report": {"get": {"tags": ["MicrosoftSplaReport"], "summary": "Generate a billing report.\r\nIf month is not specified, it takes the current month and generate billing data submitted for this month.\r\nThis will return a CSV file with the contentType as \"text/csv;charset=UTF-8\".", "parameters": [{"name": "year", "in": "query", "description": "From which year the billing-report should be generated from. Format: YYYY", "schema": {"type": "integer", "description": "From which year the billing-report should be generated from. Format: YYYY", "format": "int32", "nullable": true}}, {"name": "month", "in": "query", "description": "Get a billing report for a specific month. 1 = January, 2 = February etc..", "schema": {"type": "integer", "description": "Get a billing report for a specific month. 1 = January, 2 = February etc..", "format": "int32", "nullable": true}}, {"name": "separator", "in": "query", "description": "Which separator should be used. Standard = ';'", "schema": {"type": "string", "description": "Which separator should be used. Standard = ';'", "default": ";"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSplaReport/billing-report-draft": {"get": {"tags": ["MicrosoftSplaReport"], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/MicrosoftSubscriptionsManagement/{customerId}/Subscription/{subscriptionId}": {"get": {"tags": ["MicrosoftSubscriptionsManagement"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftSubscriptionsManagement/{customerId}/Subscriptions": {"get": {"tags": ["MicrosoftSubscriptionsManagement"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionIds", "in": "query", "schema": {"type": "array", "items": {"type": "string"}, "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftSubscriptionsManagement/{customerId}/Subscription/{subscriptionId}/updateState": {"patch": {"tags": ["MicrosoftSubscriptionsManagement"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.StatusDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.StatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.StatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.StatusDto"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftSubscriptionsManagement/{customerId}/Subscription/{subscriptionId}/ToogleAutoRenew": {"patch": {"tags": ["MicrosoftSubscriptionsManagement"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.AutoRenewDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.AutoRenewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.AutoRenewDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebAPI.ModelDtos.AutoRenewDto"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/MicrosoftSubscriptionsManagement/{customerId}/Subscription/{subscriptionId}/ManageRenewal": {"patch": {"tags": ["MicrosoftSubscriptionsManagement"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Subscriptions.ScheduledNextTermInstructions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Subscriptions.ScheduledNextTermInstructions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Subscriptions.ScheduledNextTermInstructions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Subscriptions.ScheduledNextTermInstructions"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Partners": {"get": {"tags": ["Partners"], "summary": "Get list of partners", "description": "Returns a list of all partners", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns list of all partners", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Partner"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Partners/DefaultQuoteSettings": {"get": {"tags": ["Partners"], "summary": "Get default quote setting", "description": "Get the default settings for a quote header and footer. Note: Base64 Encoding from UTF8", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns default quote settings", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DTO.PartnerBrandingDTO"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Partners/{id}": {"get": {"tags": ["Partners"], "summary": "Get partner by id", "description": "Returns a specified partner from supplied id with all customers for the partner.", "parameters": [{"name": "id", "in": "path", "description": "Id of wanted partner", "required": true, "schema": {"type": "integer", "description": "Id of wanted partner", "format": "int32"}}, {"name": "includeCustomers", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns partner information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Partner"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "The partner was not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["Partners"], "summary": "Update partner information.", "description": "Update a partners information from supplied id and partner object.", "parameters": [{"name": "id", "in": "path", "description": "Id of the partner to update", "required": true, "schema": {"type": "integer", "description": "Id of the partner to update", "format": "int32"}}], "requestBody": {"description": "Updated information", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdatePartner"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdatePartner"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdatePartner"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdatePartner"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Partners/Self": {"get": {"tags": ["Partners"], "summary": "Get current user", "description": "Returns logged-in Partner", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns logged-in partner information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Partner"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Partners/{id}/Logo": {"put": {"tags": ["Partners"], "summary": "Update partner <PERSON><PERSON>", "description": "Updates a partners logo from the supplied id and image.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Logo"], "type": "object", "properties": {"Logo": {"type": "string", "format": "binary"}}}, "encoding": {"Logo": {"style": "form"}}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "204": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "get": {"tags": ["Partners"], "summary": "Get partner logo", "description": "Returns partner logo from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/png": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the partners logo as a file", "content": {"application/png": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}, "application/json": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}}}, "206": {"description": "Success", "content": {"application/png": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}, "application/json": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}}}, "400": {"description": "Bad Request", "content": {"application/png": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/png": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "416": {"description": "<PERSON><PERSON>", "content": {"application/png": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Partners/{id}/TOS": {"put": {"tags": ["Partners"], "summary": "Update partners Terms of service", "description": "Update a partner's Terms of Service from supplied partner id and partner TOS input", "parameters": [{"name": "id", "in": "path", "description": "Id of the partner to update", "required": true, "schema": {"type": "integer", "description": "Id of the partner to update", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["File"], "type": "object", "properties": {"File": {"type": "string", "format": "binary"}}}, "encoding": {"File": {"style": "form"}}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "204": {"description": "Successfully updated partner TOS"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "get": {"tags": ["Partners"], "summary": "Get partner Terms of Service", "description": "Returns a file with a partners Terms of service from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "Id of the partner", "required": true, "schema": {"type": "integer", "description": "Id of the partner", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns partners TOS as a file", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}, "application/json": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}}}, "206": {"description": "Success", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}, "application/json": {"schema": {"$ref": "#/components/schemas/System.IO.File"}}}}, "400": {"description": "Bad Request", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "416": {"description": "<PERSON><PERSON>", "content": {"application/pdf": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Permission/Nodes": {"get": {"tags": ["Permission"], "summary": "Get permission nodes", "description": "Returns all permission nodes", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all permission nodes", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_PermissionNode"}}}}}}, "security": [{"bearer": []}]}}, "/Permission/Roles": {"get": {"tags": ["Permission"], "summary": "Get roles", "description": "Returns a list of all roles", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of all roles", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Role"}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Role/{id}": {"get": {"tags": ["Permission"], "summary": "Get role", "description": "Returns a specific role from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns role", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Role"}}}}, "404": {"description": "Role of that Id not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}, "put": {"tags": ["Permission"], "summary": "Update role", "description": "Updates a role from supplied role id and object.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully updated role"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}, "delete": {"tags": ["Permission"], "summary": "Delete role", "description": "Deletes a role with the supplied role id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted role"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Accounts/{section}/by-id/multiple": {"post": {"tags": ["Permission"], "summary": "Get Multiple accounts by their id", "description": "Returns accounts from supplied id and section.", "parameters": [{"name": "section", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "Array of ids", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "Array of ids", "nullable": true}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "Array of ids", "nullable": true}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "Array of ids", "nullable": true}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "Array of ids", "nullable": true}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns accounts for a section", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.Auth.VM_Account"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Account/{section}/{id}": {"get": {"tags": ["Permission"], "summary": "Get account", "description": "Returns an account from supplied id and section.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}, {"name": "section", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns account for a section", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.Auth.VM_Account"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Account/{section}/{id}/ResetPassword": {"patch": {"tags": ["Permission"], "summary": "Reset password", "description": "Resets the password from supplied account id and section", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}, {"name": "section", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns boolean for reset password", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Accounts/{section}": {"get": {"tags": ["Permission"], "summary": "Get accounts by section", "description": "Returns all accounts for a specified section.\r\nAccepted section input: \"partner\" or \"customer\"", "parameters": [{"name": "section", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of accounts for a section", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.Auth.VM_Account"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Role": {"post": {"tags": ["Permission"], "summary": "Create role", "description": "Creates a new role with supplied object, and returns the result.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateRole"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created role", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Role"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Account": {"post": {"tags": ["Permission"], "summary": "Create new account", "description": "Creates a new account from supplied object and returns it.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateAccount"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateAccount"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateAccount"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateAccount"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.Services.Auth.VM_Account"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Role/{id}/Permissions": {"put": {"tags": ["Permission"], "summary": "Update role permissions", "description": "Update roles permission to current list.", "parameters": [{"name": "id", "in": "path", "description": "Id of role to change permisisons", "required": true, "schema": {"type": "integer", "description": "Id of role to change permisisons", "format": "int32"}}], "requestBody": {"description": "Array of Permission nodes to set.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateRolePermissions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateRolePermissions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateRolePermissions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateRolePermissions"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "204": {"description": "Successfully updated role permissions"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Account/{id}": {"put": {"tags": ["Permission"], "summary": "Update account", "description": "Updates an account from supplied id and object.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateAccount"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateAccount"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateAccount"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateAccount"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully updated account"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}, "delete": {"tags": ["Permission"], "summary": "Delete account", "description": "Deletes an account with supplied account id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted account"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/Permission/Account/{id}/MFA": {"delete": {"tags": ["Permission"], "summary": "Reset account MFA", "description": "Resets the MFA for the supplied account id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "204": {"description": "Successfully reset account MFA"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "security": [{"bearer": []}]}}, "/PriceAdjustment/Types": {"get": {"tags": ["PriceAdjustment"], "summary": "Get price adjustments", "description": "Returns a list of all types of price adjustments.", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns all price adjustments", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_PriceAdjustmentType"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/{id}": {"get": {"tags": ["PriceAdjustment"], "summary": "Get price adjustment", "description": "Returns a specific price adjustment from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns specific price adjustment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["PriceAdjustment"], "summary": "Update price adjustment", "description": "Updates a price adjustment from supplied id and with the value.\r\nField to update is determined from the type and with the value.\r\nAccepted types: \"1\":Product, \"2\":Category, \"3\":QuoteEntryCost, \"4\":QuoteEntrySale, \"5\":Quote\r\nReturns the updated price adjustment.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_UpdatePriceAdjustment"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_UpdatePriceAdjustment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_UpdatePriceAdjustment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_UpdatePriceAdjustment"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the updated price adjustment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["PriceAdjustment"], "summary": "Delete price adjustment", "description": "Deletes a price adjustment from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "204": {"description": "Successfully deleted price adjustment"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/CategoriesByPartner/{partnerId}": {"get": {"tags": ["PriceAdjustment"], "summary": "Get partner categories", "description": "Returns a list of all categories for a partner from supplied partner id.", "parameters": [{"name": "partnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of a partners categories", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/ProductsByPartner/{partnerId}": {"get": {"tags": ["PriceAdjustment"], "summary": "Get partner products", "description": "Returns a list of all price adjustments for a partner from supplied id.", "parameters": [{"name": "partnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of a partners products", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/Product/{productId}": {"get": {"tags": ["PriceAdjustment"], "summary": "Get price adjustment", "description": "Returns the price adjustment of a product id, for the authenticated partner", "parameters": [{"name": "productId", "in": "path", "description": "Id of Product", "required": true, "schema": {"type": "integer", "description": "Id of Product", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a price adjustment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/Product/{productId}/{partnerId}": {"get": {"tags": ["PriceAdjustment"], "summary": "Get partner price adjustments", "description": "Returns the price adjustment for a specific product, using a partner id.\r\nTrying to get from other partners without relevant permissions will return exception.", "parameters": [{"name": "productId", "in": "path", "description": "Id of product", "required": true, "schema": {"type": "integer", "description": "Id of product", "format": "int32"}}, {"name": "partnerId", "in": "path", "description": "Id of partner", "required": true, "schema": {"type": "integer", "description": "Id of partner", "format": "int32", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a partners price adjustments", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/QuoteEntry/{QuoteEntryId}": {"post": {"tags": ["PriceAdjustment"], "summary": "Create a new quote entry", "description": "Creates a new quote entry from supplied id and object.", "parameters": [{"name": "QuoteEntryId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created quote entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/Category/{CategoryId}/{PartnerId}": {"post": {"tags": ["PriceAdjustment"], "summary": "Create category", "description": "Creates a new category from supplied object and ids.", "parameters": [{"name": "CategoryId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "PartnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created category", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/Product/{ProductId}/{PartnerId}": {"post": {"tags": ["PriceAdjustment"], "summary": "Create product", "description": "Creates a new product from supplied object and ids, and returns it.", "parameters": [{"name": "ProductId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "PartnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/PriceAdjustment/Quote/{QuoteId}": {"post": {"tags": ["PriceAdjustment"], "summary": "Create new quote", "description": "Creates a new quote with supplied object and id. \r\nReturns the newly made object.", "parameters": [{"name": "QuoteId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_CreatePriceAdjustment"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the newly created quote", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products/RecursionTerms": {"get": {"tags": ["Products"], "summary": "Get statuses", "description": "Returns a list of all recursion terms.", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns list of Recursion Terms", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products/IaasResourcePriceAsProduct/{PartnerId}": {"get": {"tags": ["Products"], "summary": "Get IaaS resource prices", "description": "Get Iaas Resource Prices with Discount as a Product Type addable to the quote from partner id.", "parameters": [{"name": "PartnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns IaaS price", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Product"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products": {"get": {"tags": ["Products"], "summary": "Get all products", "description": "Returns a list of all available products in repository.", "parameters": [{"name": "partnerId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "categoryIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns list of all products", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Product"}}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "post": {"tags": ["Products"], "summary": "Create product", "description": "Creates a new Product, Category Id has to match a category, which the authenticated account has permissions to manage.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateProduct"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateProduct"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateProduct"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateProduct"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the created product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Interfaces.Models.IProduct"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products/{id}": {"get": {"tags": ["Products"], "summary": "Get product", "description": "Returns the requested product from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns list of products", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Product"}}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["Products"], "summary": "Create product", "description": "Creates a new product from supplied object and id and returns the newly create item.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateProduct"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateProduct"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateProduct"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateProduct"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the created product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Product"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["Products"], "summary": "Delete product", "description": "Deletes the product from the supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted product"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products/WithMarkup/{productId}/{partnerId}": {"get": {"tags": ["Products"], "summary": "Get partner products", "description": "Get a Product on behalf of a partner, for e.g. generic listing.\r\nWill apply partner's relevant markups.", "parameters": [{"name": "productId", "in": "path", "description": "Id of Product", "required": true, "schema": {"type": "integer", "description": "Id of Product", "format": "int32"}}, {"name": "partnerId", "in": "path", "description": "Id of Partner", "required": true, "schema": {"type": "integer", "description": "Id of Partner", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns list of partners products", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Product"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products/iaas": {"post": {"tags": ["Products"], "summary": "Create IaaS", "description": "Creates a new IaaS with supplied object.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the id for the newly created IaaS", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Products/GetIaasPrice/{partnerId}": {"post": {"tags": ["Products"], "summary": "Get IaaS price (Not Post)", "description": "Returns a price for IaaS. \r\nSince the price is dependent from the input, we cannot let it be a HttpGet.\r\nHttpGet does not accept a body as input, and therefor this method is a post.", "parameters": [{"name": "partnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateIaas"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns IaaS price", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Product"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/QuoteEntry/{id}": {"get": {"tags": ["QuoteEnt<PERSON>"], "summary": "Get quote entry", "description": "Returns a quote entry from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns quote entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_QuoteEntry"}}}}, "403": {"description": "Logged-in user not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["QuoteEnt<PERSON>"], "summary": "Update quote entry", "description": "Updates a specific quote entry from supplied id and object.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuoteEntry"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuoteEntry"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuoteEntry"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuoteEntry"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the created quote entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_QuoteEntry"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["QuoteEnt<PERSON>"], "summary": "Delete quote entry", "description": "Deletes quote entry from supplied quote entry id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted quote entry"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/QuoteEntry/ByQuoteId/{id}": {"get": {"tags": ["QuoteEnt<PERSON>"], "summary": "Get quote", "description": "Returns a quote from supplied quote id.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns quote entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_QuoteEntry"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/QuoteEntry": {"post": {"tags": ["QuoteEnt<PERSON>"], "summary": "Create quote entry", "description": "Creates a new quote entry from supplied object.", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuoteEntry"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuoteEntry"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuoteEntry"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuoteEntry"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the id of created quote entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_QuoteEntry"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/QuoteEntry/{id}/Quantity/{quantity}": {"patch": {"tags": ["QuoteEnt<PERSON>"], "summary": "Modify quote entry quantity", "description": "Modifies the quantity for a quote entry and sets the new quantity supplied.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "quantity", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully modified quote entry quantity"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/Status": {"get": {"tags": ["Quotes"], "summary": "Get quote statuses", "description": "Returns a list of all possible statuses of a quote.", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns list of quote statuses", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes": {"get": {"tags": ["Quotes"], "summary": "Get quotes", "description": "Returns a list of all quotes accessable for the logged-in user.", "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of users quotes", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Quote"}}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "post": {"tags": ["Quotes"], "summary": "Create quote", "description": "Creates a new quote from supplied input and returns the id of the new quote.", "requestBody": {"description": "CreateQuote ViewModel", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuote"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuote"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuote"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_CreateQuote"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns newly created quote id", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "put": {"tags": ["Quotes"], "summary": "Update quote", "description": "Update information on a quote with supplied object.", "requestBody": {"description": "Parameters to update", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully updated quote"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/{id}": {"get": {"tags": ["Quotes"], "summary": "Get quote information", "description": "Get Detailed information about a quote, including, product info, and price adjustments.", "parameters": [{"name": "id", "in": "path", "description": "Id of quote", "required": true, "schema": {"type": "integer", "description": "Id of quote", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns detailed quote information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_Quote"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}, "delete": {"tags": ["Quotes"], "summary": "Delete quote", "description": "Delete a quote in it's entirity from supplied id.", "parameters": [{"name": "id", "in": "path", "description": "Id of quote to delete", "required": true, "schema": {"type": "integer", "description": "Id of quote to delete", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully deleted user"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/{id}/Status/{status}": {"patch": {"tags": ["Quotes"], "summary": "Modify quote property \"statusId\"", "description": "Modifies the property \"statusId\" on a quote with the supplied input.\r\nReturns the id on the modified quote.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "status", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns the id of the modified quote", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/{id}/Attention": {"patch": {"tags": ["Quotes"], "summary": "Modify quote property \"attention\"", "description": "Modifies the property \"attention\" on a quote with the supplied input.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully modified quote \"attention\""}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/{id}/Freetext": {"patch": {"tags": ["Quotes"], "summary": "Modify quote property \"freeText\"", "description": "Modifies the property \"freeText\" on a quote with the supplied input.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ViewModels.VM_UpdateQuote"}}}}, "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Successfully modified quote \"freeText\""}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/{id}/Export/{type}": {"get": {"tags": ["Quotes"], "summary": "Get quote export", "description": "Generates and returns a file for export in requested format. \r\nFiletype can be CSV or PDF.", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "type", "in": "path", "description": "", "required": true, "schema": {"type": "string", "description": "", "nullable": true}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns CSV/PDF file"}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/ByPartner/{partnerId}": {"get": {"tags": ["Quotes"], "summary": "Get quote by partner", "description": "Returns a list of quotes from supplied partner id.", "parameters": [{"name": "partnerId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns a list of partners quotes", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Quote"}}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}, "/Quotes/{quoteId}/AddTemplate/{templateId}": {"put": {"tags": ["Quotes"], "summary": "Modify quote with template", "description": "Modifies an excisting quote and adds a product template to it from supplied quote id and template id.\r\nReturns the quote id of the modified quote.", "parameters": [{"name": "quoteId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}, {"name": "templateId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "description": "", "format": "int32"}}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "200": {"description": "Returns id of modified quote.", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "403": {"description": "User not authorized to execute", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}, "deprecated": true, "security": [{"bearer": []}]}}}, "components": {"schemas": {"AuthService.Models.AccountsFilterOperator": {"enum": ["AND", "OR"], "type": "string"}, "Microsoft.AspNetCore.Mvc.ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}, "extensions": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}}, "additionalProperties": false}, "CF.Pagination.PageResponseMetadata": {"required": ["page", "pageSize", "totalPages", "totalRecords"], "type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "totalRecords": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_Role": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "protected": {"type": "boolean"}, "permissions": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.Auth.VM_Account": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "loginCount": {"type": "integer", "format": "int32"}, "partnerId": {"type": "integer", "format": "int32"}, "endCustomerId": {"type": "integer", "format": "int32", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_Role"}, "nullable": true}}, "additionalProperties": false}, "CF.Pagination.PageResponseReferenceLinks": {"required": ["currentPage", "nextPage", "previousPage"], "type": "object", "properties": {"currentPage": {"type": "string"}, "nextPage": {"type": "string"}, "previousPage": {"type": "string"}}, "additionalProperties": false}, "CF.Pagination.PageResponse`1[[ViewModels.Services.Auth.VM_Account, ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"required": ["metadata", "results"], "type": "object", "properties": {"metadata": {"$ref": "#/components/schemas/CF.Pagination.PageResponseMetadata"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.Auth.VM_Account"}}, "references": {"$ref": "#/components/schemas/CF.Pagination.PageResponseReferenceLinks"}}, "additionalProperties": false}, "DTO.Enums.AlertLevel": {"enum": ["info", "low", "medium", "high"], "type": "string"}, "Interfaces.Models.IAlert": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "alertLevel": {"$ref": "#/components/schemas/DTO.Enums.AlertLevel"}}, "additionalProperties": false}, "Models.Alert": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "alertLevel": {"$ref": "#/components/schemas/DTO.Enums.AlertLevel"}, "alertLevelString": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_EndCustomer": {"required": ["countryCode"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "partner": {"$ref": "#/components/schemas/ViewModels.VM_Partner"}, "microsoftTenantId": {"type": "string", "nullable": true, "deprecated": true}, "hidePricesInMineLicenser": {"type": "boolean"}, "externalServiceIds": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "nullable": true}, "cvr": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "format": "email", "nullable": true}, "phone": {"type": "string", "format": "tel", "nullable": true}, "address": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"pattern": "[A-Z]{2}", "type": "string"}}, "additionalProperties": false}, "ViewModels.VM_Partner": {"required": ["countryCode"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "endCustomers": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_EndCustomer"}, "nullable": true}, "portalAccess": {"type": "boolean"}, "mpnId": {"type": "integer", "format": "int32"}, "externalServiceIds": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "nullable": true}, "quoteHeader": {"type": "string", "nullable": true}, "quoteFooter": {"type": "string", "nullable": true}, "hidePricesInMineLicenser": {"type": "boolean"}, "cvr": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "format": "email", "nullable": true}, "phone": {"type": "string", "format": "tel", "nullable": true}, "address": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"pattern": "[A-Z]{2}", "type": "string"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureModels.RoleDefinition": {"type": "object", "properties": {"roleDefinitionId": {"type": "string", "nullable": true}, "principalId": {"type": "string", "nullable": true}, "scope": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "updatedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "additionalProperties": false, "nullable": true}, "updatedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureModels.AzureSetOwnerPropertiesResponse": {"type": "object", "properties": {"properties": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.RoleDefinition"}, "id": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureModels.SubscriptionRenameId": {"type": "object", "properties": {"subscriptionId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureModels.SubscriptionProperties": {"type": "object", "properties": {"roleDefinitionId": {"type": "string", "nullable": true}, "principalId": {"type": "string", "nullable": true}, "scope": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "updatedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureModels.Value": {"type": "object", "properties": {"properties": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.SubscriptionProperties"}, "id": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureModels.GetAllUsersOnASubscription": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureModels.Value"}, "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.RequestModel.CreateSubscription.CreateSubscriptionDTO": {"type": "object", "properties": {"displayName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.Properties": {"type": "object", "properties": {"subscriptionId": {"type": "string", "nullable": true}, "provisioningState": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.CreatedSubscriptionResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "properties": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.Properties"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.AzureGetCustomersModels.Reseller": {"type": "object", "properties": {"resellerId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.AzureGetCustomersModels.Properties": {"type": "object", "properties": {"billingProfileDisplayName": {"type": "string", "nullable": true}, "billingProfileId": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "enabledAzurePlans": {"type": "array", "items": {"type": "object", "additionalProperties": false}, "nullable": true}, "resellers": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.AzureGetCustomersModels.Reseller"}, "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.AzureGetCustomersModels.Customers": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "properties": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.AzureCreateSubscriptionModels.ResponseModel.AzureGetCustomersModels.Properties"}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DTO.AzureSupport.IssueTypeDTO": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "legacyId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DTO.AzureSupport.SupportProblemSubTopicDTO": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "legacyId": {"type": "string", "nullable": true}, "subTopics": {"type": "array", "items": {"type": "object", "additionalProperties": false}, "nullable": true}}, "additionalProperties": false}, "DTO.AzureSupport.SupportProblemDTO": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "legacyId": {"type": "string", "nullable": true}, "subTopics": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.AzureSupport.SupportProblemSubTopicDTO"}, "nullable": true}, "relatedProblemLegacyId": {"type": "string", "nullable": true}, "relatedProblems": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.AzureSupport.SupportProblemDTO"}, "nullable": true}}, "additionalProperties": false}, "DTO.AzureSupport.SupportFormElementDTO": {"type": "object", "properties": {"parrentID": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "controlType": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "required": {"type": "boolean"}, "dropdownOptions": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DTO.AzureSupport.AzureTicketInfoDTO": {"type": "object", "properties": {"summary": {"type": "string", "nullable": true}, "issueType": {"type": "string", "nullable": true}, "subscription": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "problemtype": {"type": "string", "nullable": true}, "subProblemtype": {"type": "string", "nullable": true}, "relatedProblemtype": {"type": "string", "nullable": true}, "relatedSubProblemtype": {"type": "string", "nullable": true}, "specialFormElemnts": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.AzureSupport.SupportFormElementDTO"}, "nullable": true}, "shareDiagnosticInformation": {"type": "boolean"}, "severity": {"type": "string"}, "availability24x7VsBusinessHours": {"type": "boolean"}, "preferredContactMethod": {"type": "string", "nullable": true}, "supportLanguage": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "additionalEmailforNotification": {"type": "string", "nullable": true}, "phoneNumber": {"type": "integer", "format": "int32"}, "countryOrRegion": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_Category": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "sort": {"type": "integer", "format": "int32"}, "isSystem": {"type": "boolean"}, "systemType": {"type": "string", "nullable": true}, "owner": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_CreateCategory": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string"}, "owner": {"type": "integer", "format": "int32"}, "sort": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_UpdateCategory": {"required": ["name", "sort"], "type": "object", "properties": {"name": {"type": "string"}, "sort": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_ContentPage": {"type": "object", "properties": {"content": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.Data.VM_Country": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "dial_Code": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "tax_Rate": {"type": "number", "format": "double"}, "supported_Cultures": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_CreateEndCustomer": {"required": ["countryCode", "partnerId"], "type": "object", "properties": {"partnerId": {"type": "integer", "format": "int32"}, "microsoftTenantId": {"type": "string", "nullable": true}, "cvr": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "format": "email", "nullable": true}, "phone": {"type": "string", "format": "tel", "nullable": true}, "address": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"pattern": "[A-Z]{2}", "type": "string"}}, "additionalProperties": false}, "ViewModels.VM_UpdateEndCustomer": {"required": ["countryCode", "partnerId"], "type": "object", "properties": {"partnerId": {"type": "integer", "format": "int32"}, "microsoftTenantId": {"type": "string", "nullable": true}, "externalServiceIds": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "nullable": true}, "hidePricesInMineLicenser": {"type": "boolean"}, "cvr": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "format": "email", "nullable": true}, "phone": {"type": "string", "format": "tel", "nullable": true}, "address": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"pattern": "[A-Z]{2}", "type": "string"}}, "additionalProperties": false}, "ViewModels.Services.Log.VM_LogRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "partnerId": {"type": "integer", "format": "int32"}, "customerId": {"type": "integer", "format": "int32"}, "statusCode": {"type": "integer", "format": "int32"}, "method": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "authId": {"type": "string", "nullable": true}, "timeMs": {"type": "integer", "format": "int64"}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ViewModels.PaginationResponse`1[[ViewModels.Services.Log.VM_LogRequest[], ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.Log.VM_LogRequest"}}, "nullable": true}}, "additionalProperties": false}, "Models.Services.MicrosoftPartnerCenter.Seat": {"type": "object", "properties": {"additionalAttributes": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}, "id": {"type": "string", "nullable": true}, "parentSubscriptionId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "targetQuantity": {"type": "integer", "format": "int32"}, "sku": {"type": "string", "nullable": true}, "isTrial": {"type": "boolean"}, "statusId": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "billingCycle": {"type": "integer", "format": "int32"}, "renewTime": {"type": "string", "format": "date-time", "nullable": true}, "termDuration": {"type": "string", "nullable": true}, "productId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Models.Services.MicrosoftPartnerCenter.Tenant": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "domain": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "qualification": {"type": "string", "nullable": true}, "userCredential_Username": {"type": "string", "nullable": true}, "userCredential_Password": {"type": "string", "nullable": true}, "seats": {"type": "array", "items": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}, "nullable": true}, "domainPrefix": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Models.Services.MicrosoftPartnerCenter.SignMCA": {"required": ["dateAgreed", "email", "firstName", "lastName", "phoneNumber"], "type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "dateAgreed": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "System.ValueTuple`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"item1": {"type": "integer", "format": "int32"}, "item2": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_AddMicrosoftSeat": {"type": "object", "properties": {"productId": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "billingCycleType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Offers.BillingCycleType": {"enum": ["Unknown", "Monthly", "Annual", "None", "OneTime", "Triennial", "Biennial"], "type": "string"}, "Microsoft.Store.PartnerCenter.Models.Orders.RenewsTo": {"type": "object", "properties": {"termDuration": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Link": {"type": "object", "properties": {"uri": {"type": "string", "format": "uri", "nullable": true}, "method": {"type": "string", "nullable": true}, "headers": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Orders.OrderLineItemLinks": {"type": "object", "properties": {"subscription": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "product": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "sku": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "availability": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "provisioningStatus": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "activationLinks": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.ResourceAttributes": {"type": "object", "properties": {"etag": {"type": "string", "nullable": true}, "objectType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Orders.OrderLineItem": {"type": "object", "properties": {"lineItemNumber": {"type": "integer", "format": "int32"}, "offerId": {"type": "string", "nullable": true}, "subscriptionId": {"type": "string", "nullable": true}, "parentSubscriptionId": {"type": "string", "nullable": true}, "termDuration": {"type": "string", "nullable": true}, "renewsTo": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Orders.RenewsTo"}, "transactionType": {"type": "string", "nullable": true}, "promotionId": {"type": "string", "nullable": true}, "customTermEndDate": {"type": "string", "format": "date-time", "nullable": true}, "friendlyName": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "partnerIdOnRecord": {"type": "string", "nullable": true}, "additionalPartnerIdsOnRecord": {"type": "array", "items": {"type": "string"}, "nullable": true}, "provisioningContext": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "attestationAccepted": {"type": "boolean", "nullable": true}, "links": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Orders.OrderLineItemLinks"}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Orders.OrderLinks": {"type": "object", "properties": {"provisioningStatus": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "patchOperation": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "self": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Orders.Order": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "alternateId": {"type": "string", "nullable": true}, "referenceCustomerId": {"type": "string", "nullable": true}, "billingCycle": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Offers.BillingCycleType"}, "currencyCode": {"type": "string", "nullable": true}, "currencySymbol": {"type": "string", "nullable": true}, "lineItems": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Orders.OrderLineItem"}, "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "transactionType": {"type": "string", "nullable": true}, "links": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Orders.OrderLinks"}, "totalPrice": {"type": "number", "format": "double", "nullable": true}, "partnerOnRecordAttestationAccepted": {"type": "boolean", "nullable": true}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.BillingCycleType": {"enum": ["Unknown", "Monthly", "Annual", "None", "OneTime", "Triennial", "Biennial"], "type": "string"}, "Microsoft.Store.PartnerCenter.Models.Products.Constraint": {"type": "object", "properties": {"constraintName": {"type": "string", "nullable": true}, "constraintValue": {"type": "number", "format": "double"}, "constraintType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.SkuAttestationProperties": {"type": "object", "properties": {"enforceAttestation": {"type": "boolean"}, "attestationType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.SpecializedOfferPricingPolicy": {"type": "object", "properties": {"policyType": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.SpecializedOfferProperties": {"type": "object", "properties": {"startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}, "pricingPolicies": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.SpecializedOfferPricingPolicy"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.StandardResourceLinks": {"type": "object", "properties": {"self": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.Sku": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "productId": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "minimumQuantity": {"type": "integer", "format": "int32"}, "maximumQuantity": {"type": "integer", "format": "int32"}, "isTrial": {"type": "boolean"}, "termsOfUseUri": {"type": "string", "nullable": true}, "supportedBillingCycles": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.BillingCycleType"}, "nullable": true}, "purchasePrerequisites": {"type": "array", "items": {"type": "string"}, "nullable": true}, "inventoryVariables": {"type": "array", "items": {"type": "string"}, "nullable": true}, "provisioningVariables": {"type": "array", "items": {"type": "string"}, "nullable": true}, "constraints": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.Constraint"}, "nullable": true}, "dynamicAttributes": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}, "attestationProperties": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.SkuAttestationProperties"}, "consumptionType": {"type": "string", "nullable": true}, "specializedOfferProperties": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.SpecializedOfferProperties"}, "links": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.StandardResourceLinks"}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.CurrencyInfo": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "symbol": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.RenewalInstructionOption": {"type": "object", "properties": {"renewToId": {"type": "string", "nullable": true}, "isAutoRenewable": {"type": "boolean"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.RenewalInstruction": {"type": "object", "properties": {"applicableTermIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "renewalOptions": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.RenewalInstructionOption"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.RenewalOption": {"type": "object", "properties": {"termDuration": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.CancellationRefundOption": {"type": "object", "properties": {"sequenceId": {"type": "integer", "format": "int32"}, "type": {"type": "string", "nullable": true}, "expiresAfter": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.CancellationPolicy": {"type": "object", "properties": {"refundOptions": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.CancellationRefundOption"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.AvailabilityTerm": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "billingCycle": {"type": "string", "nullable": true}, "renewalOptions": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.RenewalOption"}, "nullable": true}, "cancellationPolicies": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.CancellationPolicy"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.AvailabilityIncludedQuantity": {"type": "object", "properties": {"units": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.ItemType": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "subType": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.ItemType"}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.ProductLinks": {"type": "object", "properties": {"skus": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}, "self": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Link"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.Product": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "productType": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.ItemType"}, "isMicrosoftProduct": {"type": "boolean"}, "publisherName": {"type": "string", "nullable": true}, "links": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.ProductLinks"}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Products.Availability": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "productId": {"type": "string", "nullable": true}, "skuId": {"type": "string", "nullable": true}, "catalogItemId": {"type": "string", "nullable": true}, "defaultCurrency": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.CurrencyInfo"}, "segment": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "isPurchasable": {"type": "boolean"}, "isRenewable": {"type": "boolean"}, "renewalInstructions": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.RenewalInstruction"}, "nullable": true}, "terms": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.AvailabilityTerm"}, "nullable": true}, "includedQuantityOptions": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.AvailabilityIncludedQuantity"}, "nullable": true}, "product": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.Product"}, "sku": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.Sku"}, "links": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.StandardResourceLinks"}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.ProductAPI": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "skuItems": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.Sku"}, "nullable": true}, "skuAvailabilities": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Products.Availability"}}, "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_CreateTenant": {"required": ["addressLine1", "city", "companyName", "countryCode", "domainPrefix", "email", "firstName", "lastName", "partnerId", "phoneNumber", "postalCode"], "type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "domainPrefix": {"pattern": "[a-z0-9]+", "type": "string"}, "companyName": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string"}, "state": {"type": "string", "nullable": true}, "countryCode": {"type": "string"}, "postalCode": {"type": "string"}, "phoneNumber": {"type": "string"}, "partnerId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Models.Services.MicrosoftPartnerCenter.Interfaces.ITenant": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "domain": {"type": "string", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "qualification": {"type": "string", "nullable": true}, "userCredential_Username": {"type": "string", "nullable": true}, "userCredential_Password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_UpdateBillingInfo": {"required": ["addressLine1", "city", "countryCode", "email", "firstName", "id", "lastName", "partnerId", "phoneNumber", "postalCode"], "type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "companyName": {"type": "string", "nullable": true}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string"}, "countryCode": {"type": "string"}, "postalCode": {"type": "string"}, "phoneNumber": {"type": "string"}, "partnerId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.CountryValidationRules.CountryValidationRules": {"type": "object", "properties": {"iso2Code": {"type": "string", "nullable": true}, "defaultCulture": {"type": "string", "nullable": true}, "isStateRequired": {"type": "boolean"}, "supportedStatesList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "supportedLanguagesList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "supportedCulturesList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isPostalCodeRequired": {"type": "boolean"}, "postalCodeRegex": {"type": "string", "nullable": true}, "isCityRequired": {"type": "boolean"}, "isVatIdSupported": {"type": "boolean"}, "taxIdFormat": {"type": "string", "nullable": true}, "taxIdSample": {"type": "string", "nullable": true}, "vatIdRegex": {"type": "string", "nullable": true}, "phoneNumberRegex": {"type": "string", "nullable": true}, "isTaxIdSupported": {"type": "boolean"}, "isTaxIdOptional": {"type": "boolean"}, "countryCallingCodesList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_LicenseProduct": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "licenseGroup": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "skuPartNumber": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_LicenseServicePlan": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "capabilityStatus": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_SubscribedSku": {"type": "object", "properties": {"activeUnits": {"type": "integer", "format": "int32"}, "availableUnits": {"type": "integer", "format": "int32"}, "capabilityStatus": {"type": "string", "nullable": true}, "consumedUnits": {"type": "integer", "format": "int32"}, "productSku": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_LicenseProduct"}, "servicePlans": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_LicenseServicePlan"}, "nullable": true}, "suspendedUnits": {"type": "integer", "format": "int32"}, "totalUnits": {"type": "integer", "format": "int32"}, "warningUnits": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_CustomerUserAssignedLicense": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_LicenseProduct"}, "servicePlans": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_LicenseServicePlan"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.MicrosoftPartnerCenter.VM_CustomerUser": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "usageLocation": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "lastDirectorySyncTime": {"type": "string", "format": "date-time", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "userDomainType": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "licenses": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.MicrosoftPartnerCenter.VM_CustomerUserAssignedLicense"}, "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.GraphCreateUserRequestDTO": {"type": "object", "properties": {"givenName": {"type": "string", "nullable": true}, "surname": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "mailNickname": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "usageLocation": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "mobilePhone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.GraphCreateUserResponseDTO": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "givenName": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "mobilePhone": {"type": "string", "nullable": true}, "surname": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Users.PasswordProfile": {"type": "object", "properties": {"forceChangePassword": {"type": "boolean"}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Users.UserDomainType": {"enum": ["None", "Managed", "Federated"], "type": "string"}, "Microsoft.Store.PartnerCenter.Models.Users.UserState": {"enum": ["Active", "Inactive"], "type": "string"}, "Microsoft.Store.PartnerCenter.Models.Users.CustomerUser": {"type": "object", "properties": {"usageLocation": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "passwordProfile": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.PasswordProfile"}, "lastDirectorySyncTime": {"type": "string", "format": "date-time", "nullable": true}, "userDomainType": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.UserDomainType"}, "state": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Users.UserState"}, "softDeletionTime": {"type": "string", "format": "date-time", "nullable": true}, "links": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.StandardResourceLinks"}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.CustomCustomerUser": {"type": "object", "properties": {"usageLocation": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "userDomainType": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.ProductSku": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "skuPartNumber": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}, "licenseGroupId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.ServicePlan": {"type": "object", "properties": {"displayName": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "capabilityStatus": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.LicensesAttributes": {"type": "object", "properties": {"objectType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.LicensesItem": {"type": "object", "properties": {"availableUnits": {"type": "integer", "format": "int32"}, "activeUnits": {"type": "integer", "format": "int32"}, "consumedUnits": {"type": "integer", "format": "int32"}, "suspendedUnits": {"type": "integer", "format": "int32"}, "totalUnits": {"type": "integer", "format": "int32"}, "warningUnits": {"type": "integer", "format": "int32"}, "productSku": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.ProductSku"}, "servicePlans": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.ServicePlan"}, "nullable": true}, "capabilityStatus": {"type": "string", "nullable": true}, "attributes": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.LicensesAttributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.GetAllAvailableLicensesOnCustomer": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.LicensesItem"}, "nullable": true}, "attributes": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.LicensesAttributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.ProductSku": {"type": "object", "properties": {"prerequisiteSkus": {"type": "array", "items": {"type": "string"}, "nullable": true}, "skuId": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "skuPartNumber": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}, "licenseGroupId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.ServicePlan": {"type": "object", "properties": {"displayName": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "capabilityStatus": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.LicensesAttributes": {"type": "object", "properties": {"objectType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.LicensesItem": {"type": "object", "properties": {"availableUnits": {"type": "integer", "format": "int32"}, "activeUnits": {"type": "integer", "format": "int32"}, "consumedUnits": {"type": "integer", "format": "int32"}, "suspendedUnits": {"type": "integer", "format": "int32"}, "totalUnits": {"type": "integer", "format": "int32"}, "warningUnits": {"type": "integer", "format": "int32"}, "productSku": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.ProductSku"}, "servicePlans": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.ServicePlan"}, "nullable": true}, "capabilityStatus": {"type": "string", "nullable": true}, "attributes": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.LicensesAttributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.GetAllAvailableLicensesOnCustomerWithPrerequisiteSkus": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.LicensesItem"}, "nullable": true}, "attributes": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.LicensesAttributes"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Licenses.LicenseAssignment": {"type": "object", "properties": {"excludedPlans": {"type": "array", "items": {"type": "string"}, "nullable": true}, "skuId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Licenses.LicenseWarning": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "servicePlans": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Licenses.LicenseUpdate": {"type": "object", "properties": {"licensesToAssign": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseAssignment"}, "nullable": true}, "licensesToRemove": {"type": "array", "items": {"type": "string"}, "nullable": true}, "licenseWarnings": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Licenses.LicenseWarning"}, "nullable": true, "readOnly": true}, "attributes": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.ResourceAttributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.LicensServicePlan": {"type": "object", "properties": {"displayName": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "capabilityStatus": {"type": "string", "nullable": true}, "targetType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.LicensProductSku": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "skuPartNumber": {"type": "string", "nullable": true}, "licenseGroupId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.Attributes": {"type": "object", "properties": {"objectType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.LicensItem": {"type": "object", "properties": {"servicePlans": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.LicensServicePlan"}, "nullable": true}, "productSku": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.LicensProductSku"}, "attributes": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.Attributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.API.GetAllAssignedLicensesOnAUser": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.LicensItem"}, "nullable": true}, "attributes": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.API.Attributes"}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.PartnerCenterCustomerUser": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "softDeletionTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MicrosoftPartnerCenter.Models.PartnerCenterModels.PartnerCenterCustomerUserDTO": {"type": "object", "properties": {"totalCount": {"type": "string", "nullable": true}, "user": {"type": "array", "items": {"$ref": "#/components/schemas/MicrosoftPartnerCenter.Models.PartnerCenterModels.PartnerCenterCustomerUser"}, "nullable": true}}, "additionalProperties": false}, "DTO.PartnerCenterRestoredCustomerUserDTO": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.GraphUserDTO": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "mobilePhone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WebAPI.ViewModels.VM_GraphUpdateUser": {"type": "object", "properties": {"jobTitle": {"type": "string", "nullable": true}, "mobilePhone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Graph.AverageComparativeScore": {"type": "object", "properties": {"averageScore": {"type": "number", "format": "double", "nullable": true}, "basis": {"type": "string", "nullable": true}, "additionalData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}, "oDataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Graph.ControlScore": {"type": "object", "properties": {"controlCategory": {"type": "string", "nullable": true}, "controlName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "score": {"type": "number", "format": "double", "nullable": true}, "additionalData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}, "oDataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Graph.SecurityVendorInformation": {"type": "object", "properties": {"provider": {"type": "string", "nullable": true}, "providerVersion": {"type": "string", "nullable": true}, "subProvider": {"type": "string", "nullable": true}, "vendor": {"type": "string", "nullable": true}, "additionalData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}, "oDataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Graph.SecureScore": {"type": "object", "properties": {"activeUserCount": {"type": "integer", "format": "int32", "nullable": true}, "averageComparativeScores": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Graph.AverageComparativeScore"}, "nullable": true}, "azureTenantId": {"type": "string", "nullable": true}, "controlScores": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Graph.ControlScore"}, "nullable": true}, "createdDateTime": {"type": "string", "format": "date-time", "nullable": true}, "currentScore": {"type": "number", "format": "double", "nullable": true}, "enabledServices": {"type": "array", "items": {"type": "string"}, "nullable": true}, "licensedUserCount": {"type": "integer", "format": "int32", "nullable": true}, "maxScore": {"type": "number", "format": "double", "nullable": true}, "vendorInformation": {"$ref": "#/components/schemas/Microsoft.Graph.SecurityVendorInformation"}, "id": {"type": "string", "nullable": true}, "oDataType": {"type": "string", "nullable": true}, "additionalData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.GraphSecurityScoreDTO": {"type": "object", "properties": {"controlScore": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Graph.SecureScore"}, "nullable": true}}, "additionalProperties": false}, "Microsoft.Graph.ServicePlanInfo": {"type": "object", "properties": {"appliesTo": {"type": "string", "nullable": true}, "provisioningStatus": {"type": "string", "nullable": true}, "servicePlanId": {"type": "string", "format": "uuid", "nullable": true}, "servicePlanName": {"type": "string", "nullable": true}, "additionalData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}, "oDataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Graph.LicenseDetails": {"type": "object", "properties": {"servicePlans": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Graph.ServicePlanInfo"}, "nullable": true}, "skuId": {"type": "string", "format": "uuid", "nullable": true}, "skuPartNumber": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "oDataType": {"type": "string", "nullable": true}, "additionalData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false}, "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.GraphLicenseDetailsDTO": {"type": "object", "properties": {"licenseDetails": {"type": "array", "items": {"$ref": "#/components/schemas/Microsoft.Graph.LicenseDetails"}, "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.MfaUser": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "isMfaRegistered": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "DTO.MicrosoftGraph.GraphMfaStatusOnUsersDTO": {"type": "object", "properties": {"mfaUser": {"type": "array", "items": {"$ref": "#/components/schemas/DTO.MicrosoftGraph.MfaUser"}, "nullable": true}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_NCE_SubscriptionConvertion": {"required": ["targetQuantity"], "type": "object", "properties": {"subscriptionId": {"type": "string", "description": "Existing subscription id on the tenant; May be null if purchasing a new subscription", "nullable": true}, "targetProductId": {"type": "string", "description": "Product to be purchased in stead of; May be null if target quantity is 0", "format": "uuid", "nullable": true}, "targetQuantity": {"type": "integer", "description": "Quantity of new product to be aquired. May be 0 for simply suspending", "format": "int32"}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_NCE_QueueSubscriptionConvertion": {"required": ["conversionTimeUTC", "subscriptions"], "type": "object", "properties": {"subscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_SubscriptionConvertion"}, "description": "List of Subscriptions to Convert; Send empty list to cancel."}, "conversionTimeUTC": {"type": "string", "description": "Time, in UTC, to execute the conversion.", "format": "date-time"}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_NCE_Seat": {"type": "object", "properties": {"seat": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Seat"}, "canConvert": {"type": "boolean", "readOnly": true}, "recomendedConvertion": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_NCE_Tenant": {"type": "object", "properties": {"tenant": {"$ref": "#/components/schemas/Models.Services.MicrosoftPartnerCenter.Tenant"}, "seats": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_NCE_Seat"}, "nullable": true}}, "additionalProperties": false}, "ViewModes.Microsoft.VM_Product": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "categoryId": {"type": "string", "format": "uuid", "readOnly": true}, "sku": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "description": {"type": "string", "nullable": true, "readOnly": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true, "readOnly": true}, "deprecated": {"type": "boolean", "readOnly": true}, "recursionTerm": {"type": "integer", "description": "How long, in months, between a products review cycle. 0 means product is a one-time purchase; -1 means unknown.", "format": "int32", "readOnly": true}, "billingTerm": {"type": "integer", "description": "How long, in months, between a products billing cycle. 0 means product is a one-time purchase; -1 means unknown.", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_Price": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid", "readOnly": true}, "currency": {"type": "string", "nullable": true, "readOnly": true}, "cost": {"type": "number", "format": "double", "readOnly": true}, "sale": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_NCE_AllConvertions": {"type": "object", "properties": {"legacySku": {"type": "string", "nullable": true}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModes.Microsoft.VM_Product"}, "nullable": true}, "prices": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_Price"}, "nullable": true}}, "additionalProperties": false}, "Models.MicrosoftSkuConvertion": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "legacySku": {"type": "string", "nullable": true}, "nceSku": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_ProvisionProduct": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WebAPI.ViewModels.Microsoft.VM_ProvisionNew": {"type": "object", "properties": {"cart": {"type": "array", "items": {"$ref": "#/components/schemas/WebAPI.ViewModels.Microsoft.VM_ProvisionProduct"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.SplaReport.VM_SplaCustomer": {"type": "object", "properties": {"customerGuid": {"type": "string", "format": "uuid"}, "customerName": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "created": {"type": "string", "format": "date-time", "nullable": true}, "modified": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.SplaReport.VM_SplaProduct": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "productGuid": {"type": "string", "format": "uuid"}, "sku": {"type": "string", "nullable": true}, "customers": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.SplaReport.VM_SplaCustomer"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.Services.SplaReport.VM_Price": {"type": "object", "properties": {"productGuid": {"type": "string", "format": "uuid", "readOnly": true}, "currency": {"type": "string", "nullable": true, "readOnly": true}, "cost": {"type": "number", "format": "double", "readOnly": true}, "sale": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "ViewModels.Services.SplaReport.VM_SplaReport": {"type": "object", "properties": {"guid": {"type": "string", "format": "uuid"}, "partnerGuid": {"type": "string", "format": "uuid"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.SplaReport.VM_SplaProduct"}, "nullable": true}, "prices": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.SplaReport.VM_Price"}, "nullable": true}, "created": {"type": "string", "format": "date-time", "nullable": true}, "modified": {"type": "string", "format": "date-time", "nullable": true}, "submitted": {"type": "string", "format": "date-time", "nullable": true}, "billingMonth": {"type": "integer", "format": "int32", "nullable": true}, "billingYear": {"type": "integer", "format": "int32", "nullable": true}, "submitDay": {"type": "string", "format": "date-time", "nullable": true}, "canBeSubmitted": {"type": "boolean"}}, "additionalProperties": false}, "Interfaces.Models.CreateUpdateSplaCustomer": {"type": "object", "properties": {"customerGuid": {"type": "string", "format": "uuid"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Interfaces.Models.CreateUpdateSplaProduct": {"type": "object", "properties": {"productGuid": {"type": "string", "format": "uuid"}, "customers": {"type": "array", "items": {"$ref": "#/components/schemas/Interfaces.Models.CreateUpdateSplaCustomer"}, "nullable": true}}, "additionalProperties": false}, "Interfaces.Models.CreateUpdateSplaReport": {"type": "object", "properties": {"guid": {"type": "string", "format": "uuid", "nullable": true}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Interfaces.Models.CreateUpdateSplaProduct"}, "nullable": true}}, "additionalProperties": false}, "CF.Pagination.PageResponse`1[[ViewModels.Services.SplaReport.VM_SplaReport, ViewModels, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"required": ["metadata", "results"], "type": "object", "properties": {"metadata": {"$ref": "#/components/schemas/CF.Pagination.PageResponseMetadata"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.Services.SplaReport.VM_SplaReport"}}, "references": {"$ref": "#/components/schemas/CF.Pagination.PageResponseReferenceLinks"}}, "additionalProperties": false}, "Interfaces.Models.SplaReportPartnerFilter": {"enum": ["WithSplaReport", "WithoutSplaReport"], "type": "string"}, "WebAPI.ModelDtos.StatusDto": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WebAPI.ModelDtos.AutoRenewDto": {"type": "object", "properties": {"autoRenew": {"type": "boolean"}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Subscriptions.ProductTerm": {"type": "object", "properties": {"productId": {"type": "string", "nullable": true}, "skuId": {"type": "string", "nullable": true}, "availabilityId": {"type": "string", "nullable": true}, "billingCycle": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Offers.BillingCycleType"}, "termDuration": {"type": "string", "nullable": true}, "promotionId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.Store.PartnerCenter.Models.Subscriptions.ScheduledNextTermInstructions": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/Microsoft.Store.PartnerCenter.Models.Subscriptions.ProductTerm"}, "quantity": {"type": "integer", "format": "int32"}, "customTermEndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DTO.PartnerBrandingDTO": {"type": "object", "properties": {"quoteHeader": {"type": "string", "nullable": true}, "quoteFooter": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_UpdatePartner": {"required": ["countryCode"], "type": "object", "properties": {"quoteHeader": {"type": "string", "nullable": true}, "quoteFooter": {"type": "string", "nullable": true}, "hidePricesInMineLicenser": {"type": "boolean"}, "cvr": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contactEmail": {"type": "string", "format": "email", "nullable": true}, "phone": {"type": "string", "format": "tel", "nullable": true}, "address": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"pattern": "[A-Z]{2}", "type": "string"}}, "additionalProperties": false}, "System.IO.File": {"type": "object", "additionalProperties": false}, "ViewModels.VM_PermissionNode": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_CreateRole": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}, "ViewModels.VM_CreateAccount": {"required": ["email", "name"], "type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "roles": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "partnerId": {"type": "integer", "format": "int32"}, "endCustomerId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_UpdateRolePermissions": {"required": ["permissionNodes"], "type": "object", "properties": {"permissionNodes": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "additionalProperties": false}, "ViewModels.VM_UpdateAccount": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "partnerId": {"type": "integer", "format": "int32"}, "endCustomerId": {"type": "integer", "format": "int32", "nullable": true}, "roles": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_PriceAdjustmentType": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.PriceAdjustment.VM_PriceAdjustment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "rate": {"type": "number", "format": "double"}, "type": {"type": "integer", "format": "int32"}, "glueId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.PriceAdjustment.VM_UpdatePriceAdjustment": {"type": "object", "properties": {"rate": {"type": "number", "format": "double"}, "type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.PriceAdjustment.VM_CreatePriceAdjustment": {"type": "object", "properties": {"rate": {"type": "number", "format": "double"}, "type": {"type": "integer", "format": "int32"}, "editCost": {"type": "boolean"}}, "additionalProperties": false}, "ViewModels.VM_Product": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "pricePartner": {"type": "number", "format": "double"}, "priceMSRP": {"type": "number", "format": "double"}, "basePricePartner": {"type": "number", "format": "double"}, "basePriceMSRP": {"type": "number", "format": "double"}, "vCPU": {"type": "integer", "format": "int32", "nullable": true}, "memory": {"type": "integer", "format": "int32", "nullable": true}, "storage": {"type": "integer", "format": "int32", "nullable": true}, "sku": {"type": "string", "nullable": true}, "category": {"$ref": "#/components/schemas/ViewModels.VM_Category"}, "costAdjustment": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}, "saleAdjustment": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}, "owner": {"type": "integer", "format": "int32"}, "recursionTerm": {"type": "integer", "format": "int32"}, "deprecated": {"type": "boolean"}}, "additionalProperties": false}, "ViewModels.VM_CreateProduct": {"required": ["categoryId", "name", "priceMSRP", "pricePartner"], "type": "object", "properties": {"name": {"type": "string"}, "pricePartner": {"type": "number", "format": "double"}, "priceMSRP": {"type": "number", "format": "double"}, "categoryId": {"type": "integer", "format": "int32"}, "owner": {"type": "integer", "format": "int32"}, "recursionTerm": {"type": "integer", "format": "int32"}, "sku": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Interfaces.RecursionTerm": {"enum": ["OneTime", "<PERSON><PERSON>", "Quaterly", "Yearly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Oct<PERSON><PERSON>ly"], "type": "string"}, "Interfaces.Models.IProduct": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "priceCost": {"type": "number", "format": "double"}, "priceMSRP": {"type": "number", "format": "double"}, "categoryId": {"type": "integer", "format": "int32"}, "vCPU": {"type": "integer", "format": "int32", "nullable": true}, "memory": {"type": "integer", "format": "int32", "nullable": true}, "storage": {"type": "integer", "format": "int32", "nullable": true}, "sku": {"type": "string", "nullable": true}, "owner": {"type": "integer", "format": "int32"}, "recursionTerm": {"$ref": "#/components/schemas/Interfaces.RecursionTerm"}, "deprecation": {"type": "boolean"}}, "additionalProperties": false}, "ViewModels.VM_UpdateProduct": {"required": ["categoryId", "deprecated", "name", "priceMSRP", "pricePartner", "recursionTerm"], "type": "object", "properties": {"name": {"type": "string"}, "pricePartner": {"type": "number", "format": "double"}, "priceMSRP": {"type": "number", "format": "double"}, "categoryId": {"type": "integer", "format": "int32"}, "recursionTerm": {"type": "integer", "format": "int32"}, "sku": {"type": "string", "nullable": true}, "deprecated": {"type": "boolean"}}, "additionalProperties": false}, "ViewModels.VM_CreateIaas": {"required": ["cpu", "disk", "name", "owner", "ram"], "type": "object", "properties": {"name": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "ram": {"type": "integer", "format": "int32"}, "disk": {"type": "integer", "format": "int32"}, "owner": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_QuoteEntry": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "quoteId": {"type": "integer", "format": "int32"}, "product": {"$ref": "#/components/schemas/ViewModels.VM_Product"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "priceMSRP": {"type": "number", "format": "double", "readOnly": true}, "pricePartner": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "ViewModels.VM_UpdateQuoteEntry": {"required": ["id", "productId", "quantity"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_CreateQuoteEntry": {"required": ["productId", "quantity", "quoteId"], "type": "object", "properties": {"quoteId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ViewModels.VM_Quote": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "partner": {"$ref": "#/components/schemas/ViewModels.VM_Partner"}, "endCustomer": {"$ref": "#/components/schemas/ViewModels.VM_EndCustomer"}, "created": {"type": "string", "format": "date-time"}, "lastEdited": {"type": "string", "format": "date-time"}, "quoteEntries": {"type": "array", "items": {"$ref": "#/components/schemas/ViewModels.VM_QuoteEntry"}, "nullable": true}, "status": {"type": "string", "nullable": true}, "priceAdjustment": {"$ref": "#/components/schemas/ViewModels.PriceAdjustment.VM_PriceAdjustment"}, "attention": {"type": "string", "nullable": true}, "freeText": {"type": "string", "nullable": true}, "taxRateCountry": {"type": "number", "format": "double", "nullable": true}, "taxRateOverride": {"type": "number", "format": "double", "nullable": true}, "taxRate": {"type": "number", "format": "double", "readOnly": true}, "quoteTag": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_CreateQuote": {"required": ["endCustomerId", "partnerId"], "type": "object", "properties": {"partnerId": {"type": "integer", "format": "int32"}, "endCustomerId": {"type": "integer", "format": "int32"}, "attention": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewModels.VM_UpdateQuote": {"required": ["endCustomerId", "id", "statusId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "statusId": {"type": "integer", "format": "int32"}, "endCustomerId": {"type": "integer", "format": "int32"}, "taxRateOverride": {"type": "number", "format": "double", "nullable": true}, "attention": {"type": "string", "nullable": true}, "freeText": {"type": "string", "nullable": true}, "quoteTag": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"bearer": {"type": "http", "description": "Please enter into the field below the JWT Token. You do NOT need to add the word Bearer", "scheme": "bearer", "bearerFormat": "JWT"}}}}