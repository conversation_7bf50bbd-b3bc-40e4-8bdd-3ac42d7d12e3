# Partner Center Impersonation Script using REST API
# This script authenticates with Microsoft Entra ID and allows switching to another tenant to list subscriptions and role assignments

# Configuration parameters - modify these as needed
# PowerShell's built-in client ID for interactive authentication
$clientId = "1950a258-227b-4e31-a9cf-717495945fc2"  # PowerShell's built-in client ID
$tenantId = "common"  # Use 'common' for multi-tenant authentication or specify your tenant ID
$redirectUri = "urn:ietf:wg:oauth:2.0:oob"  # Standard redirect URI for PowerShell

# Target tenant to impersonate/access - uncomment the one you want to use
$targetTenantId = "a032445b-08b9-42b9-8fae-6d8ea90723e0"
# $targetTenantId = "98ae562d-ed37-48b0-a9ca-d48df78c56c0"
# $targetTenantId = "f57aa6c3-5ecf-4771-822c-bf40f54c5acf"

function Get-DeviceCodeAuthentication {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$Scope = "https://management.azure.com/.default offline_access"
    )
    
    $deviceCodeUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/devicecode"
    
    $body = @{
        client_id = $ClientId
        scope     = $Scope
    }
    
    try {
        $deviceCodeResponse = Invoke-RestMethod -Uri $deviceCodeUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        
        # Display the message to the user
        Write-Host $deviceCodeResponse.message -ForegroundColor Cyan
        
        # Poll for token
        $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
        $tokenBody = @{
            grant_type    = "device_code"
            device_code   = $deviceCodeResponse.device_code
            client_id     = $ClientId
        }
        
        $interval = $deviceCodeResponse.interval
        $expiresOn = (Get-Date).AddSeconds($deviceCodeResponse.expires_in)
        
        while ((Get-Date) -lt $expiresOn) {
            try {
                Start-Sleep -Seconds $interval
                $tokenResponse = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $tokenBody -ContentType "application/x-www-form-urlencoded"
                return $tokenResponse
            }
            catch {
                if ($_.Exception.Response.StatusCode -ne 400) {
                    throw
                }
                $error = $_.ErrorDetails.Message | ConvertFrom-Json
                if ($error.error -ne "authorization_pending") {
                    Write-Host "Error: $($error.error_description)" -ForegroundColor Red
                    throw
                }
            }
        }
        
        throw "Authentication timed out"
    }
    catch {
        Write-Error "Error during device code authentication: $_"
        throw
    }
}

function Get-AccessTokenForTargetTenant {
    param (
        [string]$TargetTenantId,
        [string]$ClientId,
        [string]$RefreshToken,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TargetTenantId/oauth2/v2.0/token"
    
    $body = @{
        client_id     = $ClientId
        scope         = $Scope
        refresh_token = $RefreshToken
        grant_type    = "refresh_token"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token for target tenant: $_"
        throw
    }
}

function Get-Subscriptions {
    param (
        [string]$AccessToken
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions?api-version=2020-01-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving subscriptions: $_"
        throw
    }
}

function Get-RoleAssignments {
    param (
        [string]$AccessToken,
        [string]$SubscriptionId
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions/$SubscriptionId/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving role assignments: $_"
        throw
    }
}

# Main script execution
try {
    Write-Host "Authenticating to home tenant..." -ForegroundColor Cyan
    $tokenResponse = Get-DeviceCodeAuthentication -TenantId $tenantId -ClientId $clientId
    
    Write-Host "Successfully authenticated to home tenant." -ForegroundColor Green
    Write-Host "Switching context to target tenant: $targetTenantId" -ForegroundColor Cyan
    
    $targetTokenResponse = Get-AccessTokenForTargetTenant -TargetTenantId $targetTenantId -ClientId $clientId -RefreshToken $tokenResponse.refresh_token
    
    Write-Host "Successfully switched to target tenant." -ForegroundColor Green
    Write-Host "Retrieving subscriptions..." -ForegroundColor Cyan
    
    $subscriptions = Get-Subscriptions -AccessToken $targetTokenResponse.access_token
    
    Write-Host "Found $($subscriptions.Count) subscriptions:" -ForegroundColor Green
    $subscriptions | Format-Table -Property subscriptionId, displayName, state -AutoSize
    
    if ($subscriptions.Count -gt 0) {
        $selectedSubscription = $subscriptions[0]
        Write-Host "Retrieving role assignments for subscription: $($selectedSubscription.displayName)" -ForegroundColor Cyan
        
        $roleAssignments = Get-RoleAssignments -AccessToken $targetTokenResponse.access_token -SubscriptionId $selectedSubscription.subscriptionId
        
        Write-Host "Found $($roleAssignments.Count) role assignments:" -ForegroundColor GreenApp
        $roleAssignments | Format-Table -Property roleDefinitionId, principalId, scope -AutoSize
    }
}
catch {
    Write-Error "An error occurred: $_"
}