#This script will loop through all our partners tenants and check their azure subscription to see if we have access to them.
#The minimum access required 
# Version 1.0.7 - Added customer filtering capabilities using Partner Center API filter parameters

#Roles that are PEC eligible
$PECRoles = @(
    "Owner"
    "Contributor"
    "Reader"
    "Support Request Contributor"
)

# Configuration - Replace these with your actual values
$tenantId = "ad926c03-4b20-4cfa-9353-f896a61b0d25"  # Your tenant ID
$clientId = "a7585dd1-d076-4db7-955c-b21f8f9ea21d"  # Your application ID
if (!$clientSecret ){
    $clientSecret = Read-Host -Prompt "Enter client secret"  # Your application secret
}
$appSecret = ConvertTo-SecureString -String $clientSecret -AsPlainText -Force
$credential = [PSCredential]::new($clientId, $appSecret)

$tokenSplat = @{
    ApplicationId        = $clientId
    Credential           = $credential
    #Scopes               = @("https://api.partnercenter.microsoft.com/user_impersonation", "https://management.azure.com/user_impersonation")
    Scopes               = @("https://management.azure.com/user_impersonation")
    ServicePrincipal     = $true
    TenantId             = $tenantId
    UseAuthorizationCode = $true
}

$token = New-PartnerAccessToken @tokenSplat
$testtoken=Connect-PartnerCenter
Connect-PartnerCenter
Get-PartnerCustomerAccessToken -CustomerId $azureplan.tenantid -Resource "https://management.azure.com/" -Scopes "https://management.azure.com/.default"

Get-PartnerCustomerSubscription


# Function to get all items from paginated Partner Center API endpoints
function Get-PartnerCenterApiItems {
    param (
        [Parameter(Mandatory = $true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory = $true)]
        [string]$BaseUri,
        
        [Parameter(Mandatory = $false)]
        [string]$QueryString = "",
        
        [Parameter(Mandatory = $false)]
        [int]$MaxItems = 0  # 0 means retrieve all items
    )
    
    $headers = @{
        "Authorization"    = "Bearer $AccessToken"
        "Accept"           = "application/json"
        "MS-RequestId"     = [guid]::NewGuid().ToString()
        "MS-CorrelationId" = [guid]::NewGuid().ToString()
    }
    
    $allItems = @()
    $uri = if ($QueryString) { "$BaseUri$QueryString" } else { $BaseUri }
    $continuationToken = $null
    
    do {
        Write-Host "Requesting data from $uri"
        
        # Update headers if we have a continuation token
        if ($continuationToken) {
            $headers["MS-ContinuationToken"] = $continuationToken
        }
        
        try {
            $response = Invoke-RestMethod -Uri $uri -Headers $headers -Method Get
            
            if ($response.items) {
                $allItems += $response.items
                Write-Host "Retrieved $($response.items.Count) items (Total: $($allItems.Count))"
            }
            
            # Check for continuation token
            if ($response.continuationToken) {
                $continuationToken = $response.continuationToken
                Write-Host "Continuation token found, fetching next page..."
            } else {
                Write-Host "No further pages available."
                $continuationToken = $null
            }
            
            # Check if maximum item limit is reached
            if ($MaxItems -gt 0 -and $allItems.Count -ge $MaxItems) {
                Write-Host "Reached maximum item limit of $MaxItems."
                $allItems = $allItems[0..($MaxItems - 1)]
                break
            }
        }
        catch {
            Write-Error "Error retrieving data: $_"
            
            # Improved error handling for different response types
            if ($_.Exception.Response) {
                try {
                    # For older PowerShell versions
                    if ($_.Exception.Response.GetResponseStream) {
                        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                        $reader.BaseStream.Position = 0
                        $reader.DiscardBufferedData()
                        $responseBody = $reader.ReadToEnd()
                        Write-Error "Response body: $responseBody"
                    }
                    # For newer PowerShell versions
                    elseif ($_.ErrorDetails.Message) {
                        Write-Error "Response body: $($_.ErrorDetails.Message)"
                    }
                }
                catch {
                    Write-Error "Could not read error response details: $_"
                }
            }
            
            $continuationToken = $null
        }
    } while ($continuationToken)
    
    return $allItems
}

# Function to find a customer by domain or company name
function Find-PartnerCustomer {
    param (
        [Parameter(Mandatory = $true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory = $false)]
        [string]$Domain,
        
        [Parameter(Mandatory = $false)]
        [string]$CompanyName,
        
        [Parameter(Mandatory = $false)]
        [string]$TenantId
    )
    
    # If we have a specific TenantId, we can try a direct approach first
    if ($TenantId) {
        try {
            $uri = "https://api.partnercenter.microsoft.com/v1/customers/$TenantId"
            $headers = @{
                "Authorization"    = "Bearer $AccessToken"
                "Accept"           = "application/json"
                "MS-RequestId"     = [guid]::NewGuid().ToString()
                "MS-CorrelationId" = [guid]::NewGuid().ToString()
            }
            
            $customer = Invoke-RestMethod -Uri $uri -Headers $headers -Method Get
            Write-Host "Found customer by tenant ID: $($customer.companyProfile.companyName)"
            return $customer
        }
        catch {
            Write-Warning "Could not find customer directly by tenant ID: $TenantId. Trying other methods."
        }
    }
    
    # Prepare filter query based on what was provided
    $queryString = ""
    
    if ($Domain) {
        # Per API docs, we can filter by DefaultDomainName that starts with the provided value
        $encodedDomain = [System.Web.HttpUtility]::UrlEncode($Domain)
        $queryString = "?filter.field=DefaultDomainName&filter.value=$encodedDomain&filter.operator=StartsWith"
    }
    elseif ($CompanyName) {
        # Per API docs, we can filter by DisplayName that starts with the provided value
        $encodedName = [System.Web.HttpUtility]::UrlEncode($CompanyName)
        $queryString = "?filter.field=DisplayName&filter.value=$encodedName&filter.operator=StartsWith"
    }
    else {
        Write-Error "Must provide either Domain, CompanyName, or TenantId"
        return $null
    }
    
    $customers = Get-PartnerCenterApiItems -AccessToken $AccessToken -BaseUri "https://api.partnercenter.microsoft.com/v1/customers" -QueryString $queryString
    
    # If filtering by domain, try to find an exact match
    if ($Domain -and $customers.Count -gt 0) {
        $exactMatch = $customers | Where-Object { $_.companyProfile.domain -eq $Domain }
        if ($exactMatch) {
            Write-Host "Found exact domain match for: $Domain"
            return $exactMatch
        }
    }
    
    # If filtering by company name, try to find an exact match
    if ($CompanyName -and $customers.Count -gt 0) {
        $exactMatch = $customers | Where-Object { $_.companyProfile.companyName -eq $CompanyName }
        if ($exactMatch) {
            Write-Host "Found exact company name match for: $CompanyName"
            return $exactMatch
        }
    }
    
    # Return all matches (which could be empty, one, or multiple)
    return $customers
}

# Example: Find a specific customer
$customerDomain = "iaas.cloudfactory.dk"
$customerName = "CLOUD FACTORY A/S (IaaS)"
$customerTenantId = "a032445b-08b9-42b9-8fae-6d8ea90723e0"

# Find customer by domain (primary domain)
Write-Host "Finding customer by domain: $customerDomain"
$customer = Find-PartnerCustomer -AccessToken $token.AccessToken -TenantId $customerTenantId 

# Get all Azure subscriptions for the customer
Write-Host "Retrieving Azure subscriptions for customer: $($customer.companyProfile.companyName)"
$Subscriptions = Get-PartnerCenterApiItems -AccessToken $token.AccessToken -BaseUri "https://api.partnercenter.microsoft.com/v1/customers/$($customer.id)/subscriptions"

# Check if we have access to the Azure subscriptions
foreach ($AzurePlan in $Subscriptions | Where-Object { $_.status -eq "active" -and $_.productType.id -eq "Azure" }) {
    # Get Azure subscriptions associated with the Azure plan
    $AzurePlan
    #GET https://api.partnercenter.microsoft.com/v1/customers/{customer-tenant-id}/subscriptions/{azure-plan-subscription-id}/azureEntitlements
    $AzureEntitlements = Get-PartnerCenterApiItems -AccessToken $token.AccessToken -BaseUri "https://api.partnercenter.microsoft.com/v1/customers/$($customer.id)/subscriptions/$($AzurePlan.id)/azureEntitlements"
    if (!$AzureEntitlements) {
        Write-Host "No Azure entitlements found for subscription $($AzurePlan.id)"
        continue
    }
    
    $tokenSplat = @{
        ApplicationId        = $clientId
        Credential           = $credential
        Scopes               = "https://management.azure.com/user_impersonation"
        ServicePrincipal     = $true
        TenantId             = $customer.id
        UseAuthorizationCode = $true
    }
    
    $TenantToken = New-PartnerAccessToken @tokenSplat

    
    #get permissions of Azure Entitlements
    #GET https://management.azure.com/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01
    foreach ($AzureEntitlement in $AzureEntitlements) {
        $subscriptionId = $AzureEntitlement.id
        $roleAssignments = Get-AzRoleAssignment -Scope $subscriptionId -ApiVersion "2022-04-01"
        $roleAssignments
    }
}


# Optional: Get all customers (uncomment if needed)
# Write-Host "Retrieving all customers from Partner Center..."
# $allCustomers = Get-PartnerCenterApiItems -AccessToken $token.AccessToken -BaseUri "https://api.partnercenter.microsoft.com/v1/customers"
# Write-Host "Retrieved a total of $($allCustomers.Count) customers"

# Optional: Get all indirect resellers (uncomment if needed)
# Write-Host "Retrieving all indirect resellers from Partner Center..."
# $allIndirectResellers = Get-PartnerCenterApiItems -AccessToken $token.AccessToken -BaseUri "https://api.partnercenter.microsoft.com/v1/relationships" -QueryString "?relationship_type=IsIndirectCloudSolutionProviderOf"
# Write-Host "Retrieved a total of $($allIndirectResellers.Count) indirect resellers"
