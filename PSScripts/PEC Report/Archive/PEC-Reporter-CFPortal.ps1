#region Rules
<#

- <PERSON><PERSON><PERSON> must return 
Customername
Subscriptionid (all subscriptions, not azure plan id)
Subscriptionname
Country (secloudfactory)


-test customer 1: with PEC enabled
billetkontoret - partner er supporters
has support request contributor on all subscriptions except 1
Subscription Role assignements visible in azure portal, with only Support Request Contributor

- Test customer 2: without PEC enabled
Jumbo transport

#>
#endregion Rules


#region Functions



function Get-DeviceCodeAuthentication {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$Scope = "https://management.azure.com/.default offline_access"
    )
    
    $deviceCodeUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/devicecode"
    
    $body = @{
        client_id = $ClientId
        scope     = $Scope
    }
    
    try {
        $deviceCodeResponse = Invoke-RestMethod -Uri $deviceCodeUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        
        # Display the message to the user
        Write-Host $deviceCodeResponse.message -ForegroundColor Cyan
        
        # Poll for token
        $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
        $tokenBody = @{
            grant_type    = "device_code"
            device_code   = $deviceCodeResponse.device_code
            client_id     = $ClientId
        }
        
        $interval = $deviceCodeResponse.interval
        $expiresOn = (Get-Date).AddSeconds($deviceCodeResponse.expires_in)
        
        while ((Get-Date) -lt $expiresOn) {
            try {
                Start-Sleep -Seconds $interval
                $tokenResponse = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $tokenBody -ContentType "application/x-www-form-urlencoded"
                return $tokenResponse
            }
            catch {
                if ($_.Exception.Response.StatusCode -ne 400) {
                    throw
                }
                $error = $_.ErrorDetails.Message | ConvertFrom-Json
                if ($error.error -ne "authorization_pending") {
                    Write-Host "Error: $($error.error_description)" -ForegroundColor Red
                    throw 
                }
            }
        }
        
        throw "Authentication timed out"
    }
    catch {
        Write-Error "Error during device code authentication: $_"
        throw
    }
}

function Get-AccessTokenForTargetTenant {
    param (
        [string]$TargetTenantId,
        [string]$ClientId,
        [string]$RefreshToken,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TargetTenantId/oauth2/v2.0/token"
    
    $body = @{
        client_id     = $ClientId
        scope         = $Scope
        refresh_token = $RefreshToken
        grant_type    = "refresh_token"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token for target tenant: $_"
        throw $_
    }
}

function Get-Subscriptions {
    param (
        [string]$AccessToken
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions?api-version=2020-01-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving subscriptions: $_"
        throw
    }
}

function Get-RoleAssignments {
    param (
        [string]$AccessToken,
        [string]$SubscriptionId
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions/$SubscriptionId/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving role assignments: $_"
        throw
    }
}

function Invoke-CFPortalPagedCall {
    param (
        $baseURI,
        $Header = $PortalAPIParams.Header,
        $Endpoint = "/v2/microsoft/tenants"
    )
    $ErrorActionPreference = "Stop" 
    $PageIndex = 1
    $PageSize = 100

    $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$PageSize"


    $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
    $TotalPages = $Result.metadata.totalpages
    $output = @()
    $output += $result.results
    $output += 2..$TotalPages | ForEach-Object -ThrottleLimit 20 -Parallel {
        
        $PageIndex = $PSItem
        $uri = "$($using:baseURI)$($using:Endpoint)?PageIndex=$PageIndex&PageSize=$using:PageSize"
        Write-Host "Getting page $PageIndex"
        $tryCount = 0
        $maxRetries = 3
        $success = $false
        
        while (-not $success -and $tryCount -lt $maxRetries) {
            $tryCount++
            try {
                $startTime = Get-Date
                $Result = Invoke-RestMethod -Uri $uri -Headers $using:header -TimeoutSec 60
                $executionTime = (Get-Date) - $startTime
                Write-Host "Page $PageIndex completed in $($executionTime.TotalSeconds) seconds" -ForegroundColor Cyan
                $success = $true
                $result.results
            }
            catch {
                $errorMessage = $_ | Out-String
                Write-Host "Error on page $PageIndex (Attempt $tryCount of $maxRetries): $errorMessage" -ForegroundColor Red
                if ($tryCount -lt $maxRetries) {
                    Write-Host "Retrying in 3 seconds..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 3
                }
                else {
                        
                    throw "Failed to retrieve page $PageIndex after $maxRetries attempts"
                }
            }
        }
    }

    Return $output
}

#endregion Functions
$refreshtoken = op item get "portal-api-cloudfactory-dk" --fields password --reveal --format json  | ConvertFrom-Json | Select-Object -ExpandProperty value
$baseURI = "https://portal.api.cloudfactory.dk"
$PortalAPIParams = Get-CFPortalAccessToken -RefreshToken $refreshtoken -baseURI $baseURI

# Get all partners
#$Partners = Get-CFPortalPartners @PortalAPIParams
#Write-Host "Retrieved $(($Partners | Measure-Object).Count) partners" -ForegroundColor Green
#
## Get all customers
#$Customers = Get-CFPortalEndCustomers @PortalAPIParams
#Write-Host "Retrieved $(($Customers | Measure-Object).Count) customers" -ForegroundColor Green

#region INIT

# Partner Center Impersonation Script using REST API
# This script authenticates with Microsoft Entra ID and allows switching to another tenant to list subscriptions and role assignments

# Configuration parameters - modify these as needed
# PowerShell's built-in client ID for interactive authentication
$clientId = "1950a258-227b-4e31-a9cf-717495945fc2"  # PowerShell's built-in client ID
$tenantId = "common"  # Use 'common' for multi-tenant authentication or specify your tenant ID
$redirectUri = "urn:ietf:wg:oauth:2.0:oob"  # Standard redirect URI for PowerShell


#region Connect to SQL and get Azure plans

$SQLUser = op item get "Portal SQL - utilityportalro" --vault CF-Admin --fields username,password,IPAddress --reveal --format json  | ConvertFrom-Json


$sqlparams = @{
    Username               = $SQLUser | ? id -eq "Username" | select -ExpandProperty value
    Password               = $SQLUser | ? id -eq "Password" | select -ExpandProperty value
    ServerInstance         = $SQLUser | ? label -eq "IPAddress" | select -ExpandProperty value
    Database               = "UtilityPortal_v2"
    TrustServerCertificate = $true
}
$query = @"
SELECT
	c.[UUID] AS CustomerId,
	c.[Name] AS CustomerName,
	c.PartnerId,
	s.SubscriptionId AS AzurePlanID,
    s.CredentialsId,
    ee.ExternalServiceId as TenantId,
	p.BusinessCentralLink,
	p.[Name] AS PartnerName
FROM
	Microsoft_Subscriptions s
	JOIN UtilityPortal.dbo.EndCustomers_ExternalServiceIds ee ON ee.ExternalServiceId COLLATE Latin1_General_CI_AS = s.TenantId
	JOIN Partners_ExternalServices pe ON pe.ExternalServiceId = s.PartnerMpn
	JOIN Partners p ON p.ID = pe.PartnerID
	JOIN UtilityPortal.dbo.EndCustomers c ON c.Id = ee.EndCustomerId AND c.PartnerId = p.BusinessCentralLink
WHERE
	s.[Sku] = 'DZH318Z0BPS6:0001:DZH318Z0BLRP'
"@
$Azureplans = Invoke-SqlCmd @sqlparams -Query $query 
#endregion region Connect to SQL and get Azure plans

$roles = @(
    @{
        GUID="8e3af657-a8ff-443c-a75c-2fe8c4bcb635"
        Name="Owner"
    },
    @{
        GUID="b24988ac-6180-42a0-ab88-20f7382dd24c"
        Name="Contributor"
    },
    @{
        GUID="cfd33db0-3dd1-45e3-aa9d-cdbdf3b6f24e"
        Name="Support Request Contributor"
    }
)
$adminAgentGroups = @(
    @{
        GUID       = "4e515f36-d3ab-46bb-898e-************"
        Prettyname = "Cloud Factory Denmark"
        CredentialID="DK"
    },
    @{
        GUID       = "877fb66d-052b-48cc-92ac-c6901d5066c5"
        Prettyname = "Cloud Factory Norway"
        CredentialID="NO"
    },
    @{
        GUID       = "224e3ca0-5816-4f6f-af4b-1479894e2f18"
        Prettyname = "Cloud Factory Sweden"
        CredentialID="SE"
    },
    @{
        GUID       = "a14267f9-f4c3-4789-81fd-a019aa02e74b"
        Prettyname = "Cloud Factory BeNeLux"
        CredentialID="BE"
    }
    
)


#initial authentication to Partner Center
Write-Host "Authenticating to home tenant..." -ForegroundColor Cyan
$tokenResponse = Get-DeviceCodeAuthentication -TenantId $tenantId -ClientId $clientId



#endregion INIT


<# Test data
$azureplan =  $Azureplans | Where-Object { $_.CustomerName -eq "CLOUD FACTORY A/S - IaaS" }
$azureplan =  $Azureplans | Where-Object { $_.CustomerName -match "Jumbo Transport" }
$azureplan =  $Azureplans | Where-Object { $_.CustomerName -match "Billetkontoret" }
$azureplan =  $Azureplans | Where-Object { $_.CustomerName -match "Cube Design A/S" }
$azureplans =  $Azureplans | Where-Object { $_.CustomerName -notmatch "levitrax" }
$azureplans =  $Azureplans | Where-Object { $_.CustomerName -match "Alumichem" }
#>



$VerbosePreference="SilentlyContinue"
$VerbosePreference="Continue"
$i=0
$AzurePlansOutput=@()
foreach ($Azureplan in $Azureplans) {
    $ErrorActionPreference = "stop"
    $i++
    try{
        Write-Progress -Activity "Processing Azure plans" -Status "Processing Azure plan $i of $($Azureplans.Count)" -PercentComplete ($i / $Azureplans.Count * 100)
    }catch{ }

    $AzurePlanOutput=[PSCustomObject]@{
        CustomerName = $Azureplan.CustomerName
        AzurePlanqId = $Azureplan.AzurePlanID
        Country = $Azureplan.CredentialsId
        PartnerName = $Azureplan.PartnerName
        SubscriptionOverview=$null #list of subscrriptions. grouped by status. and all active are split in count af PEC eligible and PEC non eligible
        Subscriptions=$null
        Error=$null
    }

    write-host -ForegroundColor blue "Processing Azure plan for endcustomer $($Azureplan.CustomerName)"
    #Get Azure subscriptions associated with the Azure plan
    #/customer/{customerId}/azure/plans/{azurePlanId}/subscriptions
    $uri = "https://portal.api.cloudfactory.dk/v2/microsoft/customer/$($Azureplan.CustomerID)/azure/plans/$($Azureplan.AzurePlanID)/subscriptions"
    try {
        Write-verbose "Getting Azure subscriptions for Azure plan $($Azureplan.AzurePlanID)"
        write-verbose $uri
        $AzureplanSubscriptions = Invoke-RestMethod -Uri $uri -Headers $PortalAPIParams.Header -TimeoutSec 60
        Write-Verbose "Subscriptions Overview: $($AzureplanSubscriptions|Group-Object status | Select-Object count,name |out-string)"
        $AzureplanSubscriptions  = $AzureplanSubscriptions  | Where-Object { $_.status -eq "active" }
    }
    catch {
        $AzureplanSubscriptions=$null
        Write-Host "Error getting Azure subscriptions Customer: $($Azureplan.CustomerName) Azure plan: $($Azureplan.AzurePlanID). Error: $($_|out-string)" -ForegroundColor Red
        $AzurePlanOutput.Error=$_
        $AzurePlansOutput+=$AzurePlanOutput
        continue
    }
    
    try{
    Write-Verbose "Switching context to target tenant:$($azureplan.CustomerName) $($Azureplan.TenantId)" 
    try {
        $targetTokenResponse = Get-AccessTokenForTargetTenant -TargetTenantId $Azureplan.TenantId -ClientId $clientId -RefreshToken $tokenResponse.refresh_token

    }
    catch {
        Write-Warning "Error getting access token for target tenant: $($Azureplan.CustomerName) $($Azureplan.TenantId). Error: $($_|out-string)" 
        $AzurePlanOutput.Error=$_
        $AzurePlansOutput+=$AzurePlanOutput
        continue
    }
        
    Write-Verbose "Target token expires in: $($targetTokenResponse.expires_in) seconds"
    }catch{
        Write-Warning "Error switching context to target tenant: $($Azureplan.CustomerName) $($Azureplan.TenantId). Error: $($_|out-string)" 
        $AzurePlanOutput.Error=$_
        $AzurePlansOutput+=$AzurePlanOutput
        continue
    }
    $SubscriptionOutput=foreach ($AzureplanSubscription in $AzureplanSubscriptions) {
        
        $Output = [PSCustomObject]@{
            
            SubscriptionID = $AzureplanSubscription.id
            SubscriptionName = $AzureplanSubscription.name
            SubscriptionCountry = $AzureplanSubscription.credentialsKey
            SubscriptionStatus = $AzureplanSubscription.status
            PECEligible = $false
            AdminAgentRights=@() # an array of all admin agents and their rights. as pscustomobjects with properties: Country="DK", Role="Owner"
            Error=$null #error output as string
            RoleAssignmentsRAW=$null #raw role assignments output 
            CountryMatch=$false
        }
    
        Write-Verbose "Successfully switched to target tenant." 
        Write-Verbose "Retrieving subscriptions..." 
        try {
            $roleAssignments = Get-RoleAssignments -AccessToken $targetTokenResponse.access_token -SubscriptionId $AzureplanSubscription.id
            $Output.RoleAssignmentsRAW=$roleAssignments 
        }
        catch {
            Write-Warning "Error getting role assignments for subscription $($AzureplanSubscription.id). Error: $($_|out-string)" 
            $roleAssignments=$null
            $Output.Error=$_
            $output
            continue
        }
        
        Write-verbose "Found $($roleAssignments.Count) role assignments:" 
        
        #example of: $CloudFactoryRoleAssignments.properties.roleDefinitionId 
        #/subscriptions/da1c04c5-ea97-4e27-b7e3-00337bc944d7/providers/Microsoft.Authorization/roleDefinitions/cfd33db0-3dd1-45e3-aa9d-cdbdf3b6f24e
        #then -replace "^.+/" (replace everything up until the last /)
        #then we're left with cfd33db0-3dd1-45e3-aa9d-cdbdf3b6f24e

        Write-Verbose "Filtering role assignments..." 
        $CloudFactoryRoleAssignments = $roleAssignments | Where-Object { $_.properties.principalId -in $adminAgentGroups.GUID -and ($_.properties.roleDefinitionId -replace "^.+/") -in $roles.GUID }
        
        foreach ($CloudFactoryRoleAssignment in $CloudFactoryRoleAssignments) {
            $roleGUID=($CloudFactoryRoleAssignment.properties.roleDefinitionId -replace "^.+/")
            $RoleName=$roles | Where-Object { $_.GUID -eq $roleGUID } | Select-Object -ExpandProperty Name
            $AdminAgentGroup=$adminAgentGroups | Where-Object { $_.GUID -eq $CloudFactoryRoleAssignment.properties.principalId } | Select-Object -ExpandProperty CredentialID

            $Output.AdminAgentRights+=[PSCustomObject]@{
                Country = $AdminAgentGroup
                Role = $RoleName
            }
        }

        if ($CloudFactoryRoleAssignments){
            #check county match
            write-Host "Cloud Factory role assignments found for subscription $($AzureplanSubscription.id)" -foregroundcolor green
            $Output.PECEligible=$true
            #get which country admin agent group is assigned
            $AdminAgentGroupsAssigned=$adminAgentGroups | Where-Object { $_.GUID -in $CloudFactoryRoleAssignments.properties.principalId } | Select-Object -Unique

            #Check if the countrys match
            if ($AdminAgentGroupsAssigned.CredentialID -ne $Azureplan.CredentialsId) {
                Write-Host "Country mismatch for subscription $($AzureplanSubscription.id). Admin agent group $($AdminAgentGroupsAssigned.CredentialID -join ", ") does not match country $($Azureplan.CredentialsId)"
                $Output.CountryMatch=$false
            }else{
                $Output.CountryMatch=$true
            }
            
        }
        if(!$Output.AdminAgentRights){
            #pause
        }
        $output
    }

    $AzurePlanOutput.Subscriptions=$SubscriptionOutput

    #group subscriptions by PEC eligible
    $SubscriptionOverview=foreach($Subscription in ($SubscriptionOutput | Group-Object PECEligible)) {
        [PSCustomObject]@{
            PECEligible = $Subscription.Name
            SubscriptionCount = $Subscription.Count
        }
    }
    $AzurePlanOutput.SubscriptionOverview=$SubscriptionOverview
    $AzurePlansOutput+=$AzurePlanOutput
    write-host ($AzurePlanOutput | convertto-json -depth 2)
}


