$FilePath = "/Users/<USER>/Downloads/Detail_BillingProfile_33dd987b-8e7a-49c8-a207-a80c0cc02106_202505_en.csv"
$lines = 0
$reader = [System.IO.File]::OpenText($FilePath)
$Headersraw = $reader.ReadLine()
$Headers = $Headersraw -split ","
while ($reader.ReadLine() -ne $null) { $lines++ }
$reader.Close()


$output = [hashtable]::Synchronized(@{})
#region foreach customer
$hash = [hashtable]::Synchronized(@{})
$hash.I = 0
$LinesPerBatch=10000
$FilteredLines=Get-Content $FilePath -ReadCount $LinesPerBatch | ForEach-Object  -ThrottleLimit 32  -Parallel  { 
    $hash = $using:hash
    $hash.I=$hash.I+$using:LinesPerBatch
    
    try {
        Write-Progress -Activity "Processing file" -Status "Processing line $($hash.I) of $($using:lines)" -PercentComplete ($hash.I / $using:lines * 100)
    }
    catch {
    }
    foreach ($line in $_) {
    
        if ($line -match "6e3f2b83-ed08-4aa5-8ec7-e9bae47f0d02") {
            
            $line
        }
    }
} 


$FilteredObjects=$FilteredLines | convertfrom-csv -Delimiter "," -Header $Headers
$FilteredObjects | select partnerEarnedCreditApplied -Unique


$i=0
$FilteredLines=Get-Content $FilePath -ReadCount $LinesPerBatch | ForEach-Object   { 
$i=$i+$LinesPerBatch
    
    try {
        Write-Progress -Activity "Processing file" -Status "Processing line $($i) of $($lines)" -PercentComplete ($i / $lines * 100)
    }
    catch {
    }
    foreach ($line in $_) {
    
        if ($line -match "6e3f2b83-ed08-4aa5-8ec7-e9bae47f0d02") {
            
            $line
        }
    }
} 