#region Functions
#Get-CosmosDbCFDocuments.ps1
function Get-CosmosDbCFDocuments {
    param (
        $cosmosDbContext,
        $CollectionId = 'vm_logs',
        $documentsPerRequest = 20,
        $query = "",
        $MaxItemCount = 999

    )
    $ErrorActionPreference = 'Stop'

    $continuationToken = $null
    $documents = @()

    do {
        try {
            Write-Progress -Activity "Getting CosmosDB Documents $($cosmosDbContext.account)" -status "Documents: $($documents.count) / $MaxItemCount" -PercentComplete ($documents.count / $MaxItemCount * 100)
        }
        catch {
            
        }

        $responseHeader = $null
        $getCosmosDbDocumentParameters = @{
            Context        = $cosmosDbContext
            CollectionId   = $CollectionId
            MaxItemCount   = $documentsPerRequest
            ResponseHeader = ([ref] $responseHeader)
            query          = $query
        }

        if ($continuationToken) {

            $getCosmosDbDocumentParameters += @{
                ContinuationToken = $continuationToken
            }
        }
        $documents += Get-CosmosDbDocument @getCosmosDbDocumentParameters -QueryEnableCrossPartition:$true 
        $continuationToken = [System.String] $responseHeader.'x-ms-continuation'
        #Write-Verbose $continuationToken 
    } while (-not [System.String]::IsNullOrEmpty($continuationToken) -and $documents.count -lt $MaxItemCount)
    return $documents
}
#endregion Functions

$OPData = op item get "cf-prod-db1" --vault "PSAutomation" --reveal --format json  | ConvertFrom-Json 
$primaryKey=$OPData.fields | Where-Object id -eq "password" | Select-Object -ExpandProperty value |  ConvertTo-SecureString -AsPlainText -Force 
$CosmosDbContext = New-CosmosDbContext -Account 'cf-prod-db1' -Database 'Microsoft' -Key $primaryKey

$SubscriptionId = "8bbb6bef-a409-442e-dbd7-2c6783f5457a"
$Database="Microsoft"
$CollectionID="BilledUsage"
$query="SELECT * FROM $($Database) WHERE $($Database).SubscriptionId = '$SubscriptionId'"
$Documents = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 
