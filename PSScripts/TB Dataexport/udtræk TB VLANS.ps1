$netboxvlans=get-netboxvlans
$NetboxDeviceInterfaces = Get-NetboxDeviceInterfaces
$NetboxPrefixes=get-netboxprefixes
$netboxDevices=get-netboxdevices
$Fortivms=$netboxDevices | ? {$_.device_role.slug -eq "fortigate-vm"}
$CustomerVlans=$netboxvlans| ? {$_.group.id -eq 3 -and $_.role.id -in (1,2)}
$NetboxTenants = Get-NetboxTenants
$VLANSParsed=foreach ($netboxvlan in $CustomerVlans) {

    $VDOMNames=$NetboxDeviceInterfaces | ? {$_.untagged_vlan.vid -eq $netboxvlan.vid } | select -expand vdcs | select -expand name -unique
    $VDOMNames=$VDOMNames -join ","
    $TenantGroup=$NetboxTenants | ? id -eq $netboxvlan.tenant.id | select -expand group
    #VID,Name,Site,Group,Prefixes,Tenant,Status,Role,Description,Tenant Group,Mapped,Mapped from,ID,Comments,Tags,L2VPN,Created,Last updated,Assigned to Customer
    $Prefix=$NetboxPrefixes | ? {$_.vlan.vid -eq $netboxvlan.vid}
    [PSCustomObject]@{
        VLANID=$netboxvlan.vid
        Name=$netboxvlan.name
        VDOMS=$VDOMNames 
        Tenant=$netboxvlan.tenant.name
        TenantGroup=$TenantGroup.name
        prefix=$Prefix.prefix
    }
}

$VLANSParsed | export-csv   -Delimiter ";" -Path "VlansWithVDOMS.csv" -encoding unicode
