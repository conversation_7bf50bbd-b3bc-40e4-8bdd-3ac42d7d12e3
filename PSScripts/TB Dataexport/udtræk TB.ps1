﻿#Requires -Modules CosmosDB
function Convert-ToUnixDate ($PSdate) {
    $epoch = [timezone]::CurrentTimeZone.ToLocalTime([datetime]'1/1/1970')
    (New-TimeSpan -Start $epoch -End $PSdate).TotalSeconds
}


$ErrorActionPreference = "Stop"
Write-Output "Running on: $env:COMPUTERNAME"
   
#Netbox Authentication & Credentials#
$NetboxCredentials = Get-SafeCredentials -CredentialName "NetboxToken"
$NetboxParams = Get-NetboxHeader -Credentials $NetboxCredentials
$Netboxvms = Get-NetboxVMs 
$Netboxvms = $Netboxvms | ? { $_.status.value -eq "active" }

$NBDevices = Get-NetboxDevices
$NBDeviceTypes = Get-NetboxDeviceTypes
$Customers = Get-NetboxTenants
$NBTenantGroups = Get-NetboxTenantGroups
$NBVLANS = Get-NetboxVLANs
$NBPrefixes = Get-NetboxPrefixes
$NBVDCs = Invoke-NBAPI @NetboxParams -Command "dcim/virtual-device-contexts/"

$nutanixVMs = Get-NutanixVMs

$RackUs = $NBDevices | ? { $_.name -ne $null -and $_.tenant.name -ne $null -and $_.device_role.id -ne 18 -and $_.device_role.id -ne 19 }
$FortiVMs = $NBDevices | ? { $_.name -ne $null -and $_.tenant.name -ne $null -and $_.device_role.id -eq 19 }
$FortiVDOMs = $NBDevices | ? { $_.name -ne $null -and $_.tenant.name -ne $null -and $_.device_role.id -eq 18 }
$WANInterfaces = $NBVLANS | ? { $_.role.id -eq 2 }
$FortiPoints = $NBDevices | ? { $_.name -ne $null -and $_.tenant.name -ne $null -and $_.tags.name -contains "fortiflex" }
$fortiVDCs = $NBVDCs | ? { $_.name -ne $null -and $_.tenant.name -ne $null -and $_.custom_fields.decommissioning -ne $true -and $_.name -ne "root" }
#Getting Netbox Interfaces
#$Customer=$Customers | ? display -match "IMS_NTX" | select -first 1
$i = 0

$List = foreach ($Customer in $Customers) {
    $i++
    try {
        Write-Progress -Activity "Making pretty list" -Status $Customer.name -PercentComplete ($i / $Customers.count * 100)
    }
    catch {}
        
        
    #Filter data



    $CustomerRackUs = $RackUs | ? { $_.tenant.id -eq $Customer.id }
    $CustomerFortiVMs = $FortiVMs | ? { $_.tenant.id -eq $Customer.id }
    $CustomerFortiVDOMs = $FortiVDOMs | ? { $_.tenant.id -eq $Customer.id }
    $CustomerFortiVDCs = $fortiVDCs | ? { $_.tenant.id -eq $Customer.id }   
    $CustomerWANInterfaces = $WANInterfaces | ? { $_.tenant.id -eq $Customer.id }
    $CustomerVMs = $Netboxvms | ? { $_.tenant.id -eq $Customer.id }

    $customerFortiPoints = $FortiPoints | ? { $_.tenant.id -eq $Customer.id } 
    #if ($CustomerRackUs){pause}
        
    #prettyup data
    $CustomerRackUsArray = @()
    $CustomerRackUsArray += $CustomerRackUs | select @{n = "Name"; e = { $_.name } }, ID, @{n = "Quantity"; E = { $NBDeviceTypes | ? id -eq $_.device_type.id | select -expand u_height } }
    $CustomerRackUs = $CustomerRackUsArray
        
    $CustomerFortiVMsArray = @()
    $CustomerFortiVMsArray += $CustomerFortiVMs | select @{n = "Name"; e = { $_.name } }, ID, @{n = "SKU"; E = { $_.custom_fields.SKU } }, @{n = "Quantity"; E = { 0 } }
    $CustomerFortiVMs = $CustomerFortiVMsArray
        
    $CustomerFortiVDOMsArray = @()
    $CustomerFortiVDOMsArray += $CustomerFortiVDOMs | select @{n = "Name"; e = { $_.name } }, ID, @{n = "Model"; E = { $_.device_type.model } }
    $CustomerFortiVDOMs = $CustomerFortiVDOMsArray
    
    $CustomerFortiVDCsArray = @()
    $CustomerFortiVDCsArray += $CustomerFortiVDCs | select @{n = "Name"; e = { $_.name } }, ID, @{n = "Device"; E = { $_.device.name } }, @{n = "SKU"; E = { $_.custom_fields.SKU } }
    $CustomerFortiVDCs = $CustomerFortiVDCsArray
        
    $CustomerWANInterfacesArray = @()
    $CustomerWANInterfacesArray += foreach ($CustomerWANInterface in $CustomerWANInterfaces ) {
        $CustomerWANPrefixes = $NBPrefixes | ? { $_.vlan.id -eq $CustomerWANInterface.id }
        foreach ($Prefix in $CustomerWANPrefixes) {
                
            $SubnetMask = $Prefix.Prefix -replace ".+?/"
            $IPCount = [math]::Pow(2, (32 - $SubnetMask))
            

            if ($Prefix.prefix -match "^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/\d{1,2}$") {
                $IPVersion = "IPv4"
            }
            else {
                $IPVersion = "Unknown"
            }
            [pscustomobject]@{
                Name      = $CustomerWANInterface.name
                Id        = $Prefix.Id
                Network   = $Prefix.prefix
                IPVersion = $IPVersion
                Quantity  = $IPCount
            }
        }
    }
    $CustomerWANInterfaces = $CustomerWANInterfacesArray

    $CustomerFortiPointssArray = @()
    $CustomerFortiPointssArray += $customerFortiPoints | select @{n = "Name"; e = { $_.serial } }, ID, @{n = "SKU"; E = { $_.custom_fields.SKU } }, @{n = "Quantity"; E = { $_.custom_fields.FortiFlexPoints } }
    $customerFortiPoints = $CustomerFortiPointssArray
    $tenantgroup = $Customer.group.name
    $tenantgroupid = $Customer.group.slug
    $CustomerNutanixVMs = $NutanixVMs | ? { $_.metadata.uuid -in $CustomerVMs.custom_fields.VM_ID }
    
    $CustomerWindowsVMs = @()
    $CustomerLinuxVMs = @()
    foreach ($CustomerVM in $CustomerVMs) {
        $nutanixvm = $NutanixVMs | ? { $_.metadata.uuid -in $CustomerVM.custom_fields.VM_ID }
        
        if ($nutanixvm | ? { $_.metadata.categories.WindowsInstalled -eq "true" }) {
            #if windows vm.
            $CustomerWindowsVMs += $CustomerVM
        }
        else {
            #if Linux VM
            $CustomerLinuxVMs += $CustomerVM
        }
    }

    $Windows_VCPUPowerONVMs = ($CustomerWindowsVMs | ? { $_.custom_fields.vm_state -eq "on" }).vcpus  | measure -Sum | select -ExpandProperty sum 
    $Windows_MemoryMBPowerONVMs = ($CustomerWindowsVMs | ? { $_.custom_fields.vm_state -eq "on" }).memory  | measure -Sum | select -ExpandProperty sum 
    $Windows_MemoryGBPowerONVMs = $Windows_MemoryMBPowerONVMs / 1024
    $Windows_DiskGBAllVMs = $CustomerWindowsVMs.disk  | measure -Sum | select -ExpandProperty sum 
    $Windows_95pcpurcores = ($CustomerWindowsVMs | ? { $_.custom_fields.CPU_Cores_Used_95p -gt 0 }).custom_fields.CPU_Cores_Used_95p  | measure -Sum | select -ExpandProperty sum 
    $Windows_95pmemoryGB = ($CustomerWindowsVMs | ? { $_.custom_fields.Memory_GiB_Used_95p -gt 0 }).custom_fields.Memory_GiB_Used_95p  | measure -Sum | select -ExpandProperty sum 
    $Windows_95pIOPS = ($CustomerWindowsVMs | ? { $_.custom_fields.IOPS_95p -gt 0 }).custom_fields.IOPS_95p  | measure -Sum | select -ExpandProperty sum 
    
    $Linux_VCPUPowerONVMs = ($CustomerLinuxVMs | ? { $_.custom_fields.vm_state -eq "on" }).vcpus  | measure -Sum | select -ExpandProperty sum 
    $Linux_MemoryMBPowerONVMs = ($CustomerLinuxVMs | ? { $_.custom_fields.vm_state -eq "on" }).memory  | measure -Sum | select -ExpandProperty sum 
    $Linux_MemoryGBPowerONVMs = $Linux_MemoryMBPowerONVMs / 1024
    $Linux_DiskGBAllVMs = $CustomerLinuxVMs.disk  | measure -Sum | select -ExpandProperty sum 
    $Linux_95pcpurcores = ($CustomerLinuxVMs | ? { $_.custom_fields.CPU_Cores_Used_95p -gt 0 }).custom_fields.CPU_Cores_Used_95p  | measure -Sum | select -ExpandProperty sum 
    $Linux_95pmemoryGB = ($CustomerLinuxVMs | ? { $_.custom_fields.Memory_GiB_Used_95p -gt 0 }).custom_fields.Memory_GiB_Used_95p  | measure -Sum | select -ExpandProperty sum 
    $Linux_95pIOPS = ($CustomerLinuxVMs | ? { $_.custom_fields.IOPS_95p -gt 0 }).custom_fields.IOPS_95p  | measure -Sum | select -ExpandProperty sum 
    
    $RackUsString = (($CustomerRackUs | % { "$($_.name)-$($_.Quantity)" }) -join ",")
    $MPLS = $RackUsString -match "\(MPLS\)"
    $FortiVDOMsString = $CustomerFortiVDCs.sku -join ","
    [PSCustomObject]@{
        TenantGroupID                  = $tenantgroupid
        TenantGroup                    = $tenantgroup
        TenantDescription              = $Customer.description
        
        Name                           = $Customer.name
        Customername                   = $Customer.custom_fields.CustomerName
        CustomerActive                 = $Customer.custom_fields.CustomerActive
        RackU                          = $RackUsString
        FortiFlexPointDailyConsumption = $customerFortiPoints.Quantity | measure -Sum | select -ExpandProperty sum 
        FortiVDOMs                     = $FortiVDOMsString
        IPAddresses                    = $CustomerWANInterfaces.quantity | measure -Sum | select -ExpandProperty sum 
        MPLS                           = $MPLS
        
        "Windows_VMs"                  = $CustomerWindowsVMs | measure | select -ExpandProperty count
        "Windows_VCPUs_ON"             = $Windows_VCPUPowerONVMs
        "Windows_MemoryGB_ON"          = $Windows_MemoryGBPowerONVMs
        "Windows_DiskGB_ALL"           = $Windows_DiskGBAllVMs
        "Windows_95pCPUCores"          = $Windows_95pcpurcores
        "Windows_95pMemoryGB"          = $Windows_95pmemoryGB
        "Windows_95pIOPS"              = $Windows_95pIOPS

        "Linux_VMs"                    = $CustomerLinuxVMs | measure | select -ExpandProperty count
        "Linux_VCPUs_ON"               = $Linux_VCPUPowerONVMs
        "Linux_MemoryGB_ON"            = $Linux_MemoryGBPowerONVMs
        "Linux_DiskGB_ALL"             = $Linux_DiskGBAllVMs
        "Linux_95pCPUCores"            = $Linux_95pcpurcores
        "Linux_95pMemoryGB"            = $Linux_95pmemoryGB
        "Linux_95pIOPS"                = $Linux_95pIOPS

    }
}


#$List | select {$_.rackup.quantity}
#$List| ? racku| select TenantName,{$_.racku.Quantity | measure -Sum |select -ExpandProperty sum } 
#($list | ? FortiFlexPoint).FortiFlexPoint
#$list | sort -Descending vms | select -first 10 | ft

$list | ConvertTo-Csv -Delimiter ";" -NoTypeInformation | clip
$list | select -expand TenantGroup | clip
$list | group tenantgroup | sort -Descending count | select -expand name | clip
$list | group tenantgroup 
$list | ? customername


#udtræk 23/4. Depends on @cosmodvmram.ps1
#$AndersDate=get-date "23-04-2025 00:00:00"
#$VMs2304 = $documents | ? {$_.date.Date -eq $AndersDate} | select -ExpandProperty vms
#foreach ($group in $list | group tenantdescription)
#{
#    $TenantsToInclude=$group.group.name
#    $VMsToReport=$VMs2304 | ? {$_.metadata.project_reference.name -in $TenantsToInclude -and $_.status.resources.power_state -eq "ON"}
#    $SumMemoryMB=$VMsToReport.spec.resources.memory_size_mib | Measure-Object -Sum | select -ExpandProperty sum
#    $SumMemoryGB=$SumMemoryMB / 1024
#    $MemoryGB=$group.Group.Windows_MemoryGB_ON + $group.Group.Linux_MemoryGB_ON | measure -Sum | select -ExpandProperty sum
#    [PSCustomObject]@{
#        Group = $group.name
#        MemoryGB=$MemoryGB
#        MemoryGB23april=$SumMemoryGB
#    }
#    
#}