$netboxDevices = Get-NetboxDevices
$NetboxVMInterfaces = Get-NetboxVMsInterfaces
$netboxvms = Get-NetboxVMs
$NetboxTenants=Get-NetboxTenants
$nutanixvms=Get-NutanixVMs
$nutaniximages=Get-NutanixImages

$Vms = foreach ($netboxvm in $netboxvms) {
    $Interfaces = $NetboxVMInterfaces | Where-Object { $_.virtual_machine.id -eq $netboxvm.id }
    $IPAddresses = try { $netboxvm.primary_ip.address -join "," } catch { "" }
    $MACAddresses = $Interfaces | Select-Object -ExpandProperty mac_address -Unique
    $nutanixvm = $nutanixvms | Where-Object { $_.metadata.uuid -eq $netboxvm.custom_fields.VM_ID } 
    [PSCustomObject]@{
        PartnerID = ($NetboxTenants | Where-Object { $_.id -eq $netboxvm.tenant.id }).group.id
        PartnerName = ($NetboxTenants | Where-Object { $_.id -eq $netboxvm.tenant.id }).group.name
        Tenant = $netboxvm.tenant.name
        Name = $netboxvm.name
        IP =  $IPAddresses  -join ","
        NIC_MAC_Address = $Interfaces.mac_address -join ","
        vCore = $netboxvm.vcpus
        vRAM = $netboxvm.memory
        Windows =  $nutanixvm.metadata.categories.WindowsInstalled
        SourceImage=$netboxvm.custom_fields.Source_Image
        DiskUsedGB = $netboxvm.custom_fields.Disk_Usage_GB
        DiskProvisionedGB = $netboxvm.disk
        PowerState = $netboxvm.custom_fields.VM_STATE
    }
}

$Vms | Export-Csv -Delimiter ";" -Path "C:\Users\<USER>\Cloud Factory A-S IUL Dropbox\Lars Arnth Jessen\VMsWithDetails.csv" -Encoding Unicode