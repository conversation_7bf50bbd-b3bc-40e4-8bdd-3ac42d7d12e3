$PartnerAccounts=Get-CFPortalAccounts -AccountType partner
$PartnerAccounts| ? {$_.roles | ? name -eq "Infrastructure"} | select name,email,{$_.roles.name -join ", "} | Export-Csv "Partnerlist.csv" -Delimiter ";" -Encoding unicode

$PartnerAccounts| ? {$_.roles | ? name -eq "Infrastructure"}  | group partnerid | measure


$IgnorePartnerList=("Levitrax AI ApS","We got your Back","itm8 | JDM A/S","Advania Danmark A/S")
$TenantgroupsList = $list | group tenantgroup | select name,@{Name='PartnerID'; Expression={$_.group.tenantgroupid | select -Unique}} 

#all users from all partners that are in the list 
$CSVOutput=foreach ($partneraccount in $PartnerAccounts | ? partnerid -in $list.tenantgroupid )
{
    #$partneraccount | Get-CFPortalUsers | ? {$_.roles | ? name -eq "Infrastructure"} | select name,email,@{Name='PartnerID'; Expression={$partneraccount.partnerid}}
    $Partnername = $TenantgroupsList | ? partnerid -eq $partneraccount.partnerid | select -ExpandProperty Name

    if ($partnername -in $IgnorePartnerList) {
        write-host "Skipping $partneraccount in ignore list"

        continue
    }
    #$partneraccount | select name,email,@{Name='Portal Roles'; Expression={$_.roles.name -join ", "}}
    [PSCustomObject]@{
        Name = $partneraccount.name
        Email = $partneraccount.email
        Partner = $Partnername
        PortalRoles = $partneraccount.roles.name -join ", "
    }
}

$CSVOutput| Export-Csv "Partnerlist.csv" -Delimiter ";" -Encoding unicode

