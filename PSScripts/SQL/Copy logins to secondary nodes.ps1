install-module DBATools 


$AGLSN = 'AGUPBifrost'
Set-DbatoolsInsecureConnection -SessionOnly
$primaryReplica =    Get-DbaAgReplica -SqlInstance $AGLSN | Where-Object Role -eq Primary
$secondaryReplica = Get-DbaAgReplica -SqlInstance $AGLSN | Where-Object Role -eq Secondary
     
$LoginsOnPrimary = (Get-DbaLogin -SqlInstance $primaryReplica[0].Name)
      
$LoginsOnSecondary = (Get-DbaLogin -SqlInstance $secondaryReplica[0].Name)
     
$diff = $LoginsOnPrimary | Where-Object Name -notin ($LoginsOnSecondary.Name)
if($diff) {
    Copy-DbaLogin -Source $primaryReplica[0].Name -Destination $secondaryReplica[0].Name #-Login $diff.Nane
}   

