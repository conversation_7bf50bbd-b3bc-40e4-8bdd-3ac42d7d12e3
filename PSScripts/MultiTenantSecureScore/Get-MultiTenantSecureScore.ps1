# Partner Center Impersonation Script using REST API
# This script authenticates with Microsoft Entra ID and allows switching to another tenant to list subscriptions and role assignments

# Configuration parameters - modify these as needed
# PowerShell's built-in client ID for interactive authentication
$clientId = "1950a258-227b-4e31-a9cf-717495945fc2"  # PowerShell's built-in client ID
$tenantId = "common"  # Use 'common' for multi-tenant authentication or specify your tenant ID
$redirectUri = "urn:ietf:wg:oauth:2.0:oob"  # Standard redirect URI for PowerShell

# Target tenant to impersonate/access - uncomment the one you want to use
$targetTenantId = "a032445b-08b9-42b9-8fae-6d8ea90723e0"
# $targetTenantId = "98ae562d-ed37-48b0-a9ca-d48df78c56c0"
# $targetTenantId = "f57aa6c3-5ecf-4771-822c-bf40f54c5acf"
$targetTenantId="a032445b-08b9-42b9-8fae-6d8ea90723e0" #iaas
$targetTenantId="ad926c03-4b20-4cfa-9353-f896a61b0d25" #se cloudfactory
$targetTenantId="5ec72b35-871e-4a85-8923-bcdfce5c9ef0" #levitrax
function Get-DeviceCodeAuthentication {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$Scope = "https://management.azure.com/.default offline_access"
    )
    
    $deviceCodeUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/devicecode"
    
    $body = @{
        client_id = $ClientId
        scope     = $Scope
    }
    
    try {
        $deviceCodeResponse = Invoke-RestMethod -Uri $deviceCodeUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        
        # Display the message to the user
        Write-Host $deviceCodeResponse.message -ForegroundColor Cyan
        
        # Poll for token
        $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
        $tokenBody = @{
            grant_type    = "device_code"
            device_code   = $deviceCodeResponse.device_code
            client_id     = $ClientId
        }
        
        $interval = $deviceCodeResponse.interval
        $expiresOn = (Get-Date).AddSeconds($deviceCodeResponse.expires_in)
        
        while ((Get-Date) -lt $expiresOn) {
            try {
                Start-Sleep -Seconds $interval
                $tokenResponse = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $tokenBody -ContentType "application/x-www-form-urlencoded"
                return $tokenResponse
            }
            catch {
                if ($_.Exception.Response.StatusCode -ne 400) {
                    throw
                }
                $error = $_.ErrorDetails.Message | ConvertFrom-Json
                if ($error.error -ne "authorization_pending") {
                    Write-Host "Error: $($error.error_description)" -ForegroundColor Red
                    throw
                }
            }
        }
        
        throw "Authentication timed out"
    }
    catch {
        Write-Error "Error during device code authentication: $_"
        throw
    }
}

function Get-AccessTokenForTargetTenant {
    param (
        [string]$TargetTenantId,
        [string]$ClientId,
        [string]$RefreshToken,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TargetTenantId/oauth2/v2.0/token"
    
    $body = @{
        client_id     = $ClientId
        scope         = $Scope
        refresh_token = $RefreshToken
        grant_type    = "refresh_token"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token for target tenant: $_"
        throw
    }
}

function Get-Subscriptions {
    param (
        [string]$AccessToken
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions?api-version=2020-01-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving subscriptions: $_"
        throw
    }
}

function Get-RoleAssignments {
    param (
        [string]$AccessToken,
        [string]$SubscriptionId
    )

    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }

    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions/$SubscriptionId/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving role assignments: $_"
        throw
    }
}

function Get-SecureScore {
    param (
        [string]$AccessToken
    )

    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }

    try {
        $response = Invoke-RestMethod -Uri "https://graph.microsoft.com/v1.0/security/secureScores?`$top=1" -Method Get -Headers $headers

        if (-not $response.value -or $response.value.Count -eq 0) {
            Write-Warning "No security score data found"
            return $null
        }

        # Return the latest secure score
        return $response.value[0]
    }
    catch {
        Write-Error "Error retrieving secure score: $_"
        throw
    }
}

function Get-SecurityContact {
    param (
        [string]$AccessToken=$targetTokenResponse.access_token
    )

    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }

    try {
        $response = Invoke-RestMethod -Uri "https://graph.microsoft.com/v1.0/organization" -Method Get -Headers $headers

        if (-not $response.value -or $response.value.Count -eq 0) {
            Write-Warning "No organization data found"
            return $null
        }

        # Get the first (and typically only) organization
        return $response.value[0]
    }
    catch {
        Write-Error "Error retrieving security contact information: $_"
        throw
    }
}

# Main script execution
try {
    Write-Host "Authenticating to home tenant..." -ForegroundColor Cyan
    $tokenResponse = Get-DeviceCodeAuthentication -TenantId $tenantId -ClientId $clientId

    Write-Host "Successfully authenticated to home tenant." -ForegroundColor Green
    Write-Host "Switching context to target tenant: $targetTenantId" -ForegroundColor Cyan

    # Get Microsoft Graph token for secure score API
    $targetTokenResponse = Get-AccessTokenForTargetTenant -TargetTenantId $targetTenantId -ClientId $clientId -RefreshToken $tokenResponse.refresh_token -Scope "https://graph.microsoft.com/.default"

    Write-Host "Successfully switched to target tenant." -ForegroundColor Green

    # Retrieve security contact information
    Write-Host "Retrieving security contact information..." -ForegroundColor Cyan
    $securityContact = Get-SecurityContact -AccessToken $targetTokenResponse.access_token

    if ($securityContact) {
        Write-Host "Security Contact Information:" -ForegroundColor Green
        Write-Host "Organization: $($securityContact.OrganizationDisplayName)" -ForegroundColor White
        Write-Host "Organization ID: $($securityContact.OrganizationId)" -ForegroundColor White

        if ($securityContact.SecurityComplianceNotificationMails -and $securityContact.SecurityComplianceNotificationMails.Count -gt 0) {
            Write-Host "Security Compliance Notification Emails:" -ForegroundColor Yellow
            foreach ($email in $securityContact.SecurityComplianceNotificationMails) {
                Write-Host "  - $email" -ForegroundColor White
            }
        } else {
            Write-Host "Security Compliance Notification Emails: Not configured" -ForegroundColor Red
        }

        if ($securityContact.SecurityComplianceNotificationPhones -and $securityContact.SecurityComplianceNotificationPhones.Count -gt 0) {
            Write-Host "Security Compliance Notification Phones:" -ForegroundColor Yellow
            foreach ($phone in $securityContact.SecurityComplianceNotificationPhones) {
                Write-Host "  - $phone" -ForegroundColor White
            }
        } else {
            Write-Host "Security Compliance Notification Phones: Not configured" -ForegroundColor Red
        }

        if ($securityContact.TechnicalNotificationMails -and $securityContact.TechnicalNotificationMails.Count -gt 0) {
            Write-Host "Technical Notification Emails:" -ForegroundColor Yellow
            foreach ($email in $securityContact.TechnicalNotificationMails) {
                Write-Host "  - $email" -ForegroundColor White
            }
        } else {
            Write-Host "Technical Notification Emails: Not configured" -ForegroundColor Red
        }

        if ($securityContact.BusinessPhones -and $securityContact.BusinessPhones.Count -gt 0) {
            Write-Host "Business Phones:" -ForegroundColor Yellow
            foreach ($phone in $securityContact.BusinessPhones) {
                Write-Host "  - $phone" -ForegroundColor White
            }
        } else {
            Write-Host "Business Phones: Not configured" -ForegroundColor Red
        }
    } else {
        Write-Host "No security contact information found." -ForegroundColor Red
    }

    Write-Host "Retrieving secure score..." -ForegroundColor Cyan
    # You can add secure score retrieval here if needed
    # $secureScore = Get-SecureScore -AccessToken $targetTokenResponse.access_token

}
catch {
    Write-Error "An error occurred: $_"
}