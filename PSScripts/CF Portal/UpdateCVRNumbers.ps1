$baseURI = "https://portal.api.cloudfactory.dk"
$refreshtoken = op item get "portal-api-cloudfactory-dk" --fields password --reveal --format json  | ConvertFrom-Json | Select-Object -ExpandProperty value
$PortalParams = Get-CFPortalAccessToken -RefreshToken $refreshtoken -baseURI $baseURI
$Partners = Get-CFPortalPartners @PortalParams

#find partner using name from variable
$Partner = $Partners | Where-Object { $_.Name -match "Infowise" }

#validate that we found 1 partner. Else throw.
if (($Partner | Measure-Object).Count -ne 1) {
    throw "Found $($Partner.Count) partners with name 'Infowise'. Expected 1."
}

#Get customers for partner
 $Customers= Get-CFPortalEndCustomers @PortalParams -PartnerID $Partner.debitorId

 $cvrdata=import-csv -delimiter ";" -Path "/Users/<USER>/Cloud Factory A-S IUL Dropbox/Lars Arnth Jessen/kunder-cvr-nummer-cloudfactory.csv" 

foreach ($customer in $Customers) {


    #Lookup CVR number for customer
    $CustomerCVR=$cvrdata | Where-Object {$_.CFCustomerID -eq $customer.id} 

    #if no cvr number found, write warning and continue
    if ($CustomerCVR.cvr -eq $null) {
        Write-Warning "No CVR number found for customer $($customer.name)"
        continue
    }
    #Check if CVR is equal
    if ($customer.vatId -eq $CustomerCVR.cvr) {
        Write-Host -ForegroundColor DarkBlue "CVR number for customer $($customer.name) is already correct" 
        
        continue
    }

    # Check if CVR number has a DK prefix and remove it
    #$cvrNumber = $CustomerCVR.cvr.Trim()
    #if ($cvrNumber -match '^DK') {
    #    $originalCVR = $cvrNumber
    #    $cvrNumber = $cvrNumber -replace '^DK', ''
    #    Write-Host "Removed DK prefix from CVR number: $originalCVR → $cvrNumber" -ForegroundColor Yellow
    #    $CustomerCVR.cvr = $cvrNumber
    #}

    write-host  -ForegroundColor Green "Ready to update CVR number for customer $($customer.name) to $($CustomerCVR.cvr)"

    # Create a copy of the customer object to modify
    $customerUpdate = $customer | ConvertTo-Json -Depth 10 | ConvertFrom-Json
    
    # Update the vatId (CVR number)
    $customerUpdate.vatId = $CustomerCVR.cvr

    # Convert to JSON for the API request
    $jsonBody = $customerUpdate | ConvertTo-Json -Depth 10 
    
    # Set up the API endpoint URL
    $updateUrl = "$baseURI/v2/customers/Customers/$($customer.id)"
    
    # Display customer details for verification
    Write-Host "----- Customer Details -----" -ForegroundColor Cyan
    Write-Host "Name: $($customer.name)" -ForegroundColor Yellow
    Write-Host "ID: $($customer.id)" -ForegroundColor Yellow
    Write-Host "Current VatId: $($customer.vatId)" -ForegroundColor Yellow
    Write-Host "New VatId to set: $($CustomerCVR.cvr)" -ForegroundColor Yellow
    Write-Host "---------------------------" -ForegroundColor Cyan
    

    
    Write-Host "Proceeding with update..." -ForegroundColor Cyan
    
    try {
        # Make the PUT request to update the customer
        #add content-type application/json to header
        $PortalParams.header['Content-Type'] = 'application/json'
        $updateResponse = Invoke-RestMethod -Uri $updateUrl -Method Put -Headers $PortalParams.header -Body $jsonBody
        
        Write-Host -ForegroundColor Green "Successfully updated CVR number for customer $($customer.name) to $($CustomerCVR.cvr)"
    }
    catch {
        Write-Error "Failed to update CVR number for customer $($customer.name): $_"
    }
}

#list 