function Convert-ToUnixDate ($PSdate) {
    $epoch = [timezone]::CurrentTimeZone.ToLocalTime([datetime]'1/1/1970')
    (New-TimeSpan -Start $epoch -End $PSdate).TotalSeconds
}


$primaryKey = ConvertTo-SecureString -AsPlainText -Force -String 'o6qXsiNF9YrtElmE7zfyCbdr54Q7tbVWlf9blJHJwniNupsQV2Qg2jE65l5FR7lxaBagr0JjJ4hsACDbHkU9iA=='

if ($cosmosDbContext -eq $null) {
    $backoffPolicy = New-CosmosDbBackoffPolicy -MaxRetries 10 -Method Additive -Delay 1000
    $cosmosDbContext = New-CosmosDbContext -Account 'strozzisqldb' -Database 'DK01' -Key $primaryKey -BackoffPolicy $backoffPolicy
}

#.vms.metadata.project_reference.uuid
$ProjectsToinclude=@(
    "5fa14820-bc63-4f2d-8afd-2f11cc7bd31c"
    "7546f25f-c125-4b74-9614-c1e6a4645455"
    "317c4a61-cc28-4277-a6bf-589c91ef3b03"
    "e4e98e80-a3a2-4adb-abd9-986b3ebc67fe"
    "68ce85c9-ae97-4f0a-91f5-5f09b6378fb2"
)


do {
[int]$jobStartTimeEpochGT = Convert-ToUnixDate("2025-06-05")
[int]$jobStartTimeEpochLT = Convert-ToUnixDate(get-date)
$query = "SELECT * FROM c WHERE c.JobStartTimeEpoch > $jobStartTimeEpochGT AND c.JobStartTimeEpoch < $jobStartTimeEpochLT"


$query = @"
SELECT * FROM c 
"@
$documentsPerRequest = 24000
$continuationToken = $null
$documents = @()
do {
    $responseHeader = $null
    $getCosmosDbDocumentParameters = @{
        Context = $cosmosDbContext
        CollectionId = 'ntx_vm_logs'
        MaxItemCount = $documentsPerRequest
        ResponseHeader = ([ref] $responseHeader)
        Query = $query
        QueryEnableCrossPartition = $true
    }

    if ($continuationToken) {
        $getCosmosDbDocumentParameters.ContinuationToken = $continuationToken
    }
    $retryAfter = if($responseHeader.'x-ms-retry-after-ms'){$responseHeader.'x-ms-retry-after-ms'}else{2000} # fallback, 2 sekunder
        try {
            $documents += Get-CosmosDbDocument @getCosmosDbDocumentParameters
            $continuationToken = Get-CosmosDbContinuationToken -ResponseHeader $responseHeader

           # Start-Sleep -Milliseconds $retryAfter
        } catch {
                $retryAfter = if($responseHeader.'x-ms-retry-after-ms'){$responseHeader.'x-ms-retry-after-ms'}else{4000}
                Write-Host "Exception 429, retrying after $retryAfter ms..."
                Start-Sleep -Milliseconds $retryAfter
        }   

} while (-not [System.String]::IsNullOrEmpty($continuationToken))



$groupedDocuments = $documents | Group-Object { $_.date.Date } |  ForEach-Object {
    $group=$_.Group 
    $vms=$group.vms
    $TietoGroupVMs=$vms | Where-Object {$_.metadata.project_reference.uuid -in $ProjectsToinclude -and $_.status.resources.power_state -eq "ON"}
    $cpuCount=$TietoGroupVMs | ForEach-Object {$_.spec.resources.num_sockets * $_.spec.resources.num_vcpus_per_socket}
    $cpuCount=$cpuCount | Measure-Object -sum | Select-Object -ExpandProperty sum
    $ramCount=$TietoGroupVMs.spec.resources.memory_size_mib | Measure-Object -sum | Select-Object -ExpandProperty sum
    [pscustomobject]@{
        Date = $_.Name
        TotalVMCount = $TietoGroupVMs.count
        TotalVMRAMCount = $ramCount /1024
        TotalCpuCount = $cpuCount
    }
}


$groupedDocuments | Sort-Object Date -descending 



