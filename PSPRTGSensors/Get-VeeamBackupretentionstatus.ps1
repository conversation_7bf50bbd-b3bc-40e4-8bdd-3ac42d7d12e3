$ErrorActionPreference="Stop"
New-PRTGOutput

try{
    #$password = ConvertTo-SecureString "PZD6Rm%Cg#faGqz" -AsPlainText -Force
    #$credential = New-Object System.Management.Automation.PSCredential ("postgres", $password)
    $credential = Get-SafeCredentials -CredentialName veeampostgres

    $sc = {

    if(!(Get-Module SimplySql -ListAvailable)){
        Install-Module SimplySql -Scope CurrentUser -Force
    }
    Import-Module SimplySql


    $postgresql = Open-PostGreConnection -Server "localhost" -Port "5432"  -Database "VeeamBackup" -Credential $using:credential 


    $query = @"
    select qq.backup_id,
		    o.object_id as VM_UUID,
		    qq.display_name,
		    qq.first_backup_date,
		    qq.last_backup_date,
		    qq.restore_points,
		    backups.backup_policy_name
		    FROM( SELECT q.backup_id,
                q.object_id,
			    q.display_name,
                min(q.creation_time) AS first_backup_date,
			    max(q.creation_time) AS last_backup_date,
                max(q.rn) AS restore_points
               FROM ( SELECT oibs.id,
                        oibs.object_id,
                        pnts.backup_id,
                        oibs.creation_time,
				 	    oibssen.display_name,
                        row_number() OVER (PARTITION BY pnts.backup_id, oibs.object_id ORDER BY pnts.num DESC, oibs.creation_time DESC) AS rn
                       FROM "backup.model.oibs" oibs
				 	     JOIN "backup.model.oibssensitiveinfo" oibssen on oibs.id = oibssen.oib_id
                         JOIN "backup.model.points" pnts ON pnts.id = oibs.point_id
                      WHERE oibs.is_corrupted = false and oibs.type != 4) q
              GROUP BY q.backup_id, q.object_id, q.display_name 
		      ) qq 
		      JOIN "backup.model.backups" backups on qq.backup_id = backups.id
		      join 	bobjects o on o.id = qq.object_id
		      order by last_backup_date desc
		  
"@


        Invoke-SqlQuery -Query $query
    }

    $concred = Get-SafeCredentials -CredentialName SerPSROT

    $retentionstatus = Invoke-Command -ComputerName "dk01vbr01.iaas.cloudfactory.dk" -ScriptBlock $sc -Credential $concred
    $ignoreids =@(
        "0e2caa44-51b8-4aea-aef7-6acaf367b737",
        "d794e44e-16fa-4231-8c02-407214f32bfe",
        "bf73e4e4-23ae-4d32-a925-f2260a695c59",
        "faab67e3-cfd8-416d-aca8-ff326cfc0753",
        "6937d226-edd9-4196-bac8-4f2d0d51fca9",
        "2ce971c2-95b3-48c2-b1c0-76c0760da7cd"
    )
    $vms = Get-NutanixVMs | where {$_.metadata.uuid -notin $ignoreids -and $_.spec.name -notmatch "-MoveTest$" -and $_.metadata.categories_mapping.VeeamBackup -notcontains "CF" -and $_.metadata.categories_mapping.VeeamBackup -notcontains "JDM"}
    $formatedvms = $vms |select @{n="uuid";e={$_.metadata.uuid}},@{n="ClusterName";e={$_.status.cluster_reference.name}},@{n="name";e={$_.spec.name}},@{n="Snapshot";e={$_.metadata.categories_mapping.Snapshot}},@{n="creation_time";e={$_.metadata.creation_time}}

    $chunks = @()
    for ($i = 0; $i -lt $retentionstatus.Count; $i += 100) {
        $endIndex = [Math]::Min($i + 99, $retentionstatus.Count - 1)  # Ensure we do not go out of bounds
        $chunks += [pscustomobject]@{
            name = "chunkstart $i"
            objects = $retentionstatus[$i..$endIndex]
        }
    }
    $jobs= @()
    $i = 1
    foreach($chunk in $chunks){
        $jobs += Start-Job -Name "Chunk $i" -ScriptBlock {$using:chunk.objects |select Last_backup_date,vm_uuid, @{n="Nutanixvm";e={$obj = $_;$using:formatedvms |where {$obj.vm_uuid -eq $_.uuid} }},@{n="TimespanRetentionDiff";e={$_.restore_points - (New-TimeSpan $_.first_backup_date $_.last_backup_date).Days}}}
        $i++
    }
    $backedupVMS = get-job -Name "Chunk *" |  Receive-Job -Wait -AutoRemoveJob

    $grouped = $backedupVMS | group {$_.nutanixvm.uuid} 
    $nonNutanixBackupedVMS = $grouped | where name -eq "" |select -ExpandProperty group
    $NutanixBackupedVMS = $grouped | where name -ne "" |foreach{ $_.Group | sort Last_backup_date -Descending |select -First 1 }

    $missingVM = $vms |where {$NutanixBackupedVMS.vm_uuid -notcontains $_.metadata.uuid}


    $nutanixlast24hours = $NutanixBackupedVMS  | where {$_.last_backup_date -gt (get-date).AddDays(-1).Date -and $_.nutanixvm.Snapshot -ne "NO_PROTECTION" -and $_.nutanixvm.name -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))|(DK\d{2})NTXC(\d{2})" } | measure
    $nutanixlastolderthan1dayvms= $NutanixBackupedVMS  | where {$_.last_backup_date -lt (get-date).AddDays(-1).Date -and $_.last_backup_date -gt (get-date).AddDays(-30).Date  -and $_.nutanixvm.Snapshot -ne "NO_PROTECTION" -and $_.nutanixvm.name -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))|(DK\d{2})NTXC(\d{2})"}
    $nutanixlastolderthan1day= $nutanixlastolderthan1dayvms |measure 
    $nutanixlastolderthan30days = $NutanixBackupedVMS  | where {$_.last_backup_date -lt  (get-date).AddDays(-30).Date -and $_.nutanixvm.Snapshot -ne "NO_PROTECTION" -and $_.nutanixvm.name -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))|(DK\d{2})NTXC(\d{2})"} | measure

    $last24hours = $backedupVMS  | where {$_.last_backup_date -gt (get-date).AddDays(-1).Date } | measure
    $lastolderthan1day = $backedupVMS  | where {$_.last_backup_date -lt (get-date).AddDays(-1).Date -and $_.last_backup_date -gt (get-date).AddDays(-30).Date} | measure
    $lastolderthan30days = $backedupVMS  | where last_backup_date -lt  (get-date).AddDays(-30).Date | measure
    $missingVms = $missingVM | where {$_.metadata.categories_mapping.Snapshot -ne "NO_PROTECTION" -and $_.spec.name -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))|(DK\d{2})NTXC(\d{2})" -and (get-date $_.metadata.creation_time) -lt (get-date).AddDays(-1).Date} |measure
    $excludedVms = $missingVM | where {$_.metadata.categories_mapping.Snapshot -eq "NO_PROTECTION" -or $_.spec.name -match "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))|(DK\d{2})NTXC(\d{2})"} |measure
    $createdToday = $missingVM | where {(get-date $_.metadata.creation_time) -gt (get-date).AddDays(-1).Date -and $_.spec.name -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))|(DK\d{2})NTXC(\d{2})"} |measure
    

    Add-PRTGOutputChannel -Name "Nutanix VMs Today"  -Value $nutanixlast24hours.count -Unit Count
    Add-PRTGOutputChannel -Name "Nutanix VMs 1-30 days"  -Value $nutanixlastolderthan1day.count -Unit Count
    Add-PRTGOutputChannel -Name "Nutanix VMs +30 days"  -Value $nutanixlastolderthan30days.count -Unit Count
    Add-PRTGOutputChannel -Name "VMs Today"  -Value $last24hours.count -Unit Count
    Add-PRTGOutputChannel -Name "VMs 1-30 days"  -Value $lastolderthan1day.count -Unit Count
    Add-PRTGOutputChannel -Name "VMs +30 days"  -Value $lastolderthan30days.count -Unit Count
    Add-PRTGOutputChannel -Name "Nutanix VMs Missing in backup"  -Value $missingVms.count -Unit Count
    Add-PRTGOutputChannel -Name "Nutanix VMs Excluded from Backup"  -Value $excludedVms.count -Unit Count
    Add-PRTGOutputChannel -Name "Nutanix VMs Created Today"  -Value $createdToday.count -Unit Count

    foreach ($clusterGroup in ($nutanixlastolderthan1dayvms.nutanixvm | group clustername)){
        Add-PRTGOutputChannel -Name "$($clusterGroup.Name) - Nutanix VMs 1-30 days" -Value $clusterGroup.Count -Unit Count
    }
    #if ($nutanixlastolderthan1dayvms){
    #    $VMsString=$nutanixlastolderthan1dayvms.Nutanixvm.name -join ", "
    #    Add-PRTGOutputMessage -Message $VMsString
    #}
    }
catch{
     Add-PRTGOutputMessage -Message ($_ | Out-String) -SetError
}
Get-PRTGOutput