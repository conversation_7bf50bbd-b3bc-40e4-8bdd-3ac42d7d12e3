﻿try{    
    
    $ErrorActionPreference="Stop"

    #Create New PRTGOutput
    New-PRTGOutput
    
    #Authentication
    $ignoreVMnames = @(
    "DK01RESTORE04"
    "DK01NTXC01-PROXYWorker01"
    "DK01NTXC02-PROXYWorker01"
    "DK01NTXC03-PROXYWorker01"
    "DK01NTXC05-PROXYWORKER01"
    "DK01NTXC05-PROXYWORKER02"
    "DK01NTXC05-PROXYWORKER03"
    "DK01NTXC05-PROXYWORKER04"
    "DK01PC01C01-PROXY01"
    "DK01PC01C02-PROXY01"
    "DK01PC01C03-PROXY01"
    "DK01PC01C04-PROXY01"
    "dk01ntxc06-proxy-teamblue"
    )

    $Clusters=Get-NutanixClusters
    $Clusters=$Clusters.status.name | ? {$_ -ne "DK01PC01"} | sort
    #Collecting all VMs
    $AllVMS = @()
    foreach($Cluster in $Clusters){
        $Credential = Get-SafeCredentials -CredentialName $Cluster -LocalOnly
        $Params = Get-NutanixHeader $Credential
        
        
        $uri = "https://$Cluster.cloudfactory.dk:9440/PrismGateway/services/rest/v2.0/vms"
        
        $vms = Invoke-RestMethod -Uri $uri -ContentType "application/json" -Headers $Params.header
        
        $output = $vms.entities
        $AllVMS += $Output
    }
    
    #VMs not possible to live migrate
    $VMsCantLiveMigrate = $AllVMS | ? {$_.allow_live_migrate -ne "True" -and $_.name -notin $ignoreVMnames}
    
    #VMs possible to lige migrate
    $VMsCanLiveMigrate = $AllVMS | ? {$_.allow_live_migrate -eq "True"}

    Add-PRTGOutputChannel -Unit Count -Name "VMs there is not possible to live migrate" -Value ($VMsCantLiveMigrate|measure).Count -LimitMaxError 0
    Add-PRTGOutputChannel -Unit Count -Name "VMs there is possible to live migrate" -Value ($VMsCanLiveMigrate|measure).Count

    if ($VMsCantLiveMigrate){
         Add-PRTGOutputMessage -Message "$($VMsCantLiveMigrate.name -join ", ")"
    }
}
catch{
    Add-PRTGOutputMessage -Message ($_ | Out-String) -SetError
}

Get-PRTGOutput