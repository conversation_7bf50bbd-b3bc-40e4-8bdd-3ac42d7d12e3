﻿$ErrorActionPreference="Stop"

$ProxyServers = @"
"ClusterName","ProxyIP"
"DK01NTXC01", "***********"
"DK01NTXC02", "***********"
"DK01NTXC03", "***********"
"DK01NTXC04", "***********"
"DK01NTXC05", "***********"
"DK01NTXC06", "************"
"@|ConvertFrom-Csv -Delimiter ","

try{
    New-PRTGOutput
    
    Invoke-TrustAllCertificates

    #Credential for Proxy Server
    $Credential = Get-SafeCredentials -CredentialName "VeeamProxy"
    
    $Message = @()
    $ErrorMessages = @()
    $PDsWithoutVeeamJob = @()

    foreach ($ProxyServer in $ProxyServers){

        $cluster_name = $ProxyServer.ClusterName
        $proxy_ip = $ProxyServer.ProxyIP
    
        $baseURI = "https://$proxy_ip/api/"
        $LoginBody = @{
            grantType = "Password"
            Username = $Credential.UserName
            Password = (Decrypt-SecureString -Securestring $Credential.Password)
        }

        $request = Invoke-RestMethod -Method Post -Uri ($baseURI + "oauth2/token") -Body $LoginBody
        $header = @{Authorization = "Bearer $($request.accessToken)"}

        #Getting Cluster
        $Clusters = Invoke-RestMethod -Method Get -Uri ($baseURI + "v4/clusters") -Headers $header
        $Cluster = $Clusters | ? {$_.name -eq $cluster_name}

        #If the Cluster Object dosen't exist from the Proxy Server, then skip and send error message
        if (!($Cluster)){$ErrorMessages += "[$cluster_name] : Couldn't find cluster object from Proxy Server!"; Continue}

        #Getting all Veeam Jobs from current cluster.
        $Jobs = Invoke-RestMethod -Method Get -Uri ($baseURI + "v4/jobs") -Headers $header | select -ExpandProperty results
    
        #Getting all Protection Doamins of the cluster from Nutanix.
        $PDs = Get-ProtectionDomains -ClusterName $cluster_name

        $IgnorePDs = @(
            "VEEAM-PROTECT"
            "veeam-test"
            "PCTEMP"
            "DK01C01VEEAMPROTECTCF"
            "DK01C02VEEAMPROTECTCF"
            "DK01C03VEEAMPROTECTCF"
            "DK01C04VEEAMPROTECTCF"
            "DK01C05VEEAMPROTECTCF"
            "DK01C06VEEAMPROTECTCF"
            "DK01C01VEEAMPROTECTJDM"
            "DK01C02VEEAMPROTECTJDM"
            "DK01C03VEEAMPROTECTJDM"
            "DK01C04VEEAMPROTECTJDM"
            "DK01C05VEEAMPROTECTJDM"
            "DK01C06VEEAMPROTECTJDM"
        )  
        
        foreach ($PD in $PDs){
        
            if ($PD.name -in $IgnorePDs){Continue}

            #Search after Veeam Job where current PD is added.
            $Job = $Jobs | ? {$_.settings.protectionDomains -eq $PD.name}

            #If there dosne't exist any Veeam Job with current PD.
            if (!($Job)){
                $PDsWithoutVeeamJob += [PSCustomObject]@{
                    Cluster = $cluster_name
                    PD_Name = $PD.name
                    Message = "There dosen't exist any Veeam Job with following PD!"
                }
            }
        }
    }

    if ($ErrorMessages){$Message += $ErrorMessages}
    if ($PDsWithoutVeeamJob){
        $Message = "Following PD(s) isn't attached to any Veeam Job(s)! : $(($PDsWithoutVeeamJob.PD_Name|sort-object) -join ", ")"
        Add-PRTGOutputMessage -Message $Message
        Split-SlackMessages -Channel "#pd-config-errors" -Username "Protection Domains Miss Veeam Job" -TextAsObject ($PDsWithoutVeeamJob | Sort-Object Cluser, PD_Name)
    }

    Add-PRTGOutputChannel -Unit Count -Name "PDs with no Veeam Jobs" -Value ($PDsWithoutVeeamJob|measure).count -LimitMaxError 0

}catch{Add-PRTGOutputMessage -Message $_ -SetError}

Get-PRTGOutput