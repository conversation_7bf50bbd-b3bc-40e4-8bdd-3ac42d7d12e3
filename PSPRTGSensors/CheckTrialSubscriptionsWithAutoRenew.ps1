﻿
#Check Overdue Schedules.ps1

#region init
param(
    $refreshtoken = "bleLFrOlW9Qt9xSYAdbKrNNA-K3Jvg1fy5lMHHlikR9WA"
)

$WarningPreference="SilentlyContinue"
$ErrorActionPreference = "Stop"
$baseuri = "https://portal.api.cloudfactory.dk"

function Invoke-CFPortalPagedCall {
    param (
        $baseURI,
        $Header,
        $Endpoint = "/v2/customers/Customers"
    )

    $PageIndex = 1
    $PageSize = 100
    $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$PageSize"
    if($Endpoint -match "\?"){
        $uri = "$($baseURI)$($Endpoint)&PageIndex=$PageIndex&PageSize=$PageSize"
    }
    
    
    $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
    $TotalPages = $Result.metadata.totalpages
    if(-not $TotalPages){$Result}
    $output = @()
    $output += $result.results
    $output += 2..$TotalPages | % {
        $PageIndex = $PSItem
        $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$PageSize"
        $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
        $result.results
    }

    Return $output
    
}
function Get-CFPortalAccessToken {
    param (
        $baseURI,
        $RefreshToken
    )
    
    
    $getaccesstokenuri = "$baseURI/Authenticate/ExchangeRefreshToken/$refreshtoken"

    $accesstoken = Invoke-RestMethod -Uri $getaccesstokenuri
    


    $header = @{
        Authorization = "Bearer $($accesstoken.access_token)"
    }

    @{
        Header  = $header
        BaseURI = $baseURI
    }
    Write-verbose "Connected to $baseURI"
    
}
function Get-CFPortalEndCustomers {
    param (
        $baseURI,
        $Header
    )
    Invoke-CFPortalPagedCall @params -Endpoint "/v2/customers/Customers"
}
function Get-CFPortalEndCustomer {
    param (
        $baseURI,
        $Header,
        $id
    )
    $result=Invoke-RestMethod -Uri "$baseURI/v2/customers/Customers/byServiceID?service=0&serviceId=$id" -Headers $header -TimeoutSec 60
    $result
}
#endregion

#SensorTemplate.ps1

New-PRTGOutput
$Message = @()

try {
    

    $Params=Get-CFPortalAccessToken -baseURI $baseuri -RefreshToken $refreshtoken

    #get schedules that is trial and set to autorenew
    $result = Invoke-RestMethod -Uri "$($params.BaseURI)/v2/microsoft/odata/Subscriptions?`$count=true&`$filter=IsTrial eq true and AutoRenewEnabled eq true and Status eq 'Active'" -Headers $params.Header

    $ignoreSubscriptionIDs=@(
        "70a7355d-03b1-4000-c84c-81d45ffc950e" # Kuros Biosciences AG - CyberGate SIP intercom connect to Teams - CyberGate flex plan
    )
    $ignoreSKUName=@(
        "EasyScep - One click Cloud PKI for Intune - EasyScep 50+ users"
        "EasyRadius - One click Cloud Radius - EasyRadius 50+ users"
        )
    $result.value = $result.value | ? SubscriptionId -notin $ignoreSubscriptionIDs | ? name -notin $ignoreSKUName

    $RenewingTrialSchedulesCount=$result.value | Measure-Object |select -ExpandProperty count

    if ($RenewingTrialSchedulesCount -gt 0){

        foreach($impactedCustomer in $result.value){
            $Customer = Get-CFPortalEndCustomer -id $impactedCustomer.tenantId -baseURI $Params.BaseURI -Header $params.Header
            $impactedCustomer | Add-Member -MemberType NoteProperty -Name "Customername" -Value $customer.name


        }
        $SlackMessage=$result.value | select name, tenantId, Customername, credentialsId | Out-String
        Send-MessageToSlack -Channel schedule-errors -Username "Trial Schedules With Autorenew" -Text "Trial Schedules With Autorenew enabled.`n$($SlackMessage -join ", ")"
    }

    
    
    add-prtgoutputchannel -unit count -name "Trial Schedules" -value  $RenewingTrialSchedulesCount -LimitMaxError 0
 
    #$Message += "OK"


    if ($message){
        Add-PRTGOutputMessage -Message ($message | Out-String)
    }
}
catch {
    Add-PRTGOutputMessage -Message ($_ | Out-String) -SetError
}
finally {

    Get-PRTGOutput
}