﻿$ErrorActionPreference = "Stop"

#Load PRTG Module
$prtgscriptpath = Join-Path -Path "\\DK01PRTG01\c$\Program Files (x86)\PRTG Network Monitor\Custom Sensors\EXEXML" -ChildPath "PRTGOutput.ps1"
.($prtgscriptpath)


New-PRTGOutput

try {
    #Netbox Credentials & Authentication
    $NetboxCredentials = Get-SafeCredentials -CredentialName "NetboxToken"
    $NetboxHeader = Get-NetboxHeader -Credentials $NetboxCredentials

    #Getting all Netbox Devices where device_type & device_role equal FortiGate VM
    $NetboxDevices = Invoke-NBAPI -baseuri "https://netbox.cloudfactory.dk/api" -Command "dcim/devices/" -header $NetboxHeader.header -Inputs "device_type_id=18&role_id=19&device_type_id=201&role_id=20"
    $NetboxVDCs = Invoke-NBAPI -baseuri "https://netbox.cloudfactory.dk/api" -Command "dcim/virtual-device-contexts/" -header $NetboxHeader.header | where-object {$_.name -ne "root" -and $_.custom_fields.decommissioning -ne $true}
    $DevicesWithoutTenant = $NetboxDevices | Where-Object { $_.tenant -eq $null }
    $VDCsWithoutTenant = $NetboxVDCs | Where-Object { $_.tenant -eq $null }
    Add-PRTGOutputChannel -Unit Count -Name "FortiVM without tenant mapping" -Value ($DevicesWithoutTenant | Measure-Object).count -LimitMaxWarning 0
    Add-PRTGOutputChannel -Unit Count -Name "FortiVM Total Licenses" -Value ($NetboxDevices | Measure-Object).count
    Add-PRTGOutputChannel -Unit Count -Name "FortiVDOM without tenant mapping" -Value ($VDCsWithoutTenant | Measure-Object).count -LimitMaxWarning 0
    Add-PRTGOutputChannel -Unit Count -Name "FortiVDOM Total Licenses" -Value ($NetboxVDCs | Measure-Object).count
    $message = ""
    if ( ($DevicesWithoutTenant | Measure-Object).count -gt 0) {
        $message += "DevicesWithoutTenant: $($DevicesWithoutTenant.Name -join ", "); "
    }
    if ( ($VDCsWithoutTenant | Measure-Object).count -gt 0) {
        $message += "VDCsWithoutTenant: $($VDCsWithoutTenant.Name -join ", ") "
    }
    if($message -ne ""){
        Add-PRTGOutputMessage -Message $message
    }
}

catch {
    Add-PRTGOutputMessage -Message ($_ | Out-String) -SetError
}

Get-PRTGOutput