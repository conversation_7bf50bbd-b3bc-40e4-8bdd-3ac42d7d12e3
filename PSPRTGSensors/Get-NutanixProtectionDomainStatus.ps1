$ErrorActionPreference = "Stop"
New-PRTGOutput
try {

    $seterror=$false 
    $SlackChannel = "#nutanix-auto-protection"
    $NutanixCredentials = Test-SafeCredentials -application PrismCentral
    $NetboxCredentials = Get-SafeCredentials -CredentialName "NetboxToken"
    $NetboxParams = Get-NetboxHeader -Credentials $NetboxCredentials
    #Parameter
    $params = @{
        Server      = "ntx.cloudfactory.dk:9440"
        Credentials = $NutanixCredentials
    }
    
    #Getting all Prism Central VMs
    $PCVMs = Get-NutanixVMs
    #Getting all Cluster
    $Clusters = Get-NutanixClusters | ? { $_.status.name -ne "DK01PC01" }

    #Removing Deleted VMs from Protection Domains
    foreach ($Cluster in $Clusters) {
        $cluster_name = $Cluster.status.name
        $cluster_uuid = $Cluster.metadata.uuid
        $PC_cluster_vms = $PCVMs | ? { $_.spec.cluster_reference.uuid -eq $cluster_uuid }
        if(($PC_cluster_vms | measure).count -lt 50){continue}
        $PDs = Get-ProtectionDomains -ClusterName $Cluster_Name

        foreach ($PD in $PDs) {
            $PD_Name = $PD.name
            $PD_VMs = $PD.vms
            foreach ($VM in $PD_VMs) {
                $uuid = $VM.vm_id
                $vm_name = $vm.vm_name
                
                #If VM is not in Prism Central, remove it from Protection Domain
                if ($uuid -notin $PC_cluster_vms.metadata.uuid) {
                    try {
                        Remove-VMsFromProtectionDomain -ClusterName $cluster_name -ProtectionDomainName $PD_Name -VMUUIDs $uuid | Out-Null
                    }
                    catch {
                        #Write-Output "An error occurred while removing VMs from Protection Domain"
                    }
                }
            }
        }

    }
    
    #Getting all Protection Domains
    $AllProtectionDomains = foreach ($Cluster in $Clusters) {
        $cluster_name = $Cluster.status.name
        $ProtectionDomains = Get-ProtectionDomains -ClusterName $cluster_name

        foreach ($ProtectionDomain in $ProtectionDomains) {
            [PSCustomObject]@{
                Name    = $ProtectionDomain.Name
                VMCount = ($ProtectionDomain.vms | measure).count
                Cluster = $Cluster.status.name
                VMS     = $ProtectionDomain.vms
            }
        }
    }
    
    #Ignore list
    $ignoreVMList = @(
        "937b81a0-3aa2-4b61-9942-023fea9c9938" # PC-Node-1                                   
        "dc2672e8-07a2-473d-815e-989421b861d4" # PC-Node-2                                                        
        "e2251fad-c498-4f93-9199-7b41ec87eb4e" # PC-Node-3               
        "bf73e4e4-23ae-4d32-a925-f2260a695c59" # PC-Node-1    
        "0e2caa44-51b8-4aea-aef7-6acaf367b737" # PC-Node-2  
        "d794e44e-16fa-4231-8c02-407214f32bfe" # PC-Node-3                
        "6937d226-edd9-4196-bac8-4f2d0d51fca9" # PC-Node-1    
        "2ce971c2-95b3-48c2-b1c0-76c0760da7cd" # PC-Node-2  
        "faab67e3-cfd8-416d-aca8-ff326cfc0753" # PC-Node-3  
        #"1273fbc2-3ef9-425b-adb3-d5822fb342be" # Barfoedrds01 - added to NO_PROTECTION category
        #"27f0af6d-f564-403d-be76-b928af5dde62" # Barfoedrds02 - added to NO_PROTECTION category                              
        #"c0825a32-f40d-4c7c-bded-26306ae93649" # CF-SCP-RELAYER
        #"0e1f5c32-1d1f-48e7-bc8a-908907df208d" # DAOR_BASH - added to NO_PROTECTION category
        #"6b9151ef-baab-4f8d-8258-cdb9136abc19" # daor_livemigrate
        #"5e917978-271f-4241-aec3-810db63eae32" # daor_c01_livemigrate
    )
    
    $NetboxDevices = Get-NetboxVMs
    $NetboxTenants = Get-NetboxTenants
    $LeapTenantGroups = @{
        53257 = "JDM"
        53324 = "CF"
        1283 = "CF"
    }

    $LeapProtectUUid =$NetboxTenants | where {[int]$_.group.id -in $LeapTenantGroups.Keys} | foreach{
        [PSCustomObject]@{
            uuid = $_.slug
            Name = $_.name
            Category = $LeapTenantGroups[[int]$_.group.id]
        }
    }


    $VMsInIgnoreList = @()
    $VMsInNoProtectionCategory = @()
    $VMsProtectedWithLeap = @()
    $VMsProtectedCorrectly = @()
    $VMsProtectionFailed = @()
    $VMsPDNotSetCorrectly = @()
    $VMsMoveTest = @()
    $VMsUnderLiveMigration = @()
    $VMsUnknownCluster = @()
    $UnprotectedVMs = @()
    #Looping through all VMs to check if they are protected correctly
    #If not, then add them to the correct Protection Domain
    $i=0
    foreach ($VM in $PCVMs) {
        $i++
        
        $vm_name = $VM.status.name
        try {
            write-progress -Activity "Checking VM Protection Status" -Status "$vm_name" -PercentComplete ($i / $PCVMs.count * 100)
        }catch {}

        $vm_uuid = $VM.metadata.uuid
        $vm_cluster_name = $VM.spec.cluster_reference.name
        $NetboxDevice =$NetboxDevices | where {$_.custom_fields.VM_ID -eq $vm_uuid}
        $Backuptype = "Default"
        if($NetboxDevice.tenant){
            $NetboxTenant = $NetboxTenants | where id -eq $NetboxDevice[0].tenant.id
            $Backuptype = $NetboxTenant.custom_fields.BackupType
        }

        #Checking for VM in ignore list
        if ($vm_uuid -in $ignoreVMList) { 
            $VMsInIgnoreList += $VM
            Write-Verbose "[$vm_name] : VM is in ignore list"
            Continue 
        }
        if ($vm_name -match "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))") { 
            #$VMsInIgnoreList += $VM
            Write-Verbose "[$vm_name] : VM is in ignore list"
            Continue 
        }
        ##count number of unprotected vms on cluster 5
        #if ($vm_cluster_name -match "DK01NTXC05") {
        #    continue
        #}

        #Checking for MoveTest VM
        if ( $vm_name -match "-MoveTest$" ) {
            $VMsMoveTest += $VM
            Write-Verbose "[$vm_name] : VM is a test cutover vm. Don't protect."
            Continue
        }

        #The cluster must match
        if (!($vm_cluster_name -match "(DK\d{2})NTXC(\d{2})")) {
            $VMsUnknownCluster += $VM
            Write-Verbose "Unknown ClusterName"
            Continue
        }

        $Site = $Matches[1]
        $ClusterNumber = $Matches[2]

        #if VM has NoProtection category skip it
        if ($vm.metadata.categories_mapping.Snapshot -contains "NO_PROTECTION") { 
            $VMsInNoProtectionCategory += $VM
            Write-Verbose "[$vm_name] : VM has NoProtection category"
            Continue 
        }
                
        #If VM have LiveMigration category Remove PD protection and skip it
        if ($vm.metadata.categories_mapping.Snapshot -contains "Livemigration") {
            $VMsUnderLiveMigration += $VM
            #check if protected på PD. If yes then remove PD protection
            if ($VM.status.resources.protection_type -eq "PD_PROTECTED") {
                #TODO: remove PD protection
                #Remove-VMsFromProtectionDomain -ClusterName $cluster_name -ProtectionDomainName $PD_Name -VMUUIDs $uuid | Out-Null
            }
            Continue
        }

        #If VM is protected by LEAP then continue
        if ($VM.status.resources.protection_type -eq "RULE_PROTECTED") {
            $VMsProtectedWithLeap += $VM
            Continue
        }

        

        if($VM.metadata.project_reference.uuid -in $LeapProtectUUid.uuid){
            $leapCategory = $LeapProtectUUid | Where-Object uuid -eq $VM.metadata.project_reference.uuid
            $ClusterPDs = $AllProtectionDomains | Where-Object { $_.Name -Match "$($Site)C$($ClusterNumber)VEEAMPROTECT\d\d|$($Site)C$($ClusterNumber)VEEAMPROTECTIMMUENC\d\d|$($Site)C$($ClusterNumber)VEEAMPROTECT($(( $LeapProtectUUid.Category |select -Unique) -join "|"))" }
            $update = $false
            if($VM.status.resources.protection_type -eq "PD_PROTECTED"){
                $vmprotectiondomain = $ClusterPDs | Where-Object { $_.VMS.vm_id -contains $vm_uuid }
                if($vmprotectiondomain.name -notmatch "$($Site)C$($ClusterNumber)VEEAMPROTECT$($leapCategory.Category)"){
                    Remove-VMsFromProtectionDomain -ClusterName $vm_cluster_name -ProtectionDomainName $vmprotectiondomain.Name -VMUUIDs $vm_uuid | Out-Null
                    $pd = $ClusterPDs | Where-Object { $_.Name -match "$($Site)C$($ClusterNumber)VEEAMPROTECT$($leapCategory.Category)" } |Select-Object -First 1
                    Add-VMsToProtectionDomain -ClusterName $vm_cluster_name -ProtectionDomainName $pd.Name -VMUUIDs $vm_uuid | Out-Null 
                }
            }
            else{
                $pd = $ClusterPDs | Where-Object { $_.Name -match "$($Site)C$($ClusterNumber)VEEAMPROTECT$($leapCategory.Category)" } |Select-Object -First 1
                Add-VMsToProtectionDomain -ClusterName $vm_cluster_name -ProtectionDomainName $pd.Name -VMUUIDs $vm_uuid | Out-Null 
            }
            if($VM.metadata.use_categories_mapping -ne $true){
                $VM.metadata | Add-Member -MemberType NoteProperty -Name "use_categories_mapping" -Value $true -Force
            }
            if($VM.metadata.categories_mapping.VeeamBackup -notcontains $leapCategory.Category){
                $update = $true
                if(($VM.metadata.categories_mapping | Get-Member).name -contains "VeeamBackup"){
                    $VM.metadata.categories_mapping.VeeamBackup = @($leapCategory.Category)
                }
                else{
                    $VM.metadata.categories_mapping  | Add-Member -MemberType NoteProperty -Name "VeeamBackup" -Value @($leapCategory.Category) -Force
                }
            }
            if(-not ($VM.metadata.categories_mapping.Snapshot -contains "ConvertToProtectionDomain")){
                $update = $true
                if(($VM.metadata.categories_mapping | Get-Member).name -contains "Snapshot"){
                    $VM.metadata.categories_mapping.Snapshot = @("ConvertToProtectionDomain")
                }
                else{
                    $VM.metadata.categories_mapping  | Add-Member -MemberType NoteProperty -Name "Snapshot" -Value @("ConvertToProtectionDomain") -Force
                }
            }
            if($update){
                try {
                    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $NutanixCredentials.UserName,(Decrypt-SecureString $NutanixCredentials.Password))))
                        $header=@{
                            Authorization="Basic $base64AuthInfo"
                            
                        }
                    Invoke-RestMethod -Uri "https://ntx.cloudfactory.dk:9440/api/nutanix/v3/vms/$vm_uuid" -Headers $header -Method Put -Body (($vm | select metadata, spec) | ConvertTo-Json -Depth 99) -ContentType "application/json; charset=utf-8" | Out-Null
                    $VMsProtectedCorrectly += $VM
                    
                }
                catch {
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Leap Protection - VM Leap protection failed" -Text "*$vm_name*"
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Leap Protection - VM Leap protection failed" -Text ($_ | Out-String)
                    $VMsProtectionFailed += $VM
                }
            }
            else{
                $VMsProtectedCorrectly += $VM
            }
            continue
        }

        #Get PDS for cluster
        #examplename: DK01C05VEEAMPROTECT04
        $ClusterPDs = $AllProtectionDomains | Where-Object { $_.Name -Match "$($Site)C$($ClusterNumber)VEEAMPROTECT\d\d|$($Site)C$($ClusterNumber)VEEAMPROTECTIMMUENC\d\d" }

        #check if vm is protected by correct PD
        if ($VM.status.resources.protection_type -eq "PD_PROTECTED") {
            
            $vmprotectiondomain = $ClusterPDs | ? { $_.VMS.vm_id -contains $vm_uuid }
            
            
            #If VM exist in the list, then all good!
            if ($vmprotectiondomain) {
                $VMsProtectedCorrectly += $VM
            }
            else {
                $VMsPDNotSetCorrectly += $VM
            }

            #If vm dosent have the snapshot category
            if (($VM.metadata.categories.Snapshot -notcontains "ConvertToProtectionDomain")) {
                

                   
                try {

                    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $NutanixCredentials.UserName,(Decrypt-SecureString $NutanixCredentials.Password))))
                    $header=@{
                        Authorization="Basic $base64AuthInfo"
                        
                    }
                    $VM.metadata | Add-Member -MemberType NoteProperty -Name "use_categories_mapping" -Value $true -Force
                    $VM.metadata.categories_mapping  | Add-Member -MemberType NoteProperty -Name "Snapshot" -Value @("ConvertToProtectionDomain") -Force
                    # Invoke-CFNutanixCommand @params -method put -command "vms/$vm_uuid" -putbody (($vm | select metadata, spec) | ConvertTo-Json -Depth 99) | Out-Null
                    
                    Invoke-RestMethod -Uri "https://ntx.cloudfactory.dk:9440/api/nutanix/v3/vms/$vm_uuid" -Headers $header -Method Put -Body (($vm | select metadata, spec) | ConvertTo-Json -Depth 99) -ContentType "application/json; charset=utf-8" | Out-Null
                }
                catch {
                    #$streamReader = [System.IO.StreamReader]::new($_.Exception.Response.GetResponseStream())
                    #$ErrResp = $streamReader.ReadToEnd() | ConvertFrom-Json
                    #$streamReader.Close()
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Protection Domains - Miss Category" -Text "*$vm_name*"
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Protection Domains - Miss Category" -Text ($_ | Out-String)
                    continue
                }
            }

        }
        
        #If VM is unprotected
        if ($VM.status.resources.protection_type -eq "UNPROTECTED") {
            
            #add for sensor channel. for a catch all failsafe. 
            $UnprotectedVMs += $VM

            #Check if VM has vTPM enabled. 
            #Nutanix doesnt support vTPM protection with Protected Domains
            if ($VM.spec.virtual_machine_properties.vtpm_enabled) {

                #We cant handle vtpm and immutable & encrypted together. So we add it to the list of vms that are not protected.
                if($Backuptype -eq "Immutable & Encrypted") {
                    $VMsProtectionFailed += $VM
                    continue
                }
                
                #If vm is vtpm and not targeted for immutable & encrypted, we protect it with leap protection.

                $VM.metadata | Add-Member -MemberType NoteProperty -Name "use_categories_mapping" -Value $true -Force
                $VM.metadata.categories_mapping  | Add-Member -MemberType NoteProperty -Name "Snapshot" -Value @("DK01-2h24","DK01-d30") -Force
                try {
                    Invoke-RestMethod -Uri "https://ntx.cloudfactory.dk:9440/api/nutanix/v3/vms/$vm_uuid" -Headers $header -Method Put -Body (($vm | select metadata, spec) | ConvertTo-Json -Depth 99) -ContentType "application/json; charset=utf-8" | Out-Null
                    $VMsProtectedCorrectly += $VM
                    
                }
                catch {
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Protection Domains - VM with vTPM protection failed" -Text "*$vm_name*"
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Protection Domains - VM with vTPM protection failed" -Text ($_ | Out-String)
                    $VMsProtectionFailed += $VM
                }
                continue

                
            }
            

            #Select the Protection Domain with lowerst VM count
            $selected_PD  = $null
            if($Backuptype -eq "Default"){$selected_PD = $ClusterPDs | where name -NotMatch "IMMUENC" | Sort-Object -Property VMCount, name | select -First 1}
            elseif($Backuptype -eq "Immutable & Encrypted"){$selected_PD = $ClusterPDs | where name -Match "IMMUENC" | Sort-Object -Property VMCount, name | select -First 1}
            

            if($selected_PD){
                #Adding VM to Protection Domain
                try {
                    $request = Add-VMsToProtectionDomain -ClusterName $vm_cluster_name -ProtectionDomainName $selected_PD.Name -VMUUIDs $vm_uuid
                    #Write-Output "[$vm_name] : Now protected by $($selected_PD.name)"
                    $VMsProtectedCorrectly += $VM

                    #Adding 1 to VMCount
                    $selected_PD.VMCount++

                    if (!($VM.metadata.categories.Snapshot -eq "ConvertToProtectionDomain")) {
                        #Adding to Category
        
                            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $NutanixCredentials.UserName,(Decrypt-SecureString $NutanixCredentials.Password))))
                            $header=@{
                                Authorization="Basic $base64AuthInfo"
                            
                            }
                            $VM.metadata | Add-Member -MemberType NoteProperty -Name "use_categories_mapping" -Value $true -Force
                            $VM.metadata.categories_mapping  | Add-Member -MemberType NoteProperty -Name "Snapshot" -Value @("ConvertToProtectionDomain") -Force
                            # Invoke-CFNutanixCommand @params -method put -command "vms/$vm_uuid" -putbody (($vm | select metadata, spec) | ConvertTo-Json -Depth 99) | Out-Null
                        
                            Invoke-RestMethod -Uri "https://ntx.cloudfactory.dk:9440/api/nutanix/v3/vms/$vm_uuid" -Headers $header -Method Put -Body (($vm | select metadata, spec) | ConvertTo-Json -Depth 99) -ContentType "application/json; charset=utf-8" | Out-Null
                    }
                                
                    #Take it easy!
                    Start-Sleep -Seconds 1
                }
                catch {
                    $VMsProtectionFailed += $VM
                    Send-MessageToSlack -Channel $SlackChannel -Username "Protection Domain Failed" -Text "*$vm_name*"
                    Send-MessageToSlack -Channel $SlackChannel -Username "Protection Domain Failed" -Text "Error: $($_| out-string)"
                    #Take it easy!
                    Start-Sleep -Seconds 1
                }
            }else{

                $VMsProtectionFailed += $VM

                #We not supposed to end here. But just in case we send a slack message
                Send-MessageToSlack -Channel $SlackChannel -Username "Protection Domain Failed" -Text "*$vm_name*"
                Send-MessageToSlack -Channel $SlackChannel -Username "Protection Domain Failed" -Text "No suitable Protection Domain found for $vm_name"

                



            }

        }

        #Handle VMs that are protected by the wrong PD for immutable & encrypted
        elseif(($Backuptype -eq "Default" -and $vmprotectiondomain.name -match "IMMUENC") -or ($Backuptype -eq "Immutable & Encrypted" -and $vmprotectiondomain.name -notmatch "IMMUENC")){
             #Select the Protection Domain with lowerst VM count
            $selected_PD  = $null
            if($Backuptype -eq "Default"){$selected_PD = $ClusterPDs | where name -NotMatch "IMMUENC" | Sort-Object -Property VMCount, name | select -First 1}
            elseif($Backuptype -eq "Immutable & Encrypted"){$selected_PD = $ClusterPDs | where name -Match "IMMUENC" | Sort-Object -Property VMCount, name | select -First 1}
            try{
                #"$vm_cluster_name : $($vmprotectiondomain.Name) : $vm_uuid"
                #remove PD
                if($vmprotectiondomain.Name){
                    $request = Remove-VMsFromProtectionDomain -ClusterName $vm_cluster_name -ProtectionDomainName $vmprotectiondomain.Name -VMUUIDs $vm_uuid
                    $vmprotectiondomain.VMCount--
                    #Take it easy!
                    Start-Sleep -Seconds 1
                    #add PD
                    #"$vm_cluster_name : $($selected_PD.Name) : $vm_uuid"
                    $request = Add-VMsToProtectionDomain -ClusterName $vm_cluster_name -ProtectionDomainName $selected_PD.Name -VMUUIDs $vm_uuid
                    $selected_PD.VMCount++
                    Send-MessageToSlack -Channel $SlackChannel -Username "Nutanix Protection Domains - Change" -Text "Changed VM: $vm_name from $($vmprotectiondomain.name) to $($selected_PD.name)"
                    #Take it easy!
                    Start-Sleep -Seconds 1
                }
            }
            catch {
                Send-MessageToSlack -Channel $SlackChannel -Username "Protection Domain Failed" -Text "*$vm_name* $vm_cluster_name : $($vmprotectiondomain.Name) : $($selected_PD.Name) :  $vm_uuid"
                Send-MessageToSlack -Channel $SlackChannel -Username "Protection Domain Failed" -Text "Error: $($_| out-string)"
                #Take it easy!
                Start-Sleep -Seconds 1
            }
        }
    }



    
    $message = @()
    
    #list of test cutover vms that have been in this state for more than X days
    $maxdays = 7
    $VMsMoveTestLongTime = $VMsMoveTest | ? { ((get-date) - ([datetime]$_.metadata.creation_time)).totaldays -gt $maxdays }
    if ($VMsMoveTestLongTime) {
        $VMsMoveTestLongTimeMessageString = $VMsMoveTestLongTime | % { "$([int]((get-date)-([datetime]$_.metadata.creation_time)).totaldays) days $($_.status.name)" }
        $Message += "Folowing vms have been in testcutover state for more that $maxdays days: $($VMsMoveTestLongTimeMessageString -join ", ")"
    }

    if ($VMsPDNotSetCorrectly) { $message += "These VMs is not protected correctly: $($VMsPDNotSetCorrectly.spec.Name -join ", ")" }
    if ($VMsUnderLiveMigration) { $message += "vms being live migrated: $($VMsUnderLiveMigration.status.name -join ", ")" }
    if ($VMsInIgnoreList) { $message += "These VMs is in ignore list: $($VMsInIgnoreList.status.name -join ", ")" }
    if ($VMsProtectionFailed) { $message += "These VMs failed to be protected: $($VMsProtectionFailed.status.name -join ", ")" }
    if ($VMsInNoProtectionCategory) { $message += "These VMs has NO_PROTECT category: $($VMsInNoProtectionCategory.status.name -join ", ")" }
    if ($UnprotectedVMs) { $message += "These VMs is not protected: $($UnprotectedVMs.status.name -join ", ")" }





    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines in ignore list" -Value ($VMsInIgnoreList | measure).count
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines protected by LEAP" -Value ($VMsProtectedWithLeap | measure).count
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines protected correctly" -Value ($VMsProtectedCorrectly | measure).count
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines in testcutover" -Value ($VMsMoveTest | measure).count
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines in testcutover long time" -Value ($VMsMoveTestLongTime | measure).count -LimitMaxWarning 0
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines Live Migrating" -Value ($VMsUnderLiveMigration | measure).count -LimitMaxWarning 0
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines with unknown cluster status" -Value ($VMsUnknownCluster | measure).count -LimitMaxError 0
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines protection failed" -Value ($VMsProtectionFailed | measure).count -LimitMaxError 0
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines with NO_PROTECTION category" -Value ($VMsInNoProtectionCategory | measure).count
    Add-PRTGOutputChannel -Unit Count -Name "Virtual Machines not protected" -Value ($UnprotectedVMs | measure).count -LimitMaxWarning 0 #only set warning, since we dont want to look at error. this will appear each time a new vm gets protected.

    foreach ($AllProtectionDomain in $AllProtectionDomains) {
        try {
            Add-PRTGOutputChannel -Unit Count -Name $AllProtectionDomain.Name -Value $AllProtectionDomain.VMCount
        }
        catch {
            $seterror = $true
            $Message+=($_|out-string)
        }
    }

    Add-PRTGOutputMessage -Message ($Message | out-string) -SetError:$seterror
}
catch {
    Add-PRTGOutputMessage -Message ($_ | Out-String) -SetError
}

Get-PRTGOutput