// Function to get tenant information
async function getTenantInfo() {
    try {
        const account = msalInstance.getActiveAccount();
        if (!account) {
            throw new Error('No active account! Please sign in first.');
        }

        // Validate token before proceeding
        const isTokenValid = await validateToken();
        if (!isTokenValid) {
            throw new Error('interaction_required: Session has expired. Please sign in again.');
        }

        // Request token with necessary scopes for both organization info and security score
        const tokenRequest = {
            account: account,
            scopes: ["User.Read", "Organization.Read.All", "SecurityEvents.Read.All"]
        };
        
        const tokenResponse = await msalInstance.acquireTokenSilent(tokenRequest);
        const accessToken = tokenResponse.accessToken;
        
        // Fetch tenant information
        const tenantInfo = await fetchGraphData('https://graph.microsoft.com/v1.0/organization', accessToken);
        
        if (!tenantInfo.value || tenantInfo.value.length === 0) {
            throw new Error('No tenant information found');
        }
        
        try {
            // Get security score
            const securityScore = await getSecurityScore(accessToken);
            
            // Display the tenant information and security score
            displayTenantInfo(tenantInfo.value[0], securityScore);
        } catch (securityError) {
            console.error('Error getting security score:', securityError);
            
            // Display tenant info but with error message for security score
            const tenantDetails = document.getElementById('tenantDetails');
            if (tenantDetails) {
                const tenantInfoHtml = `
                    <div class="bg-gray-50 p-4 rounded">
                        <p class="font-semibold">Tenant ID:</p>
                        <p class="text-gray-700 mb-2">${tenantInfo.value[0].id}</p>
                        
                        <p class="font-semibold">Display Name:</p>
                        <p class="text-gray-700 mb-2">${tenantInfo.value[0].displayName}</p>
                        
                        <p class="font-semibold">Tenant Type:</p>
                        <p class="text-gray-700">${tenantInfo.value[0].tenantType}</p>
                    </div>
                    <div class="mt-4 p-4 rounded bg-red-50 border border-red-200">
                        <h3 class="font-bold text-lg mb-2 text-red-700">Security Score Error</h3>
                        <p class="text-red-700">Unable to fetch security recommendations: ${securityError.message}</p>
                        <p class="text-sm text-red-600 mt-2">Please try again later or contact your administrator if the problem persists.</p>
                    </div>
                `;
                tenantDetails.innerHTML = tenantInfoHtml;
            }
        }
    } catch (error) {
        console.error('Error getting tenant information:', error);
        
        // Check for token expiration errors
        if (error.message.includes('interaction_required') || 
            error.message.includes('AADSTS70044') || 
            error.message.includes('session has expired')) {
            
            console.log('Session expired during tenant info fetch, logging out');
            // Handle expired session by logging out
            if (typeof signOut === 'function') {
                signOut();
            }
        }
        
        const tenantDetails = document.getElementById('tenantDetails');
        if (tenantDetails) {
            tenantDetails.innerHTML = `
                <div class="bg-red-50 p-4 rounded text-red-600">
                    Error fetching tenant information: ${error.message}
                </div>
            `;
        }
    }
}

// Function to get security score and improvement actions
async function getSecurityScore(accessToken) {
    try {
        // Fetch secure score from Microsoft Graph Security API
        const secureScoreData = await fetchGraphData('https://graph.microsoft.com/v1.0/security/secureScores?$top=1', accessToken);
        
        if (!secureScoreData.value || secureScoreData.value.length === 0) {
            console.warn('No security score data found');
            return null;
        }
        
        // Get the latest secure score
        const latestScore = secureScoreData.value[0];
        
        try {
            // Fetch security control profiles (improvement actions)
            const controlProfilesUrl = `https://graph.microsoft.com/v1.0/security/secureScoreControlProfiles`;
            const controlProfiles = await fetchGraphData(controlProfilesUrl, accessToken);
            
            if (controlProfiles && controlProfiles.value && controlProfiles.value.length > 0) {
                // Process the control profiles to extract improvement actions
                const improvementActions = controlProfiles.value.map(profile => {
                    // Calculate score impact as a percentage of the max score
                    const scoreImpact = profile.maxScore ? profile.maxScore / latestScore.maxScore : 0;
                    
                    return {
                        id: profile.id,
                        title: profile.title || profile.displayName || 'Unknown action',
                        scoreImpact: scoreImpact,
                        implementationCost: profile.implementationCost || 'Unknown',
                        userImpact: profile.userImpact || 'Unknown',
                        description: profile.description || '',
                        remediation: profile.remediation || '',
                        isImplemented: profile.controlStateUpdates && profile.controlStateUpdates.some(state => state.state === 'Implemented')
                    };
                });
                
                // Filter out already implemented actions
                const pendingActions = improvementActions.filter(action => !action.isImplemented);
                
                if (pendingActions.length === 0) {
                    // No pending actions found
                    latestScore.improvementActions = [];
                } else {
                    // Sort by score impact (highest to lowest) and take top 5
                    latestScore.improvementActions = pendingActions
                        .sort((a, b) => b.scoreImpact - a.scoreImpact)
                        .slice(0, 5);
                }
            } else {
                console.error('No control profiles found');
                throw new Error('No security recommendations available');
            }
        } catch (controlError) {
            console.error('Error fetching control profiles:', controlError);
            throw new Error('Error fetching security recommendations');
        }
        
        return latestScore;
    } catch (error) {
        console.error('Error fetching security score:', error);
        throw error;
    }
}
