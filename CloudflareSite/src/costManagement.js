// Cost Management Module for Azure Tenant Monitor
// This module handles fetching and displaying cost management recommendations

// Function to fetch cost management recommendations from Microsoft Graph API
async function getCostRecommendations(accessToken) {
    try {
        // Fetch cost recommendations from Microsoft Graph API
        // Using the advisor recommendations endpoint which includes cost management recommendations
        const advisorUrl = 'https://management.azure.com/subscriptions?api-version=2020-01-01';
        
        // First, we need to get the list of subscriptions to iterate through them
        const subscriptions = await fetchAzureData(advisorUrl, accessToken);
        
        if (!subscriptions || !subscriptions.value || subscriptions.value.length === 0) {
            console.warn('No subscriptions found');
            return null;
        }
        
        // Array to store all cost recommendations across subscriptions
        let allRecommendations = [];
        
        // For each subscription, fetch the cost recommendations
        for (const subscription of subscriptions.value) {
            const recommendationsUrl = `https://management.azure.com/subscriptions/${subscription.subscriptionId}/providers/Microsoft.Advisor/recommendations?api-version=2020-01-01&filter=Category eq 'Cost'`;
            
            const recommendations = await fetchAzureData(recommendationsUrl, accessToken);
            
            if (recommendations && recommendations.value && recommendations.value.length > 0) {
                // Add subscription info to each recommendation for reference
                const subscriptionRecommendations = recommendations.value.map(rec => ({
                    ...rec,
                    subscriptionId: subscription.subscriptionId,
                    subscriptionName: subscription.displayName
                }));
                
                allRecommendations = [...allRecommendations, ...subscriptionRecommendations];
            }
        }
        
        // Process and return the recommendations
        return processRecommendations(allRecommendations);
    } catch (error) {
        console.error('Error fetching cost recommendations:', error);
        throw error;
    }
}

// Helper function to fetch data from Azure Management API
async function fetchAzureData(url, accessToken) {
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Azure API Error:', errorData);
            throw new Error(`Azure API error: ${errorData.error ? errorData.error.message : 'Unknown error'}`);
        }

        const data = await response.json();
        console.log(`Azure API Response from ${url}:`, data);
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}

// Function to process and format the recommendations
function processRecommendations(recommendations) {
    if (!recommendations || recommendations.length === 0) {
        return [];
    }
    
    return recommendations.map(rec => {
        // Extract potential savings from the recommendation
        const potentialSavings = extractPotentialSavings(rec);
        
        // Create a direct link to the resource in Azure portal
        const resourceLink = createResourceLink(rec);
        
        return {
            id: rec.id,
            title: rec.properties.shortDescription.solution || 'Unnamed recommendation',
            description: rec.properties.shortDescription.problem || '',
            resourceType: rec.properties.resourceType || 'Unknown',
            impact: rec.properties.impact || 'Medium',
            potentialSavings: potentialSavings,
            resourceLink: resourceLink,
            subscriptionName: rec.subscriptionName || 'Unknown subscription',
            lastUpdated: rec.properties.lastUpdated || new Date().toISOString()
        };
    });
}

// Helper function to extract potential savings from recommendation
function extractPotentialSavings(recommendation) {
    try {
        // Different recommendations might store savings information differently
        // This is a simplified approach
        if (recommendation.properties.extendedProperties && recommendation.properties.extendedProperties.savingsAmount) {
            const amount = parseFloat(recommendation.properties.extendedProperties.savingsAmount);
            const currency = recommendation.properties.extendedProperties.savingsCurrency || 'USD';
            
            return {
                amount: amount,
                currency: currency,
                formatted: `${amount.toFixed(2)} ${currency}`
            };
        } else if (recommendation.properties.annualSavings) {
            // Some recommendations might have annualSavings property
            const amount = parseFloat(recommendation.properties.annualSavings.amount);
            const currency = recommendation.properties.annualSavings.currency || 'USD';
            
            return {
                amount: amount,
                currency: currency,
                formatted: `${amount.toFixed(2)} ${currency}/year`
            };
        }
        
        // If no savings information is found
        return {
            amount: 0,
            currency: 'USD',
            formatted: 'Not available'
        };
    } catch (error) {
        console.error('Error extracting potential savings:', error);
        return {
            amount: 0,
            currency: 'USD',
            formatted: 'Not available'
        };
    }
}

// Helper function to create a direct link to the resource in Azure portal
function createResourceLink(recommendation) {
    try {
        if (recommendation.properties.resourceId) {
            const resourceId = recommendation.properties.resourceId;
            return `https://portal.azure.com/#resource${resourceId}`;
        } else if (recommendation.id) {
            // If resource ID is not available, link to the recommendation itself
            return `https://portal.azure.com/#blade/Microsoft_Azure_Expert/RecommendationBlade/recommendationId/${encodeURIComponent(recommendation.id)}`;
        }
        
        // Default to Azure Cost Management
        return 'https://portal.azure.com/#blade/Microsoft_Azure_CostManagement/Menu/overview';
    } catch (error) {
        console.error('Error creating resource link:', error);
        return 'https://portal.azure.com/#blade/Microsoft_Azure_CostManagement/Menu/overview';
    }
}

// Function to display cost recommendations in the UI
function displayCostRecommendations(recommendations) {
    const costRecommendationsContainer = document.getElementById('costRecommendations');
    
    if (!costRecommendationsContainer) {
        console.error('Cost recommendations container not found');
        return;
    }
    
    if (!recommendations || recommendations.length === 0) {
        costRecommendationsContainer.innerHTML = `
            <div class="p-4 rounded bg-yellow-50 border border-yellow-200">
                <p class="text-yellow-700">No cost saving recommendations available at this time.</p>
                <p class="text-sm text-yellow-600 mt-2">Check back later or visit the Azure Cost Management portal for more information.</p>
                <div class="mt-4 text-center">
                    <a href="https://portal.azure.com/#blade/Microsoft_Azure_CostManagement/Menu/overview" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        Go to Azure Cost Management
                    </a>
                </div>
            </div>
        `;
        return;
    }
    
    // Calculate total potential savings
    const totalSavings = recommendations.reduce((total, rec) => {
        return total + (rec.potentialSavings.amount || 0);
    }, 0);
    
    // Get the most common currency
    const currencies = recommendations
        .map(rec => rec.potentialSavings.currency)
        .filter(currency => currency !== 'Not available');
    
    const mostCommonCurrency = currencies.length > 0 ? 
        currencies.sort((a, b) => 
            currencies.filter(c => c === a).length - currencies.filter(c => c === b).length
        ).pop() : 'USD';
    
    // Generate HTML for the recommendations table
    const recommendationsHtml = `
        <div class="bg-white p-4 rounded border border-gray-200">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-bold text-lg">Cost Saving Recommendations</h3>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Potential Savings:</p>
                    <p class="font-bold text-green-600">${totalSavings.toFixed(2)} ${mostCommonCurrency}/year</p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="py-2 px-4 border-b text-left">Recommendation</th>
                            <th class="py-2 px-4 border-b text-left">Resource Type</th>
                            <th class="py-2 px-4 border-b text-left">Subscription</th>
                            <th class="py-2 px-4 border-b text-right">Potential Savings</th>
                            <th class="py-2 px-4 border-b text-center">Last Updated</th>
                            <th class="py-2 px-4 border-b text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recommendations.map(rec => {
                            // Format the date
                            const lastUpdated = new Date(rec.lastUpdated).toLocaleDateString();
                            
                            // Determine impact color
                            let impactClass = 'bg-yellow-100';
                            if (rec.impact === 'High') {
                                impactClass = 'bg-green-100';
                            } else if (rec.impact === 'Low') {
                                impactClass = 'bg-gray-100';
                            }
                            
                            return `
                            <tr class="hover:bg-gray-50 ${impactClass}">
                                <td class="py-2 px-4 border-b">
                                    <div class="font-medium">${rec.title}</div>
                                    <div class="text-xs text-gray-500">${rec.description}</div>
                                </td>
                                <td class="py-2 px-4 border-b">${rec.resourceType}</td>
                                <td class="py-2 px-4 border-b">${rec.subscriptionName}</td>
                                <td class="py-2 px-4 border-b text-right font-medium text-green-600">${rec.potentialSavings.formatted}</td>
                                <td class="py-2 px-4 border-b text-center">${lastUpdated}</td>
                                <td class="py-2 px-4 border-b text-center">
                                    <a href="${rec.resourceLink}" target="_blank" class="inline-block px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                                        View
                                    </a>
                                </td>
                            </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4 text-center">
                <a href="https://portal.azure.com/#blade/Microsoft_Azure_CostManagement/Menu/overview" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    View All in Azure Portal
                </a>
            </div>
        </div>
    `;
    
    costRecommendationsContainer.innerHTML = recommendationsHtml;
}

// Export functions to be used in other modules
window.costManagement = {
    getCostRecommendations,
    displayCostRecommendations
};
