// Function to get tenant information and security score
async function getTenantInfo() {
    try {
        const account = msalInstance.getActiveAccount();
        if (!account) {
            throw new Error('No active account! Please sign in first.');
        }

        // Validate token before proceeding
        const isTokenValid = await validateToken();
        if (!isTokenValid) {
            throw new Error('interaction_required: Session has expired. Please sign in again.');
        }

        // Request token with necessary scopes for both organization info and security score
        const tokenRequest = {
            account: account,
            scopes: ["User.Read", "Organization.Read.All", "SecurityEvents.Read.All"]
        };
        
        const tokenResponse = await msalInstance.acquireTokenSilent(tokenRequest);
        const accessToken = tokenResponse.accessToken;
        
        // Fetch tenant information
        const tenantInfo = await fetchGraphData('https://graph.microsoft.com/v1.0/organization', accessToken);
        
        if (!tenantInfo.value || tenantInfo.value.length === 0) {
            throw new Error('No tenant information found');
        }
        
        // Get security score
        const securityScore = await getSecurityScore(accessToken);
        
        // Display the tenant information and security score
        displayTenantInfo(tenantInfo.value[0], securityScore);
    } catch (error) {
        console.error('Error getting tenant information:', error);
        
        // Check for token expiration errors
        if (error.message.includes('interaction_required') || 
            error.message.includes('AADSTS70044') || 
            error.message.includes('session has expired')) {
            
            console.log('Session expired during tenant info fetch, logging out');
            // Handle expired session by logging out
            if (typeof signOut === 'function') {
                signOut();
            }
        }
        
        const tenantDetails = document.getElementById('tenantDetails');
        if (tenantDetails) {
            tenantDetails.innerHTML = `
                <div class="bg-red-50 p-4 rounded text-red-600">
                    Error fetching tenant information: ${error.message}
                </div>
            `;
        }
    }
}

// Helper function to fetch data from Microsoft Graph
async function fetchGraphData(url, accessToken) {
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Graph API Error:', errorData);
            
            // Check for token expiration errors in the response
            const errorMessage = errorData.error ? errorData.error.message : 'Unknown error';
            if (errorMessage.includes('interaction_required') || 
                errorMessage.includes('AADSTS70044') || 
                errorMessage.includes('session has expired')) {
                
                throw new Error(`interaction_required: ${errorMessage}`);
            }
            
            throw new Error(`Graph API error: ${errorMessage}`);
        }

        const data = await response.json();
        console.log(`Graph API Response from ${url}:`, data);
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        
        // If this is a network error, it could be due to an expired token
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
            // Validate token to check if it's expired
            const isTokenValid = await validateToken();
            if (!isTokenValid) {
                throw new Error('interaction_required: Session may have expired. Please sign in again.');
            }
        }
        
        throw error;
    }
}

// Function to get security score and improvement actions
async function getSecurityScore(accessToken) {
    try {
        // Fetch secure score from Microsoft Graph Security API
        const secureScoreData = await fetchGraphData('https://graph.microsoft.com/v1.0/security/secureScores?$top=1', accessToken);
        
        if (!secureScoreData.value || secureScoreData.value.length === 0) {
            console.warn('No security score data found');
            return null;
        }
        
        // Get the latest secure score
        const latestScore = secureScoreData.value[0];
        
        try {
            // Fetch security control profiles (improvement actions)
            const controlProfilesUrl = `https://graph.microsoft.com/v1.0/security/secureScoreControlProfiles`;
            const controlProfiles = await fetchGraphData(controlProfilesUrl, accessToken);
            
            if (controlProfiles && controlProfiles.value && controlProfiles.value.length > 0) {
                // Process the control profiles to extract improvement actions
                const improvementActions = controlProfiles.value.map(profile => {
                    // Calculate score impact as a percentage of the max score
                    const scoreImpact = profile.maxScore ? profile.maxScore / latestScore.maxScore : 0;
                    
                    return {
                        id: profile.id,
                        title: profile.title || profile.displayName || 'Unknown action',
                        scoreImpact: scoreImpact,
                        implementationCost: profile.implementationCost || 'Unknown',
                        userImpact: profile.userImpact || 'Unknown',
                        description: profile.description || '',
                        remediation: profile.remediation || '',
                        isImplemented: profile.controlStateUpdates && profile.controlStateUpdates.some(state => state.state === 'Implemented')
                    };
                });
                
                // Filter out already implemented actions
                const pendingActions = improvementActions.filter(action => !action.isImplemented);
                
                // Sort by score impact (highest to lowest) and take top 5
                latestScore.improvementActions = pendingActions
                    .sort((a, b) => b.scoreImpact - a.scoreImpact)
                    .slice(0, 5);
            } else {
                console.error('No control profiles found');
                throw new Error('No control profiles found');
            }
        } catch (controlError) {
            console.error('Error fetching control profiles:', controlError);
            throw new Error('Error fetching control profiles');
        }
        
        return latestScore;
    } catch (error) {
        console.error('Error fetching security score:', error);
        return null;
    }
}

// Function to display tenant information and security score
function displayTenantInfo(tenantInfo, securityScore) {
    const tenantDetails = document.getElementById('tenantDetails');
    
    let securityScoreHtml = '';
    if (securityScore) {
        const scorePercentage = Math.round((securityScore.currentScore / securityScore.maxScore) * 100);
        const scoreColor = getScoreColor(scorePercentage);
        
        // Generate improvement actions HTML if available
        let improvementActionsHtml = '';
        if (securityScore.improvementActions && securityScore.improvementActions.length > 0) {
            improvementActionsHtml = `
                <div class="mt-4">
                    <h4 class="font-bold text-md mb-2">Top 5 Improvement Actions</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200">
                            <thead>
                                <tr>
                                    <th class="py-2 px-4 border-b text-left">Action</th>
                                    <th class="py-2 px-4 border-b text-right">Score Impact</th>
                                    <th class="py-2 px-4 border-b text-center">User Impact</th>
                                    <th class="py-2 px-4 border-b text-center">Implementation</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${securityScore.improvementActions.map(action => {
                                    // Format the score impact as percentage
                                    const impactPercentage = (action.scoreImpact * 100).toFixed(2);
                                    
                                    // Determine user impact color
                                    let userImpactClass = 'text-yellow-600';
                                    if (action.userImpact === 'Low') {
                                        userImpactClass = 'text-green-600';
                                    } else if (action.userImpact === 'High') {
                                        userImpactClass = 'text-red-600';
                                    }
                                    
                                    // Determine implementation difficulty color
                                    let implementationClass = 'text-yellow-600';
                                    if (action.implementationCost === 'Low') {
                                        implementationClass = 'text-green-600';
                                    } else if (action.implementationCost === 'High') {
                                        implementationClass = 'text-red-600';
                                    }
                                    
                                    // Add description if available
                                    const description = action.description 
                                        ? `<div class="text-xs text-gray-500 mt-1">${action.description}</div>` 
                                        : '';
                                    
                                    return `
                                    <tr class="hover:bg-gray-50">
                                        <td class="py-2 px-4 border-b">
                                            ${action.title}
                                            ${description}
                                        </td>
                                        <td class="py-2 px-4 border-b text-right font-medium">${impactPercentage}%</td>
                                        <td class="py-2 px-4 border-b text-center ${userImpactClass}">${action.userImpact || 'Unknown'}</td>
                                        <td class="py-2 px-4 border-b text-center ${implementationClass}">${action.implementationCost || 'Unknown'}</td>
                                    </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
        
        securityScoreHtml = `
            <div class="mt-4 p-4 rounded bg-white border border-gray-200">
                <h3 class="font-bold text-lg mb-2">Security Score</h3>
                <div class="flex items-center mb-2">
                    <div class="w-full bg-gray-200 rounded-full h-4 mr-2">
                        <div class="${scoreColor} h-4 rounded-full" style="width: ${scorePercentage}%"></div>
                    </div>
                    <span class="font-bold">${scorePercentage}%</span>
                </div>
                <p class="text-sm text-gray-600">Score: ${securityScore.currentScore} out of ${securityScore.maxScore}</p>
                <p class="text-sm text-gray-600">Last updated: ${new Date(securityScore.createdDateTime).toLocaleDateString()}</p>
                ${improvementActionsHtml}
            </div>
        `;
    } else {
        securityScoreHtml = `
            <div class="mt-4 p-4 rounded bg-yellow-50 border border-yellow-200">
                <p class="text-yellow-700">Security score information is not available.</p>
            </div>
        `;
    }
    
    tenantDetails.innerHTML = `
        <div class="bg-gray-50 p-4 rounded">
            <p class="font-semibold">Tenant ID:</p>
            <p class="text-gray-700 mb-2">${tenantInfo.id}</p>
            
            <p class="font-semibold">Display Name:</p>
            <p class="text-gray-700 mb-2">${tenantInfo.displayName}</p>
            
            <p class="font-semibold">Tenant Type:</p>
            <p class="text-gray-700">${tenantInfo.tenantType}</p>
        </div>
        ${securityScoreHtml}
    `;
}

// Helper function to get color class based on score percentage
function getScoreColor(percentage) {
    if (percentage >= 80) {
        return 'bg-green-500';
    } else if (percentage >= 60) {
        return 'bg-yellow-500';
    } else {
        return 'bg-red-500';
    }
}
