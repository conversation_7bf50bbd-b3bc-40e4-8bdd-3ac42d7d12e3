// MSAL configuration - simplified for stability
const msalConfig = {
    auth: {
        clientId: "db33db40-d444-402a-9b91-2d52c0f73ab6",
        authority: "https://login.microsoftonline.com/organizations",
        redirectUri: 'https://cloudflaresite-2ah.pages.dev'
    },
    cache: {
        cacheLocation: "localStorage",
        storeAuthStateInCookie: true
    }
};

// Create the MSAL instance directly
let msalInstance = null;

try {
    msalInstance = new msal.PublicClientApplication(msalConfig);
    console.log('MSAL initialized successfully');
} catch (error) {
    console.error('Error initializing MSAL:', error);
}

// Login request object
const loginRequest = {
    scopes: ["User.Read", "Organization.Read.All", "SecurityEvents.Read.All"]
};

// Function to validate token and handle expired sessions
async function validateToken() {
    console.log('Validating token');
    
    if (!msalInstance) {
        console.error('MSAL not initialized');
        return false;
    }
    
    const account = msalInstance.getActiveAccount();
    if (!account) {
        console.log('No active account found');
        return false;
    }
    
    try {
        // Try to silently acquire a token to test if the session is still valid
        const tokenRequest = {
            account: account,
            scopes: ["User.Read"],
            forceRefresh: true // Force refresh to ensure we're not using a cached token
        };
        
        await msalInstance.acquireTokenSilent(tokenRequest);
        console.log('Token is valid');
        return true;
    } catch (error) {
        console.error('Token validation failed:', error);
        
        // Check for specific error types that indicate an expired session
        if (error.name === 'InteractionRequiredAuthError' || 
            error.message.includes('interaction_required') ||
            error.message.includes('AADSTS70044') ||
            error.message.includes('session has expired')) {
            
            console.log('Session expired, logging out automatically');
            // Handle expired session by logging out
            signOut();
            return false;
        }
        
        return false;
    }
}

// Initialize the UI
function initializeUI() {
    console.log('Initializing UI');
    
    if (!msalInstance) {
        console.error('MSAL not initialized');
        return;
    }
    
    const loginButton = document.getElementById('loginButton');
    const logoutButton = document.getElementById('logoutButton');
    const welcomeMessage = document.getElementById('welcomeMessage');
    const tenantInfo = document.getElementById('tenantInfo');
    
    if (!loginButton) {
        console.error('Login button not found');
        return;
    }
    
    // Make sure elements are visible/hidden as appropriate
    loginButton.classList.remove('hidden');
    welcomeMessage.classList.remove('hidden');
    logoutButton.classList.add('hidden');
    tenantInfo.classList.add('hidden');

    // Add click event handlers
    loginButton.addEventListener('click', function(event) {
        console.log('Login button clicked');
        event.preventDefault();
        signIn();
    });
    
    logoutButton.addEventListener('click', function(event) {
        console.log('Logout button clicked');
        event.preventDefault();
        signOut();
    });

    // Check if we have accounts in the cache
    const accounts = msalInstance.getAllAccounts();
    console.log('Accounts found:', accounts.length);
    
    if (accounts.length > 0) {
        // User is already signed in, but validate the token first
        msalInstance.setActiveAccount(accounts[0]);
        validateToken().then(isValid => {
            if (isValid) {
                updateUI(true);
                getTenantInfo();
            } else {
                // Token is invalid, UI will be updated by the signOut function
                console.log('Token validation failed during initialization');
            }
        });
    } else {
        // No user signed in
        updateUI(false);
    }
    
    // Set up periodic token validation (every 5 minutes)
    setInterval(() => {
        if (msalInstance.getActiveAccount()) {
            validateToken();
        }
    }, 5 * 60 * 1000);
}

// Sign in function - try popup first, then fallback to redirect
function signIn() {
    console.log('Sign in function called');
    
    if (!msalInstance) {
        console.error('MSAL not initialized');
        return;
    }
    
    // Try popup first
    msalInstance.loginPopup(loginRequest)
        .then(response => {
            console.log('Login successful:', response);
            msalInstance.setActiveAccount(response.account);
            updateUI(true);
            getTenantInfo();
        })
        .catch(error => {
            console.error('Popup login failed:', error);
            // If popup fails, clear any interaction state and try redirect
            if (error.name === 'BrowserAuthError' || error.name === 'InteractionRequiredAuthError') {
                console.log('Falling back to redirect login...');
                // Clear any existing state
                sessionStorage.clear();
                localStorage.clear();
                // Try redirect login
                setTimeout(() => {
                    msalInstance.loginRedirect(loginRequest);
                }, 1000);
            }
        });
}

// Sign out function
function signOut() {
    console.log('Sign out function called');
    
    if (!msalInstance) {
        console.error('MSAL not initialized');
        return;
    }
    
    // Get the active account
    const activeAccount = msalInstance.getActiveAccount();
    
    // Clear all auth state
    sessionStorage.clear();
    localStorage.clear();
    
    // Clear MSAL cache and cookies
    msalInstance.clearCache();
    
    // Update the UI to show logged out state
    updateUI(false);
    
    // Show a message if this was an automatic logout due to expired session
    const tenantDetails = document.getElementById('tenantDetails');
    if (tenantDetails) {
        tenantDetails.innerHTML = '';
    }
    
    const welcomeMessage = document.getElementById('welcomeMessage');
    if (welcomeMessage) {
        const sessionExpiredParam = new URLSearchParams(window.location.search).get('sessionExpired');
        if (sessionExpiredParam === 'true' || (activeAccount && !window.manualLogout)) {
            welcomeMessage.innerHTML = `
                <h2 class="text-2xl font-bold mb-4">Session Expired</h2>
                <p class="text-gray-600 mb-4">Your session has expired. Please sign in again to continue.</p>
                <p class="text-gray-600">Welcome to Azure Tenant Health Monitor</p>
            `;
        } else {
            welcomeMessage.innerHTML = `
                <h2 class="text-2xl font-bold mb-4">Welcome to Azure Tenant Health Monitor</h2>
                <p class="text-gray-600">Please sign in to view your tenant information.</p>
            `;
        }
    }
    
    // Force a hard reload of the page to clear everything
    if (!window.location.search.includes('sessionExpired')) {
        window.location.href = window.location.origin + window.location.pathname + '?sessionExpired=true&refresh=' + new Date().getTime();
    } else {
        // If we're already on a page with sessionExpired parameter, just reload
        window.location.reload(true);
    }
    
    // As a backup, if the redirect doesn't work, reload after a short delay
    setTimeout(() => {
        window.location.reload(true);
    }, 300);
}

// Update UI based on authentication state
function updateUI(isAuthenticated) {
    console.log('Updating UI, authenticated:', isAuthenticated);
    
    const loginButton = document.getElementById('loginButton');
    const logoutButton = document.getElementById('logoutButton');
    const welcomeMessage = document.getElementById('welcomeMessage');
    const tenantInfo = document.getElementById('tenantInfo');

    if (isAuthenticated) {
        loginButton.classList.add('hidden');
        logoutButton.classList.remove('hidden');
        welcomeMessage.classList.add('hidden');
        tenantInfo.classList.remove('hidden');
        
        // Track that we're authenticated
        window.isAuthenticated = true;
    } else {
        loginButton.classList.remove('hidden');
        logoutButton.classList.add('hidden');
        welcomeMessage.classList.remove('hidden');
        tenantInfo.classList.add('hidden');
        
        // Track that we're not authenticated
        window.isAuthenticated = false;
    }
}

// Initialize when the page loads
window.addEventListener('load', function() {
    console.log('Window loaded, initializing application');
    initializeUI();
});

// Also initialize on DOMContentLoaded for faster startup
document.addEventListener('DOMContentLoaded', initializeUI);

// Track manual logout
document.getElementById('logoutButton')?.addEventListener('click', function() {
    window.manualLogout = true;
});
