// Function to get tenant information
async function getTenantInfo() {
    try {
        const account = msalInstance.getActiveAccount();
        if (!account) {
            throw new Error('No active account! Please sign in first.');
        }

        // Validate token before proceeding
        const isTokenValid = await validateToken();
        if (!isTokenValid) {
            throw new Error('interaction_required: Session has expired. Please sign in again.');
        }

        // Request token with necessary scopes for organization info and security score
        const tokenRequest = {
            account: account,
            scopes: ["User.Read", "Organization.Read.All", "SecurityEvents.Read.All"]
        };
        
        const tokenResponse = await msalInstance.acquireTokenSilent(tokenRequest);
        const accessToken = tokenResponse.accessToken;
        
        // Fetch tenant information
        const tenantInfo = await fetchGraphData('https://graph.microsoft.com/v1.0/organization', accessToken);
        
        if (!tenantInfo.value || tenantInfo.value.length === 0) {
            throw new Error('No tenant information found');
        }
        
        try {
            // Get security score
            const securityScore = await getSecurityScore(accessToken);
            
            // Display the tenant information and security score
            displayTenantInfo(tenantInfo.value[0], securityScore);
            
            // Show the tenant details section
            document.getElementById('tenantDetails').classList.remove('hidden');
            
            // After displaying tenant info, try to fetch and display cost recommendations
            try {
                // Request token for Azure Management API with correct scope
                const azureTokenRequest = {
                    account: account,
                    scopes: ["https://management.azure.com/user_impersonation"]
                };
                
                const azureTokenResponse = await msalInstance.acquireTokenSilent(azureTokenRequest);
                const azureAccessToken = azureTokenResponse.accessToken;
                
                // Show the cost management section
                document.getElementById('costManagement').classList.remove('hidden');
                
                // Fetch and display cost recommendations if the costManagement module is available
                if (window.costManagement) {
                    const costRecommendations = await window.costManagement.getCostRecommendations(azureAccessToken);
                    window.costManagement.displayCostRecommendations(costRecommendations);
                } else {
                    console.error('Cost management module not loaded');
                    document.getElementById('costRecommendations').innerHTML = `
                        <div class="p-4 rounded bg-yellow-50 border border-yellow-200">
                            <p class="text-yellow-700">Cost management module not available.</p>
                        </div>
                    `;
                }
            } catch (costError) {
                console.error('Error getting cost recommendations:', costError);
                document.getElementById('costManagement').classList.remove('hidden');
                document.getElementById('costRecommendations').innerHTML = `
                    <div class="p-4 rounded bg-yellow-50 border border-yellow-200">
                        <p class="text-yellow-700">Unable to fetch cost recommendations: ${costError.message}</p>
                        <div class="mt-4 text-center">
                            <a href="https://portal.azure.com/#blade/Microsoft_Azure_CostManagement/Menu/overview" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                View in Azure Portal
                            </a>
                        </div>
                    </div>
                `;
            }
        } catch (securityError) {
            console.error('Error getting security score:', securityError);
            
            // Display tenant info but with error message for security score
            const tenantDetails = document.getElementById('tenantDetails');
            if (tenantDetails) {
                const tenantInfoHtml = `
                    <div class="bg-gray-50 p-4 rounded">
                        <p class="font-semibold">Tenant ID:</p>
                        <p class="text-gray-700 mb-2">${tenantInfo.value[0].id}</p>
                        
                        <p class="font-semibold">Display Name:</p>
                        <p class="text-gray-700 mb-2">${tenantInfo.value[0].displayName}</p>
                        
                        <p class="font-semibold">Tenant Type:</p>
                        <p class="text-gray-700">${tenantInfo.value[0].tenantType}</p>
                    </div>
                    <div class="mt-4 p-4 rounded bg-yellow-50 border border-yellow-200">
                        <p class="text-yellow-700 mb-4">Security score information is not available.</p>
                        <div class="text-center">
                            <a href="https://security.microsoft.com/securescore" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                View Security Score on Microsoft Portal
                            </a>
                        </div>
                    </div>
                `;
                tenantDetails.innerHTML = tenantInfoHtml;
                tenantDetails.classList.remove('hidden');
            }
            
            // Still try to load cost recommendations even if security score fails
            try {
                // Request token for Azure Management API
                const azureTokenRequest = {
                    account: account,
                    scopes: ["https://management.azure.com/user_impersonation"]
                };
                
                const azureTokenResponse = await msalInstance.acquireTokenSilent(azureTokenRequest);
                const azureAccessToken = azureTokenResponse.accessToken;
                
                // Show the cost management section
                document.getElementById('costManagement').classList.remove('hidden');
                
                // Fetch and display cost recommendations
                if (window.costManagement) {
                    const costRecommendations = await window.costManagement.getCostRecommendations(azureAccessToken);
                    window.costManagement.displayCostRecommendations(costRecommendations);
                } else {
                    console.error('Cost management module not loaded');
                    document.getElementById('costRecommendations').innerHTML = `
                        <div class="p-4 rounded bg-yellow-50 border border-yellow-200">
                            <p class="text-yellow-700">Cost management module not available.</p>
                        </div>
                    `;
                }
            } catch (costError) {
                console.error('Error getting cost recommendations:', costError);
                document.getElementById('costManagement').classList.remove('hidden');
                document.getElementById('costRecommendations').innerHTML = `
                    <div class="p-4 rounded bg-yellow-50 border border-yellow-200">
                        <p class="text-yellow-700">Unable to fetch cost recommendations: ${costError.message}</p>
                        <div class="mt-4 text-center">
                            <a href="https://portal.azure.com/#blade/Microsoft_Azure_CostManagement/Menu/overview" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                View in Azure Portal
                            </a>
                        </div>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('Error getting tenant information:', error);
        
        // Check for token expiration errors
        if (error.message.includes('interaction_required') || 
            error.message.includes('AADSTS70044') || 
            error.message.includes('session has expired')) {
            
            console.log('Session expired during tenant info fetch, logging out');
            // Handle expired session by logging out
            if (typeof signOut === 'function') {
                signOut();
            }
        }
        
        const tenantDetails = document.getElementById('tenantDetails');
        if (tenantDetails) {
            tenantDetails.innerHTML = `
                <div class="bg-red-50 p-4 rounded text-red-600">
                    Error fetching tenant information: ${error.message}
                </div>
            `;
            tenantDetails.classList.remove('hidden');
        }
        
        // Hide cost management section if tenant info fails
        document.getElementById('costManagement').classList.add('hidden');
    }
}

// Function to get security score and improvement actions
async function getSecurityScore(accessToken) {
    try {
        // Fetch secure score from Microsoft Graph Security API
        const secureScoreData = await fetchGraphData('https://graph.microsoft.com/v1.0/security/secureScores?$top=1', accessToken);
        
        if (!secureScoreData.value || secureScoreData.value.length === 0) {
            console.warn('No security score data found');
            return null;
        }
        
        // Get the latest secure score
        const latestScore = secureScoreData.value[0];
        
        // We're no longer fetching improvement actions, just returning the score
        return latestScore;
    } catch (error) {
        console.error('Error fetching security score:', error);
        throw error;
    }
}

// Helper function to fetch data from Microsoft Graph API
async function fetchGraphData(url, accessToken) {
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Graph API Error:', errorData);
            
            // Check for token expiration errors in the response
            const errorMessage = errorData.error ? errorData.error.message : 'Unknown error';
            if (errorMessage.includes('interaction_required') || 
                errorMessage.includes('AADSTS70044') || 
                errorMessage.includes('session has expired')) {
                
                throw new Error(`interaction_required: ${errorMessage}`);
            }
            
            throw new Error(`Graph API error: ${errorMessage}`);
        }

        const data = await response.json();
        console.log(`Graph API Response from ${url}:`, data);
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        
        // If this is a network error, it could be due to an expired token
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
            // Validate token to check if it's expired
            const isTokenValid = await validateToken();
            if (!isTokenValid) {
                throw new Error('interaction_required: Session may have expired. Please sign in again.');
            }
        }
        
        throw error;
    }
}

// Function to display tenant information and security score
function displayTenantInfo(tenantInfo, securityScore) {
    const tenantDetails = document.getElementById('tenantDetails');
    
    // Generate tenant info HTML
    const tenantInfoHtml = `
        <div class="bg-gray-50 p-4 rounded">
            <p class="font-semibold">Tenant ID:</p>
            <p class="text-gray-700 mb-2">${tenantInfo.id}</p>
            
            <p class="font-semibold">Display Name:</p>
            <p class="text-gray-700 mb-2">${tenantInfo.displayName}</p>
            
            <p class="font-semibold">Tenant Type:</p>
            <p class="text-gray-700">${tenantInfo.tenantType}</p>
        </div>
    `;
    
    // Generate security score HTML based on available data or error
    let securityScoreHtml = '';
    
    if (securityScore) {
        const scorePercentage = Math.round((securityScore.currentScore / securityScore.maxScore) * 100);
        const scoreColor = getScoreColor(scorePercentage);
        
        securityScoreHtml = `
            <div class="mt-4 p-4 rounded bg-white border border-gray-200">
                <h3 class="font-bold text-lg mb-2">Security Score</h3>
                <div class="flex items-center mb-2">
                    <div class="w-full bg-gray-200 rounded-full h-4 mr-2">
                        <div class="${scoreColor} h-4 rounded-full" style="width: ${scorePercentage}%"></div>
                    </div>
                    <span class="font-bold">${scorePercentage}%</span>
                </div>
                <p class="text-sm text-gray-600">Score: ${securityScore.currentScore} out of ${securityScore.maxScore}</p>
                <p class="text-sm text-gray-600">Last updated: ${new Date(securityScore.createdDateTime).toLocaleDateString()}</p>
                
                <div class="mt-4 text-center">
                    <a href="https://security.microsoft.com/securescore" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        View Detailed Security Recommendations
                    </a>
                    <p class="text-xs text-gray-500 mt-2">For detailed security recommendations and improvement actions, visit the Microsoft Security portal</p>
                </div>
            </div>
        `;
    } else {
        securityScoreHtml = `
            <div class="mt-4 p-4 rounded bg-yellow-50 border border-yellow-200">
                <p class="text-yellow-700 mb-4">Security score information is not available.</p>
                <div class="text-center">
                    <a href="https://security.microsoft.com/securescore" target="_blank" class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        View Security Score on Microsoft Portal
                    </a>
                </div>
            </div>
        `;
    }
    
    // Combine tenant info and security score HTML
    tenantDetails.innerHTML = tenantInfoHtml + securityScoreHtml;
}

// Helper function to get color class based on score percentage
function getScoreColor(percentage) {
    if (percentage >= 80) {
        return 'bg-green-500';
    } else if (percentage >= 60) {
        return 'bg-yellow-500';
    } else {
        return 'bg-red-500';
    }
}
