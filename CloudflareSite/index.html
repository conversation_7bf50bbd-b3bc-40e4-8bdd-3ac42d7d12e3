<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Tenant Health Monitor v1.14.0</title>
    <script src="https://alcdn.msauth.net/browser/2.30.0/js/msal-browser.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/cloudfactory-theme.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex flex-col">
        <nav class="nav-gradient text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-xl font-bold">Azure Tenant Health Monitor</h1>
                <div id="authButtons">
                    <button id="loginButton" class="bg-white text-indigo-600 px-4 py-2 rounded">Sign In</button>
                    <button id="logoutButton" class="bg-white text-indigo-600 px-4 py-2 rounded hidden">Sign Out</button>
                </div>
            </div>
        </nav>

        <main class="container mx-auto flex-grow p-4">
            <div id="welcomeMessage" class="text-center py-8">
                <h2 class="text-2xl font-bold mb-4">Welcome to Azure Tenant Health Monitor</h2>
                <p class="text-gray-600">Please sign in to view your tenant information.</p>
            </div>

            <div id="tenantInfo" class="bg-white rounded-lg shadow-md p-6 hidden">
                <h2 class="text-xl font-bold mb-4">Tenant Information</h2>
                <div id="tenantDetails" class="space-y-4">
                    <!-- Tenant details will be populated here -->
                </div>
            </div>

            <div id="costManagement" class="bg-white rounded-lg shadow-md p-6 hidden">
                <h2 class="text-xl font-bold mb-4">Cost Management</h2>
                <div id="costRecommendations" class="space-y-4">
                    <!-- Cost recommendations will be populated here -->
                </div>
            </div>
        </main>

        <footer class="bg-gray-200 text-center p-4 mt-8">
            <p class="text-gray-600">&copy; 2025 Azure Tenant Health Monitor | v1.14.0</p>
        </footer>
    </div>

    <script src="/src/auth.js"></script>
    <script src="/src/app.js"></script>
    <script>
        // Ensure scripts are properly loaded and initialized
        window.addEventListener('load', function() {
            console.log('Window loaded, initializing application');
            if (typeof initializeUI === 'function') {
                initializeUI();
            } else {
                console.error('initializeUI function not found');
            }
        });
    </script>
</body>
</html>
