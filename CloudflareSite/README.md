# Azure Tenant Health Monitor

A web application that allows users to authenticate with Azure Active Directory and view information about their Azure tenant.

## Features

- Azure AD authentication using MSAL.js
- Display of tenant information including ID, display name, and tenant type
- Security score visualization with color-coded progress bar
- Top 5 security improvement actions with score impact and implementation cost
- Clean, responsive UI built with Tailwind CSS

## Technical Details

- Uses Microsoft Authentication Library (MSAL) for browser-based authentication
- Implements popup-based authentication with fallback to redirect
- Fetches tenant information from Microsoft Graph API

## Deployment

This application is configured to be deployed on Cloudflare Pages. To deploy:

1. Push this repository to GitHub
2. Connect your GitHub repository to Cloudflare Pages
3. Configure the build settings:
   - Build command: (not required for static site)
   - Build output directory: / (root directory)

## Local Development

To test locally, simply open the `index.html` file in your web browser. Note that authentication requires proper Azure AD app registration and configuration.

## Version History

### v1.14.0
- Update version history to include this version

### v1.13.0
- Update version history to include this version

### v1.12.0
- Simplified security score display
- Removed detailed security recommendations table
- Added direct link to Microsoft Security Score portal
- Improved user experience with cleaner interface

### v1.11.0
- Added product information to security recommendations display
- Improved product name extraction from API data
- Enhanced security recommendations table with product column

### v1.10.0
- Enhanced error handling for security score API calls
- Added dedicated error display for security recommendation failures
- Improved separation of tenant info and security score display
- Better user feedback when security recommendations cannot be fetched
### v1.9.0
- Added token validation and session expiration handling, improved security recommendations to use dynamic data from each tenant
- Removed hardcoded fallback data for security improvement actions
- Added error handling for when security recommendations are not available
- Improved UI for displaying security recommendations

### v1.8.0
- Replaced hardcoded improvement actions with dynamic API fetching and added fallback mechanism

### v1.7.0
- Updated security improvement actions to match security.microsoft.com recommendations and sorted by score impact

### v1.6.0
- Added display of top 5 security improvement actions with score impact

### v1.5.0
- Added security score visualization with color-coded progress bar

### v1.4.0
- Cleaned up codebase to focus on core authentication and tenant information functionality

### v1.3.2
- Improved logout with automatic page refresh

### v1.3.1
- Fixed logout to avoid account selection popup

### v1.3.0
- Implemented popup-first authentication with fallback to redirect

### v1.2.0
- Complete rewrite of authentication system for stability
