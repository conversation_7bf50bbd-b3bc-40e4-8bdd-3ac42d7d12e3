#
# Module manifest for module 'PSGet_CloudFactory'
#
# Generated by: <PERSON>
#
# Generated on: 20-05-2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = '.\CloudFactory.psm1'

# Version number of this module.
ModuleVersion = '1.0.0'

# Supported PSEditions
# CompatiblePSEditions = @()

# ID used to uniquely identify this module
GUID = 'e327311d-bdd5-4053-90e2-01141fb2cec5'

# Author of this module
Author = '<PERSON>rn<PERSON>'

# Company or vendor of this module
CompanyName = 'Cloud Factory A/S'

# Copyright statement for this module
Copyright = '(c) 2019 Cloud Factory. All rights reserved.'

# Description of the functionality provided by this module
Description = 'CF Fabric general use'

# Minimum version of the Windows PowerShell engine required by this module
# PowerShellVersion = ''

# Name of the Windows PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the Windows PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# DotNetFrameworkVersion = ''

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# CLRVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
# RequiredModules = @()

# Assemblies that must be loaded prior to importing this module
# RequiredAssemblies = @()

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
# FormatsToProcess = @()

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-CiscoEPGPhysicalDomain', 'Add-LogStats', 
               'Add-NetboxDeviceInterface', 'Add-NutanixProjectToCluster', 
               'Add-NutanixSubnetToProject', 'Add-PRTGOutputChannel', 
               'Add-PRTGOutputMessage', 'Add-ToVDOM', 'Add-NBVLANToNutanixProject', 
               'Add-VMsToProtectionDomain', 'Start-CFExecution', 'New-CFGelfObject', 
               'Repair-CFGelfObject', 'Send-CFGelfTCPFromObject', 'Send-CFgelftcp', 
               'Write-CFError', 'Write-CFInfo', 'Write-CFWarning', 
               'Send-CFExecutioninfo', 'Get-CFFunctionName', 'CheckClusterIsOK', 
               'Connect-CiscoAPIC', 'Connect-PRTG', 
               'Convert-CFPortalMicrosoftIDToSKU', 
               'Convert-NutanixNICToNetboxVLANMode', 'Convert-UnixToDateTime', 
               'ConvertTo-FlattenedCustomObject', 'Create-NutanixSubnet', 
               'Create-NutanixSubnetAndNetbox', 'Decrypt-SecureString', 
               'Drain-HyperVHost', 'Drain-HyperVHostV2', 'Enable-HyperVHost', 
               'Encrypt-Securestring', 'Execute-Command', 
               'Find-VMUsingLogicalNetwork', 'Find-VMUsingVLANID', 'Fix-Module', 
               'Get-AccessCredentials', 'Get-CFPortalAccessToken', 
               'Get-CFPortalAccounts', 'Get-CFPortalCatalogueProducts', 
               'Get-CFPortalEndCustomerLicenses', 'Get-CFPortalEndCustomers', 
               'Get-CFPortalEndCustomerScheduledSubscriptions', 
               'Get-CFPortalEndCustomerSeats', 
               'Get-CFPortalEndCustomerSubscriptions', 
               'Get-CFPortalMicrosoftProducts', 'Get-CFPortalPartners', 
               'Get-CFPortalProducts', 'Get-CFPortalProjects', 
               'Get-CFPortalWAPTenants', 'Get-CiscoNodes', 
               'Get-CiscoVirtualPortChannels', 'Get-CiscoVRF', 
               'Get-ClusterRelationOnProjects', 'Get-ClusterVSwitches', 
               'Get-Confirmation', 'Get-CosmosDbCFDocuments', 'Get-CosmosDbCFVMs', 
               'Get-DomainAdmins', 'Get-EventsFromVM', 'Get-HycuEndpoint', 
               'Get-InfoFromDialog', 'Get-LongestString', 'Get-MACAddresses', 
               'Get-MacVendor', 'Get-NetboxCommands', 'Get-NetboxTenants', 
               'Get-NetboxTenantGroups', 'Get-NetboxVLANs', 'Get-NetboxPrefixes', 
               'Get-NetboxIpamRoles', 'Get-NetboxDevices', 
               'Get-NetboxDeviceInterfaces', 'Get-NetboxDeviceRoles', 
               'Get-NetboxDeviceTypes', 'Get-NetboxDeviceManufacturers', 
               'Get-NetboxVMs', 'Get-NetboxVMsInterfaces', 'Get-NetboxClusters', 
               'Get-NetboxClusterTypes', 'Get-NetboxClusterGroups', 
               'Get-NetboxHeader', 'Get-NutanixCapacity', 'Get-NutanixCommands', 
               'Get-NutanixVMs', 'Get-NutanixTask', 'Get-NutanixImages', 
               'Get-NutanixProject', 'Get-NutanixSubnets', 'Get-NutanixHosts', 
               'Get-NutanixClusters', 'Get-NutanixCategories', 
               'Get-NutanixCategories', 'Get-NutanixRecoveryPlans', 
               'Get-NutanixClusterVSwitches', 'Get-NutanixEndpoint', 
               'Get-NutanixHeader', 'Get-NutanixHostMetric', 'Get-NutanixHostsInfo', 
               'Get-NutanixMetricCluster', 'Get-NutanixMetricVM', 
               'Get-NutanixSnapshots', 'Get-NutanixHAEvents', 
               'Get-ProtectionDomainInfo', 'Get-ProtectionDomains', 
               'Get-ProtectionDomainsSnapshots', 'Get-PRTGOutput', 
               'Get-RandomCharacters', 'Get-ResourcesRecommendations', 
               'Get-SafeCredentials', 'Get-SSHCredential', 'Get-UnprotectedVMs', 
               'Get-Uptime', 'Get-VLANsInARow', 'Get-VMMTemplates', 
               'Get-VMsFromHostCrash', 'Get-WapTenants', 'Get-WindowsHealth', 
               'Install-CFDependencies', 'Invoke-5900SSHCommand', 
               'Invoke-CFNutanixCommandOld', 'Invoke-CFNutanixCommand', 
               'Invoke-CFPortalPagedCall', 'Invoke-CommandAsScheduledTask', 
               'Invoke-FortigateCommand', 'Invoke-GenereatePEPLink', 'Invoke-NBAPI', 
               'Invoke-NutanixHPEILOConfig', 'Invoke-ParallelJobs', 
               'Invoke-SSHCommandInStream', 'Get-5900VLAN', 'Invoke-5900MapVLAN', 
               'Get-FortigateVLANS', 'Invoke-TagMissingVLANs', 
               'Invoke-TrustAllCertificates', 'Invoke-WindowsUpdate', 
               'ISObjectsEqual', 'ListAllNTXVM', 'ListAllVMS', 
               'Measure-CommandReturnResult', 'Measure-StringDistance', 
               'Move-CFAzureSubscription', 'New-CiscoBridgeDomain', 
               'New-CiscoCFVlan', 'New-CiscoEPG', 'New-FortiVM', 'New-LogicalNetwork', 
               'New-NetboxTenant', 'New-NutanixProject', 'New-NutanixVM', 
               'New-VMFromTemplate', 'Pause-PRTGSCVMHost', 'New-PRTGOutput', 
               'Get-PRTGOutput', 'Add-PRTGOutputChannel', 'Add-PRTGOutputMessage', 
               'Measure-CommandReturnResult', 'Remove-LogicalNetwork', 
               'Remove-NutanixProject', 'Remove-NutanixSubnet', 'Remove-ReplicaVM', 
               'Remove-VMsFromProtectionDomain', 'Rename-NutanixProject', 
               'Request-LetsEncryptcertificate', 
               'Restart-ComputerAndWaitForUptime', 'Resume-PRTGSCVMHost', 
               'Get-SelectSCVirtualMachine', 'Get-SelectSCCloud', 
               'Get-SelectSCUserRole', 'Get-SelectAccessADUser', 
               'Get-SelectSCVMHostGroup', 'Get-SelectSCVMHostCluster', 
               'Get-SelectSCVMNetwork', 'Send-MessageToSlack', 'SendMailToSlack', 
               'Set-CFPortalEndCustomerSeat', 'Set-CiscoEPGInterfaces', 
               'Set-TestVariablesFromParameterBlock', 'Set-VMOwnerAndCloud', 
               'Invoke-TrustAllCertificates', 'Show-CFMenu', 'Show-Menu', 
               'Split-SlackMessages', 'Start-RemoteSupport', 
               'Start-TranscriptFunction', 'Sync-Git', 
               'Sync-PartnersFromPortalToNetbox', 'Sync-PSWorkers', 'Test-Function', 
               'Test-SafeCredentials', 'test1', 'Update-CloudfactoryModuleManifest', 
               'Update-FunctionsToExport', 'Update-NetboxVLAN'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = @()

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        # Tags = @()

        # A URL to the license for this module.
        # LicenseUri = ''

        # A URL to the main website for this project.
        # ProjectUri = ''

        # A URL to an icon representing this module.
        # IconUri = ''

        # ReleaseNotes of this module
        # ReleaseNotes = ''

        # External dependent modules of this module
        # ExternalModuleDependencies = ''

    } # End of PSData hashtable

} # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}

