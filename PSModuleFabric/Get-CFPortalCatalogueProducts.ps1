function Get-CFPortalCatalogueProducts {
    param (
        $baseURI,
        $Header,
        $PartnerID#="ee151380-e071-46ed-a2b6-e651c8ac55dc"
    )
    #If no baseURI and header is provided, then get it from the SafeCredentials command
    if (!$baseURI -and !$Header) {
        $Params = Test-SafeCredentials -Application portal-api-cloudfactory-dk
        $baseURI = $Params.baseURI
        $Header = $Params.Header
    }


    #Invoke-RestMethod -Uri $uri -Headers $header
    $splatParams = @{
        baseURI = $baseURI
        Header = $Header
        Endpoint = "/v2/catalogue/Products"
    }
    
    if ($PartnerID) {
        $splatParams.urlappend = "&partnerId=$PartnerID"
    }
    
    Invoke-CFPortalPagedCall @splatParams
}