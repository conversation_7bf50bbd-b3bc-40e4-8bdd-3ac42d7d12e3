function ConvertTo-FlattenedCustomObject {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, ValueFromPipeline = $true)]
        [psobject] $InputObject,

        [string] $ParentKey = ''
    )

    process {
        $flattened = [ordered]@{}

        if ($null -eq $InputObject) {
            # Handle null input explicitly if desired, or let parameter binding fail
            return $null
        }

        foreach ($property in $InputObject.PSObject.Properties) {
            $currentPropertyName = $property.Name
            $propertyValue = $property.Value

            $newKey = if (-not [string]::IsNullOrEmpty($ParentKey)) {
                "$ParentKey.$currentPropertyName"
            }
            else {
                $currentPropertyName
            }

            if ($propertyValue -is [System.Management.Automation.PSCustomObject]) {
                # Recurse for PSCustomObject
                $nestedFlattened = ConvertTo-FlattenedCustomObject -InputObject $propertyValue -ParentKey $newKey
                if ($null -ne $nestedFlattened) {
                    foreach ($entry in $nestedFlattened.PSObject.Properties) {
                        $flattened[$entry.Name] = $entry.Value
                    }
                }
            }
            elseif ($propertyValue -is [array]) {
                # Handle arrays: iterate elements, append 1-based index to key
                for ($i = 0; $i -lt $propertyValue.Count; $i++) {
                    $arrayElement = $propertyValue[$i]
                    $indexedItemKey = "$newKey$($i + 1)" 

                    if ($arrayElement -is [System.Management.Automation.PSCustomObject]) {
                        $nestedArrayElementItems = ConvertTo-FlattenedCustomObject -InputObject $arrayElement -ParentKey $indexedItemKey
                        if ($null -ne $nestedArrayElementItems) {
                            foreach ($entry in $nestedArrayElementItems.PSObject.Properties) {
                                $flattened[$entry.Name] = $entry.Value
                            }
                        }
                    }
                    else {
                        $flattened[$indexedItemKey] = $arrayElement
                    }
                }
            }
            else {
                # Simple value or other types
                $flattened[$newKey] = $propertyValue
            }
        }
        return [pscustomobject]$flattened
    }
}
