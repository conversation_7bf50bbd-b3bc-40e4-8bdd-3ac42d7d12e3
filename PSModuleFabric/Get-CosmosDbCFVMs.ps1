function Get-CosmosDbCFVMs {
    param (
        [int]$DaysBack = 0,
        [Parameter(Mandatory = $true)]
        [ValidateSet("Hyper-V","Nutanix")]
        [String]$Platform
    )

    $primaryKey = ConvertTo-SecureString -AsPlainText -Force -String 'clear text key'
    $credential=Test-SafeCredentials -Application CosmosDB
    $primaryKey = $credential.Password
    
    $CosmosDbContext = New-CosmosDbContext -Account 'strozzisqldb' -Database 'DK01' -Key $primaryKey
    
    $Offset = $DaysBack
    $i = 0
    while ($true) {
        $i++
        if ($i -gt 30) {
            throw "No results found"
        }
        $epochStart = get-date (Get-Date).AddDays(-$Offset - 1) -uformat %s
        $epochEnd = get-date (Get-Date).AddDays(-$Offset) -uformat %s
        
        
        switch ($Platform){
            "Hyper-V" {
                $Database="vm_logs"
                #$query = "SELECT * FROM vm_logs WHERE vm_logs.JobStartTimeEpoch > $epochStart AND vm_logs.JobStartTimeEpoch < $epochend "
            }
            "Nutanix" {
                $Database="ntx_vm_logs"
                #$query = "SELECT * FROM ntx_vm_logs WHERE ntx_vm_logs.JobStartTimeEpoch > $epochStart AND ntx_vm_logs.JobStartTimeEpoch < $epochend "
            }
        }
        $query="SELECT * FROM $($Database) WHERE $($Database).JobStartTimeEpoch > $epochStart AND $($Database).JobStartTimeEpoch < $epochend"
        $Documents<<<<< = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $Database -query  $query
        $JobCount = $Documents | Group-Object -Property JobStartTimeDate | measure | select -ExpandProperty Count

        if ($JobCount -gt 1) {
            throw "found results for more than one job"
        }
        elseif ($JobCount -eq 0) {
            Write-Verbose "No results for $epochStart - $epochEnd"
            $Offset++
            continue
        }
        elseif ($jobcount -eq 1) {
            return $Documents

        }
    }
}