function Invoke-CFPortalPagedCall {
    param (
        $baseURI=$params.baseuri,
        $Header=$params.Header,
        $Endpoint = "/v2/customers/Customers",
        $urlappend,
        [switch]$Parallel=$true
    )
    #$params=Test-SafeCredentials -Application portal-api-cloudfactory-dk
    $PageIndex = 1
    $PageSize = 100


    
    if ($PSVersionTable.PSVersion.Major -ge 7) {
        #powershell 7

        $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$($PageSize)$($urlappend)"
        $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60

        $TotalPages = $Result.metadata.totalpages
        $output = @()
        $output += $result.results
        $output += 2..$TotalPages | % -ThrottleLimit 50 -Parallel {
            $PageIndex = $PSItem
            $uri = "$($using:baseURI)$($using:Endpoint)?PageIndex=$PageIndex&PageSize=$($using:PageSize)$($using:urlappend)"
            $Result = Invoke-RestMethod -Uri $uri -Headers $using:header -TimeoutSec 60
            $result.results
        }
    }elseif ($Parallel){
        $scriptblock={
            Param(
                $Arguments
            )
            $ErrorActionPreference="Stop"

            $uri=$Arguments.uri
            $header=$Arguments.header
            

            $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
            
            
            $result.results
            
        }
        
        #powershell 5 parrallel
        write-verbose "Running powershell 5 parallel"
        $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$($PageSize)$($urlappend)"
        $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60

        $TotalPages = $Result.metadata.totalpages
        write-verbose "Total Pages: $TotalPages"
        $output = @()
        $output+=$Result.results
        $ArgumentList= @()
        $ArgumentList+=foreach ($PageIndex in (2..$TotalPages))    {
            write-verbose "creating argumentlist for page $PageIndex"
            [pscustomobject]@{
                URI="$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$($PageSize)$($urlappend)"
                Header=$header

            }
            
        }
        #Write-Warning "Total Pages: $TotalPages"
        #Write-Warning "Total Argumentlistcount: $($ArgumentList.count)"
        write-verbose "Total Argumentlistcount: $($ArgumentList.count)"
        $output += Invoke-ParallelJobs -scriptblock $scriptblock -ArgumentArray $ArgumentList -JobtimeoutSeconds 60
       

        
    }
    else{
    
        #powershell 5 seriel
        $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$($PageSize)$($urlappend)"
        $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
        $TotalPages = $Result.metadata.totalpages
        $output = @()
        $output += $result.results

        $output +=foreach ($PageIndex in (2..$TotalPages))    {
            
            write-progress -activity $Endpoint -status "Page: $PageIndex had $($result.results.count) entries" -PercentComplete ($PageIndex / $TotalPages * 100)
            $uri = "$($baseURI)$($Endpoint)?PageIndex=$PageIndex&PageSize=$($PageSize)$($urlappend)"
            if ($PageIndex -eq 0){
                try{
                    $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
                }catch{}

            }else {
                $Result = Invoke-RestMethod -Uri $uri -Headers $header -TimeoutSec 60
            }
            
            $result.results
            
        }

    }
    Return $output
}
