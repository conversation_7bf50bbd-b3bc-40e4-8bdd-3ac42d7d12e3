Function ListAllNTXVM {

    #region init
    $baseURI = "https://portal.api.cloudfactory.dk"
    while ($true) {
        $ErrorActionPreference = "Stop"
        try {
            #try to connect
            $credentialName="portal-api-cloudfactory-dk"
            $Creds=Get-SafeCredentials -CredentialName $credentialName -Verbose -Username "Enter Refreshtoken in password field:"
            $refreshtoken = Decrypt-SecureString $creds.Password
            Write-Verbose "Testing entered Refreshtoken"
            $params = Get-CFPortalAccessToken -baseURI $baseURI -RefreshToken $refreshtoken
            Write-Verbose "It worked yay!"
            break
        
        }
        catch {
            Write-Verbose "Didnt work.. sry :/"
            $Creds=Get-SafeCredentials -CredentialName $credentialName -Verbose -Username "Enter Refreshtoken in password field:" -FlushSavedCredentials
        }
    }

    #endregion
    $Partners=Get-CFPortalPartners @params
    $Projects=Get-CFPortalProjects @params
    $VMS=Get-NutanixVMs
    $Clusters = Get-NutanixClusters | ? { $_.status.name -ne "DK01PC01" }
    $snapshots=foreach ($Cluster in $Clusters) {
        Get-NutanixSnapshots -ClusterName $cluster.spec.name
    }

    $i=0
    $output=foreach ($vm in $VMS){
        $i++
        try{Write-Progress -Activity "Getting Virtual Machine Data" -Status $vm.name -PercentComplete ($i / $vms.count * 100)}catch{}

        try{
            $PartnerID=$Projects | ? id -eq $vm.metadata.project_reference.uuid |select -expand partnerid
            $Partner = $Partners | ? debitorId -eq $PartnerID | select -expand name
        }catch{
            $partner="Unknown"
        }
        $vmSnapshots = $snapshots | ? {$_.vm_uuid -eq $vm.metadata.uuid}
        #convert to string with date-name,date-name. The date is unixtime so we need to convert it to a date
        $vmSnapshotsString = $vmSnapshots | % {
            $date = Convert-UnixToDateTime -UnixTime $_.created_time
            $date.ToString("yyyy-MM-dd HH:mm:ss") + " - " + $_.snapshot_name }
        $vmSnapshotsString = $vmSnapshotsString -join "|"

        [pscustomobject]@{
            name=$vm.status.name
            Status=$vm.status.resources.power_state
            UUID=$vm.metadata.uuid
            Cluster=$vm.status.cluster_reference.name
            MemoryGB=[int](($vm.status.resources.memory_size_mib|measure -Sum).sum / 1KB)
            CPUCount=$vm.status.resources.num_sockets
            SizeGB=[int](($vm.status.resources.disk_list.disk_size_bytes|measure -Sum).sum / 1GB)
            IP=$vm.status.resources.nic_list.ip_endpoint_list.ip
            VLANID=$vm.status.resources.nic_list.subnet_reference.name
            snapshotcount = $snapshots | ? {$_.vm_uuid -eq $vm.metadata.uuid} |Measure-Object | select -expand count
            snapshots =$vmSnapshotsString
            
            Project=$vm.metadata.project_reference.name
            Partner=$partner
        }

    }
    $output
}