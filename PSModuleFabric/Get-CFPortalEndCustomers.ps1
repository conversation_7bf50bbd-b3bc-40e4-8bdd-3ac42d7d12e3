function Get-CFPortalEndCustomers {
    param (
        [Parameter(ParameterSetName = 'ByID')]
        [Parameter(ParameterSetName = 'ByPartnerID')]
        $baseURI,
        
        [Parameter(ParameterSetName = 'ByID')]
        [Parameter(ParameterSetName = 'ByPartnerID')]
        $Header,
        
        [Parameter(ParameterSetName = 'ByID')]
        $id,
        
        [Parameter(ParameterSetName = 'ByPartnerID')]
        $partnerID
    )
    #If no baseURI and header is provided, then get it from the SafeCredentials command
    if (!$baseURI -and !$Header) {
        $Params = Test-SafeCredentials -Application portal-api-cloudfactory-dk
        $baseURI = $Params.baseURI
        $Header = $Params.Header
    }
    if ($id) {
        #Invoke-CFPortalPagedCall -baseURI $baseURI -header $header -Endpoint "/v2/customers/Customers/byServiceID?service=0&serviceId=$id"
        Invoke-RestMethod -Uri "$baseURI/v2/customers/Customers/byServiceID?service=0&serviceId=$id" -Headers $header -TimeoutSec 60
    }
    elseif ($partnerID) {
        #Invoke-CFPortalPagedCall -baseURI $baseURI -header $header -Endpoint "/v2/customers/Customers?PageIndex=0&PageSize=100&Filter.PartnerLegacyId=$partnerID"
        $urlappend="&Filter.PartnerLegacyId=$partnerID"
        Invoke-CFPortalPagedCall -baseURI $baseURI -header $header -Endpoint "/v2/customers/Customers" -urlappend $urlappend
        
    }
    else {
        Invoke-CFPortalPagedCall -baseURI $baseURI -header $header -Endpoint "/v2/customers/Customers"
    }
}

