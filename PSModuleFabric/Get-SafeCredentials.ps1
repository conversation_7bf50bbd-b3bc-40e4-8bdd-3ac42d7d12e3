﻿function Get-SafeCredentials {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        $CredentialName = "PrismCentral",
        $CredentialRootPath = "\\DK01FS01\Main\!Creds",
        $Username,
        [switch]$FlushSavedCredentials = $false,
        [switch]$DontWait,
        [int]$TimeoutSeconds = 60 * 5,
        [switch]$LocalOnly#if true doesnt lookup in azure keyvault.
    )
    $cacheFilePath = "$env:TEMP\$CredentialName.xml"
    
    if ($IsMacOS -or $IsLinux) {
        $cacheFilePath = [System.IO.Path]::Combine([System.IO.Path]::GetTempPath(), "$CredentialName.xml")
    }
    
    if (Test-Path $cacheFilePath) {
        $cache = Import-Clixml -Path $cacheFilePath
        $cacheTime = [datetime]$cache.Time
        if ((Get-Date) - $cacheTime -lt [timespan]::FromDays(1)) {
            return $cache.Credentials
        }else{
            remove-item $cacheFilePath -Force -ErrorAction SilentlyContinue
        }
    }

    if(get-command Get-AutomationVariable -ErrorAction SilentlyContinue){
        $token = Get-AutomationVariable -Name 'OP_SERVICE_ACCOUNT_TOKEN'
        $env:OP_SERVICE_ACCOUNT_TOKEN = $token
        $env:PATH = $env:PATH.trimend(";") + ";C:\Program Files\WinGet\Links"
    }
    if($env:computername -match "PRTG"){
        $env:OP_SERVICE_ACCOUNT_TOKEN = [System.Environment]::GetEnvironmentVariable("OP_SERVICE_ACCOUNT_TOKEN","Machine")
        $env:PATH = $env:PATH.trimend(";") + ";C:\Program Files\WinGet\Links"
    }
    try{
        $1password = op item get $CredentialName --vault PSAutomation --fields username,password --reveal --format json  | ConvertFrom-Json    
    }
    catch{
        if(test-path 'c:\Program Files\WinGet\links\op.exe'){
            Set-Location 'c:\Program Files\WinGet\links'
            $1password = .\op.exe item get $CredentialName --vault PSAutomation --fields username,password --reveal --format json  | ConvertFrom-Json
        }
        else{
            throw "Could not find op.exe"
        }
    }
    if (-not $1password){throw "No credentials found for credentialname: $CredentialName"}


    $username = $1password | where id -eq username | select -ExpandProperty value
    $password = $1password | where id -eq password| select -ExpandProperty value
    $Credentials = new-object -typename System.Management.Automation.PSCredential -argumentlist $username, (ConvertTo-SecureString $password -AsPlainText -Force)
    $cache = @{
        Time = Get-Date
        Credentials = $credentials
    }
    $cache | Export-Clixml -Path $cacheFilePath
    
    return $Credentials
   <#  if ($env:COMPUTERNAME -like "DK01AZWRK0*") {
        $Credentials = Get-AutomationPSCredential -Name $CredentialName
        if (-not $Credentials){throw "No credentials found for credentialname: $CredentialName"}
        #uncomment this line for showing passwrod in cleartext in log
        #else{Write-Warning "credentialname: $CredentialName - Username: $($Credentials.username) Cleartext password: $(Decrypt-SecureString ($Credentials.password))"}
        return $Credentials
    }
    if (get-command "Get-AutomationPSCredential" -ErrorAction SilentlyContinue) {
        $Credentials = Get-AutomationPSCredential -Name $CredentialName
        if (-not $Credentials){throw "No credentials found for credentialname: $CredentialName"}
        #uncomment this line for showing passwrod in cleartext in log
        #else{Write-Warning "credentialname: $CredentialName - Username: $($Credentials.username) Cleartext password: $(Decrypt-SecureString ($Credentials.password))"}
        return $Credentials
    }
    
    $StopWatch = [system.diagnostics.stopwatch]::StartNew()
    $pathToCLIXMLCredential = (Join-Path -Path $env:APPDATA -ChildPath "$($CredentialName)Credentials.xml")
    
    $PathToTempClearTextPassword = join-path -Path $CredentialRootPath -ChildPath "$($CredentialName).txt"


    if ($FlushSavedCredentials -eq $true) { 
        Remove-Item $pathToCLIXMLCredential -Force -Confirm:$false -ErrorAction SilentlyContinue
    }


    if (Test-Path $pathToCLIXMLCredential) {
        Write-Verbose "Credentials Exists - Reading.."
        $Credentials = Import-Clixml $pathToCLIXMLCredential
        return $Credentials
    }
    else {
        Write-Verbose "Credentials dont exist. Trying to read cleartext password files."
        while ($true) {
            #If session is interactive. 
            if([System.Environment]::UserInteractive){
                
                #Prompt user for credentials
                #$Credentials = Get-Credential -UserName $username -Message "Enter credentials for: $CredentialName"
                
                if ($LocalOnly){
                    $Credentials = Get-Credential -UserName $username -Message "Enter credentials for: $CredentialName"
                    $Credentials | Export-Clixml $pathToCLIXMLCredential -Force
                    return $Credentials
                }

                if ($null -eq $global:AzureConnect){
                    #Connect to Azure
                    Write-Warning "USE <EMAIL> to authenticate"
                    Start-Sleep -Seconds 2
                    $global:AzureConnect = Connect-AzAccount
                }

                $VaultName = "PSAutomaionVault"
                $vaultSecrets = Get-AzKeyVaultSecret -VaultName $VaultName

                #Get Secret Value on specified Secret
                $Password = Get-AzKeyVaultSecret -VaultName $VaultName -Name $CredentialName -AsPlainText
                
                if ($Password){
                    $Password -match "([a-zA-Z1-9]+).(.+)" | Out-Null

                    $SecuredPassword = ConvertTo-SecureString -AsPlainText $Matches[2] -Force
                    $Credentials = New-Object -TypeName System.Management.Automation.PSCredential -ArgumentList $Matches[1], $SecuredPassword
                }
                else {
                    $Credentials = Get-Credential -UserName $username -Message "Enter credentials for: $CredentialName"
                }

                #Save credentials for next time
                $Credentials | Export-Clixml $pathToCLIXMLCredential -Force

                return $Credentials


            }
            #if session is non-interactive: Scan for credentials in clear text
            elseif (Test-Path $PathToTempClearTextPassword) {
                #password txt was created.
                    
                #check if it has been filled.
                $Content = Get-Content $PathToTempClearTextPassword
                $Username = $Content[0]
                $Password = $Content[1]

                if ($Username -eq "USERNAME_PLACEHOLDER" -or $Password -eq "PASSWORD_PLACEHOLDER") {
                    Write-Verbose "$(get-date) Please enter credentials into file ""$PathToTempClearTextPassword"""
                    Start-Sleep -Seconds 1
                    if ($DontWait) {
                        throw "Dont wait parameter set. Go fill info: $PathToTempClearTextPassword"
                    }
                    continue
                }
                if ($StopWatch.Elapsed.TotalSeconds -gt $TimeoutSeconds) {
                    throw "Timeout Reached of $TimeoutSeconds Seconds.."
                }
                    
                #Username and password has been filled

                if (!($password)) { throw "Empty password file at $PathToTempClearTextPassword" }
                $secstr = New-Object -TypeName System.Security.SecureString
                $password.ToCharArray() | ForEach-Object { $secstr.AppendChar($_) }
                $Credentials = new-object -typename System.Management.Automation.PSCredential -argumentlist $username, $secstr
                    
                #Save credentials for next time
                $Credentials | Export-Clixml $pathToCLIXMLCredential -Force
                    
                #Delete cleartext file after import.
                Remove-Item $PathToTempClearTextPassword -Force
                    
                #remove cleartext variables from memory
                Remove-Variable "password", "secstr" -Force -ErrorAction SilentlyContinue
                    
                return $Credentials
                    

            }
            else {
                #create clear text 
                if (!$username) { $username = "USERNAME_PLACEHOLDER" }
                $text = @(
                    $username
                    "PASSWORD_PLACEHOLDER"
                    "Info: Created from: $($env:computername) by: $($env:username) at: $(get-date)"
                )

                $text | Out-File -FilePath $PathToTempClearTextPassword

            }

        }
        
    } #>
    
    
}
