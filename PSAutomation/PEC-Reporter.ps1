$ErrorActionPreference = "Stop"

#region Functions
function Get-DeviceCodeAuthentication {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$Scope = "https://management.azure.com/.default offline_access"
    )
    
    $deviceCodeUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/devicecode"
    
    $body = @{
        client_id = $ClientId
        scope     = $Scope
    }
    
    try {
        $deviceCodeResponse = Invoke-RestMethod -Uri $deviceCodeUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        
        # Display the message to the user
        Write-Host $deviceCodeResponse.message -ForegroundColor Cyan
        
        # Poll for token
        $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
        $tokenBody = @{
            grant_type  = "device_code"
            device_code = $deviceCodeResponse.device_code
            client_id   = $ClientId
        }
        
        $interval = $deviceCodeResponse.interval
        $expiresOn = (Get-Date).AddSeconds($deviceCodeResponse.expires_in)
        
        while ((Get-Date) -lt $expiresOn) {
            try {
                Start-Sleep -Seconds $interval
                $tokenResponse = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $tokenBody -ContentType "application/x-www-form-urlencoded"
                return $tokenResponse
            }
            catch {
                # Check if this is an authorization_pending error, which is expected during polling
                $errorObj = $null
                try {
                    if ($_.ErrorDetails.Message) {
                        $errorObj = $_.ErrorDetails.Message | ConvertFrom-Json
                    }
                }
                catch {}
                
                # If it's authorization_pending, continue polling
                if ($errorObj -and $errorObj.error -eq "authorization_pending") {
                    Write-Host "Waiting for authentication..." -ForegroundColor Yellow
                    continue
                }
                
                # For any other error, throw
                Write-Error "Error during token polling: $_"
                throw
            }
        }
        
        throw "Authentication timed out"
    }
    catch {
        Write-Error "Error during device code authentication: $_"
        throw
    }
}

function Get-AzureCostAnalyticsSubscriptions {
    param (
        [string]$AccessToken,
        [string]$BillingAccountId,
        [datetime]$StartDate,
        [datetime]$EndDate
    )

    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Accept"        = "application/json"
    }

    $body = @"
    {
      "type": "ActualCost",
      "dataSet": {
        "granularity": "None",
        "aggregation": {
          "totalCost": {
            "name": "Cost",
            "function": "Sum"
          },
          "totalCostUSD": {
            "name": "CostUSD",
            "function": "Sum"
          }
        },
        "sorting": [
          {
            "direction": "ascending",
            "name": "UsageDate"
          }
        ],
        "grouping": [
          {
            "type": "Dimension",
            "name": "CustomerTenantId"
          },
          {
            "type": "Dimension",
            "name": "CustomerName"
          },
          {
            "type": "Dimension",
            "name": "SubscriptionId"
          },
          {
            "type": "Dimension",
            "name": "SubscriptionName"
          },
          {
            "type": "Dimension",
            "name": "PartnerEarnedCreditApplied"
          }
        ],
        "filter": {
          "Dimensions": {
            "Name": "Frequency",
            "Operator": "In",
            "Values": [
              "UsageBased"
                ]
              }
        }
      },
      "timeframe": "Custom",
      "timePeriod": {
        "from": "$($StartDate.ToString('yyyy-MM-ddTHH:mm:sszzz'))",
        "to": "$($EndDate.ToString('yyyy-MM-ddTHH:mm:sszzz'))"
      }
    }
"@

    $rows = @()
    $uri = "https://management.azure.com/providers/Microsoft.Billing/billingAccounts/$BillingAccountId/providers/Microsoft.CostManagement/query?api-version=2021-10-01&`$top=5000"
    $uri = "https://management.azure.com/providers/Microsoft.Billing/billingAccounts/$BillingAccountId/providers/Microsoft.CostManagement/query?api-version=2021-10-01"
    
    do {
        $maxRetries = 10
        $retryCount = 0
        $success = $false
        
        while (-not $success -and $retryCount -lt $maxRetries) {
            try {
                $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body -ContentType "application/json"
                $success = $true
            }
            catch {
                if ($_.Exception.Response.StatusCode -eq 429) {
                    $retryCount++
                    $sleepDuration = [Math]::Pow(2, $retryCount) # Exponential backoff: 2, 4, 8, 16, etc.
                    Write-Warning "Rate limit (429) encountered. Retry attempt $retryCount of $maxRetries. Waiting for $sleepDuration seconds..."
                    Start-Sleep -Seconds $sleepDuration
                }
                else {
                    # For non-rate limit errors, rethrow the exception
                    throw $_
                }
            }
        }
        
        if (-not $success) {
            throw "Failed to get response after $maxRetries retry attempts due to rate limiting."
        }
        
        $columns = $response.properties.columns
        $rows += $response.properties.rows
        $uri = $response.properties.nextLink
    } while ($uri)

    $table = $rows | ForEach-Object {
        $row = $_
        $properties = @{}
        for ($i = 0; $i -lt $columns.Count; $i++) {
            $value = $row[$i]
            if ($columns[$i].name -eq "PartnerEarnedCreditApplied") {
                $value = if ($value -eq 0) { "false" } elseif ($value -eq 1) { "true" } else { "unknown" }
            }
            $properties[$columns[$i].name] = $value
        }
        [PSCustomObject]$properties
    }

    return $table
}

function Get-RoleAssignments {
    param (
        [string]$AccessToken,
        [string]$SubscriptionId
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions/$SubscriptionId/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving role assignments: $_"
        throw
    }
}

function Get-AccessTokenForTargetTenant {
    param (
        [string]$TargetTenantId,
        [string]$ClientId,
        [string]$RefreshToken,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TargetTenantId/oauth2/v2.0/token"
    
    $body = @{
        client_id     = $ClientId
        scope         = $Scope
        refresh_token = $RefreshToken
        grant_type    = "refresh_token"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token for target tenant: $_"
        throw $_
    }
}

function Get-AccessTokenAppRegistration {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$ClientSecret,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
    
    $body = @{
        grant_type    = "client_credentials"
        client_id     = $ClientId
        client_secret = $ClientSecret
        scope         = $Scope
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token using app registration: $_"
        throw $_
    }
}

function Get-CosmosDbCFDocuments {
    param (
        $cosmosDbContext,
        $CollectionId = 'BilledUsage',
        $documentsPerRequest = 20,
        $query = "",
        $MaxItemCount = 999

    )
    $ErrorActionPreference = 'Stop'

    $continuationToken = $null
    $documents = @()

    do {
        try {
            Write-Progress -Activity "Getting CosmosDB Documents $($cosmosDbContext.account)" -status "Documents: $($documents.count) / $MaxItemCount" -PercentComplete ($documents.count / $MaxItemCount * 100)
        }
        catch {
            
        }

        $responseHeader = $null
        $getCosmosDbDocumentParameters = @{
            Context        = $cosmosDbContext
            CollectionId   = $CollectionId
            MaxItemCount   = $documentsPerRequest
            ResponseHeader = ([ref] $responseHeader)
            query          = $query
        }

        if ($continuationToken) {

            $getCosmosDbDocumentParameters += @{
                ContinuationToken = $continuationToken
            }
        }
        $documents += Get-CosmosDbDocument @getCosmosDbDocumentParameters -QueryEnableCrossPartition:$true
        $continuationToken = [System.String] $responseHeader.'x-ms-continuation'
        #Write-Verbose $continuationToken 
    } while (-not [System.String]::IsNullOrEmpty($continuationToken) -and $documents.count -lt $MaxItemCount)
    return $documents
}

#endregion Functions



#region Static data
$roles = @(
    @{
        GUID = "8e3af657-a8ff-443c-a75c-2fe8c4bcb635"
        Name = "Owner"
    },
    @{
        GUID = "b24988ac-6180-42a0-ab88-20f7382dd24c"
        Name = "Contributor"
    },
    @{
        GUID = "cfd33db0-3dd1-45e3-aa9d-cdbdf3b6f24e"
        Name = "Support Request Contributor"
    }
)
$adminAgentGroups = @(
    @{
        GUID                  = "4e515f36-d3ab-46bb-898e-************"
        Prettyname            = "Cloud Factory Denmark"
        CredentialID          = "DK"
        OnePasswordObjectName = "PEC-Reporter-DK"
    },
    @{
        GUID                  = "877fb66d-052b-48cc-92ac-c6901d5066c5"
        Prettyname            = "Cloud Factory Norway"
        CredentialID          = "NO"
        OnePasswordObjectName = "PEC-Reporter-NO"
    },
    @{
        GUID                  = "224e3ca0-5816-4f6f-af4b-1479894e2f18"
        Prettyname            = "Cloud Factory Sweden"
        CredentialID          = "SE"
        OnePasswordObjectName = "PEC-Reporter-SE"
    },
    @{
        GUID                  = "a14267f9-f4c3-4789-81fd-a019aa02e74b"
        Prettyname            = "Cloud Factory BeNeLux"
        CredentialID          = "BE"
        OnePasswordObjectName = "PEC-Reporter-NL"
    }
    
)

#authenticate CosmosDB
$OPData = op item get "cf-prod-db1" --vault "PSAutomation" --reveal --format json  | ConvertFrom-Json 
$primaryKey = $OPData.fields | Where-Object id -eq "password" | Select-Object -ExpandProperty value |  ConvertTo-SecureString -AsPlainText -Force 
$CosmosDbContext = New-CosmosDbContext -Account 'cf-prod-db1' -Database 'Microsoft' -Key $primaryKey

#region Get last billed month
#get the last billed month from cosmos db
# SELECT * FROM c
#check 
$LastBilledMonth = Get-Date
$maxLoopCount = 10
$loopCounter = 0

while($true) {
    $document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId "BilledUsage" -query "SELECT * FROM c WHERE c.UsageDate like '$($LastBilledMonth.ToString("yyyy-MM"))%'" -MaxItemCount 1 -documentsPerRequest 1
    
    if($document) {
        break
    }
    
    $loopCounter++
    if($loopCounter -ge $maxLoopCount) {
        throw "Maximum loop count ($maxLoopCount) reached while searching for billed usage data"
    }
    
    $LastBilledMonth = $LastBilledMonth.AddMonths(-1)
}
$LastBilledMonthString = "$($LastBilledMonth.ToString("yyyy-MM"))%"
#endregion Get last billed month

#endregion Static data
<# Test
$adminAgentGroup = $adminAgentGroups[0]
#>

#Get all data from last billed period
$documents=Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId "BilledUsage" -query "SELECT * FROM c WHERE c.UsageDate like '$($LastBilledMonthString)'" -MaxItemCount 1000000000 -documentsPerRequest 10000



#Get unbilled data from newest date
$loopCounter = 0
$maxLoopCount = 10
$UnbilledDate=get-date -Hour 0 -Minute 0 -Second 0
while($true) {
    $loopCounter++
    if($loopCounter -ge $maxLoopCount) {
        break
    }
    $UnbilledDateString=$UnbilledDate.ToString("yyyy-MM-dd")
    $query = "SELECT * FROM c WHERE c.EntitlementId = '$($Subscription.SubscriptionId)' AND c.UsageDate = '$($UnbilledDateString)' "
    $CollectionID = "UnbilledUsage"
    $Document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 1 -documentsPerRequest 1
    if ($Document) {
        break
    }
    
    $UnbilledDate=$UnbilledDate.AddDays(-1)
}

#Now check unbilled date if PEC applied:
$query = "SELECT * FROM c WHERE c.EntitlementId = '$($Subscription.SubscriptionId)' AND c.UsageDate = '$($UnbilledDateString)' AND c.PartnerEarnedCreditPercentage > 0"
$CollectionID = "UnbilledUsage"
$Document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 1 -documentsPerRequest 1
if ($Document) {
    $SubscriptionPECEnabledOnUnbilledData = $true
}   else {
    $SubscriptionPECEnabledOnUnbilledData = $false
}








foreach ($adminAgentGroup in $adminAgentGroups) {
    

    #region authentication
    <#
    #connect using devicecode. Using MFA this grants alot more access
    $clientId = "1950a258-227b-4e31-a9cf-717495945fc2"  # PowerShell's built-in client ID
    $tenantId = "common"  # Use 'common' for multi-tenant authentication or specify your tenant ID
    $MasterTokenResponse = Get-DeviceCodeAuthentication -TenantId $tenantId -ClientId $clientId
    #>

    #connect using app registration
    
    $1password = op item get $adminAgentGroup.OnePasswordObjectName --vault "PSAutomation" --reveal --format json  | ConvertFrom-Json 
    $appregparams = @{
        ClientId         = $1password.fields | Where-Object label -eq "ApplicationID" | Select-Object -ExpandProperty value
        ClientSecret     = $1password.fields | Where-Object id -eq "password" | Select-Object -ExpandProperty value
        TenantId         = $1password.fields | Where-Object label -eq "TenantID" | Select-Object -ExpandProperty value
        BillingAccountId = $1password.fields | Where-Object label -eq "BillingAccountID" | Select-Object -ExpandProperty value
    }

    #check if all properties of appregparams have values. Do it dynamically so we can add more properties later
    $Continue = $false
    foreach ($key in $appregparams.Keys) {
        if (-not $appregparams[$key]) {
            write-warning "Missing $key in 1password object for $($adminAgentGroup.OnePasswordObjectName)"
            $Continue = $true
        }
    }
    if ($Continue) {
        continue
    }

    #connect with app registration
    $MasterTokenResponse = Get-AccessTokenAppRegistration @appregparams

    #endregion authentication




    $costAnalyticsParams = @{
        AccessToken      = $MasterTokenResponse.access_token
        BillingAccountId = $appregparams.BillingAccountId
        StartDate        = (Get-Date).adddays(-1)
        EndDate          = Get-Date
    }

    $Subscriptions = Get-AzureCostAnalyticsSubscriptions @costAnalyticsParams 

    <#
    $Subscription = $Subscriptions | Where-Object { $_.Subscriptionid -eq "6e3f2b83-ed08-4aa5-8ec7-e9bae47f0d02" }
    #>
    $i = 0
    foreach ($subscriptionGroup in $Subscriptions | Group-Object SubscriptionId) {
        try {
            Write-Progress -Activity "Processing subscriptions" -Status "Processing subscription $($subscriptionGroup.Name)" -PercentComplete ($i / $Subscriptions.Count * 100)
        }
        catch { }
        #First. Lets Check if the subscription has PEC.
        #All subscriptions that have PEC enabled, we ignore.
        #in case there is multiple entries on the same subscriptions, we check if at least on of them has PEC. If yes we ignore.
        if ($subscriptionGroup.Group | Where-Object { $_.PartnerEarnedCreditApplied -eq $true }) {
            #at least one of the entries has PEC enabled. We ignore
            Write-Verbose "PEC enabled. Skipping. - Subscription $($subscriptionGroup.group[0].customername) - $($subscriptionGroup.group[0].subscriptionname)"
            continue
        }
        else {
            #all entries has PEC disabled. We continue
            $Subscription = $subscriptionGroup.Group | Where-Object { $_.PartnerEarnedCreditApplied -eq $false }
            $SubscriptionCount = ($Subscription | Measure-Object | Select-Object -ExpandProperty count)
            if ($SubscriptionCount -ne 1) {
                throw "$SubscriptionCount entries for subscription $($subscriptionGroup.Name). We expect 1 entry."
            }
        }

        #Lets check if the cost is more than 0. we dont want to spend time on empty subscriptions
        if ($Subscription.Cost -le 0) {
            Write-Verbose "Subscription $($Subscription.SubscriptionName) has no cost. Skipping."
            continue
        }


   
    
        #This is a list of all subs without PEC with usage on them

        #lets try and figure out why that is.

        #region Role Assigment

        #first lets try and get the role assignments for the subscription
        #Create access token in context of the customer
        <#
        try { 
            $TenantTokenResponse = Get-AccessTokenForTargetTenant -TargetTenantId $Subscription.CustomerTenantId -ClientId $clientId -RefreshToken $MasterTokenResponse.refresh_token

            $roleAssignments = Get-RoleAssignments -AccessToken $tenantTokenResponse.access_token -SubscriptionId $subscriptionGroup.Name
    
            #check if any of the role assignments are for the admin agent groups
            $CloudFactoryRoleAssignments = $roleAssignments | Where-Object { $_.properties.principalId -in $adminAgentGroups.GUID -and ($_.properties.roleDefinitionId -replace "^.+/") -in $roles.GUID }

            #resolve the admin agent CredentialID  and role name
            $ParsedRoleAssignments = foreach ($CloudFactoryRoleAssignment in $CloudFactoryRoleAssignments) {
                $roleGUID = ($CloudFactoryRoleAssignment.properties.roleDefinitionId -replace "^.+/")
                $RoleName = $roles | Where-Object { $_.GUID -eq $roleGUID } | Select-Object -ExpandProperty Name
                $AdminAgentGroup = $adminAgentGroups | Where-Object { $_.GUID -eq $CloudFactoryRoleAssignment.properties.principalId } | Select-Object -ExpandProperty CredentialID

                #Write-Host "Cloud Factory has $RoleName on subscription $($Subscription.SubscriptionName) for $($Subscription.CustomerName) in $($Subscription.CustomerTenantId)" -ForegroundColor Green
            }
        }
        catch {
            $RoleAssigmentError = $_
        
        }
        #>
        try 
        {
            
            $SubscriptionPECEnabledOnLastInvoice = $false
            $SubscriptionBilledLastMonth = $false
            $invoiceID=$null
            #Check CosmoDB if Subscribtion has PEC enabled
            #where where c.ResourceURI like "/subscriptions/6e3f2b83-ed08-4aa5-8ec7-e9bae47f0d02%"
            $query = "SELECT * FROM c WHERE c.EntitlementId = '$($Subscription.SubscriptionId)' AND c.UsageDate like '$($LastBilledMonthString)'"
            $CollectionID = "BilledUsage"
            $Document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 1 -documentsPerRequest 1
            if ($Document) {
                $SubscriptionBilledLastMonth = $true
                $invoiceID=$Document.InvoiceNumber
            }
    
            if ($SubscriptionBilledLastMonth) {
                #We validated that subscription was billed last month. now lest check if it has PEC enabled
                $query = "SELECT * FROM c WHERE c.EntitlementId = '$($Subscription.SubscriptionId)' AND c.PartnerEarnedCreditPercentage > 0 AND c.ChargeEndDate like '$($LastBilledMonthString)'"
                $CollectionID = "BilledUsage"
                $Document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 1 -documentsPerRequest 1
                if ($Document) {
                    $SubscriptionPECEnabledOnLastInvoice = $true
                }
            }
            $BilledLastMonthCheckSuccess=$true

        }
        catch {
            write-warning "Failed to check if subscription $($Subscription.SubscriptionName) was billed last month."
            write-warning ($_|Out-String)
            $BilledLastMonthCheckSuccess=$false
        }

        #Check unbilled data on cosmos. loop through each day until there is data. max 10 days.
        $loopCounter = 0
        $maxLoopCount = 10
        $UnbilledDate=get-date -Hour 0 -Minute 0 -Second 0
        while($true) {
            $loopCounter++
            if($loopCounter -ge $maxLoopCount) {
                break
            }
            $UnbilledDateString=$UnbilledDate.ToString("yyyy-MM-dd")
            $query = "SELECT * FROM c WHERE c.EntitlementId = '$($Subscription.SubscriptionId)' AND c.UsageDate = '$($UnbilledDateString)' "
            $CollectionID = "UnbilledUsage"
            $Document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 1 -documentsPerRequest 1
            if ($Document) {
                break
            }
            
            $UnbilledDate=$UnbilledDate.AddDays(-1)
        }

        #Now check unbilled date if PEC applied:
        $query = "SELECT * FROM c WHERE c.EntitlementId = '$($Subscription.SubscriptionId)' AND c.UsageDate = '$($UnbilledDateString)' AND c.PartnerEarnedCreditPercentage > 0"
        $CollectionID = "UnbilledUsage"
        $Document = Get-CosmosDbCFDocuments -cosmosDbContext $cosmosDbContext -CollectionId $CollectionID -query  $query -MaxItemCount 1 -documentsPerRequest 1
        if ($Document) {
            $SubscriptionPECEnabledOnUnbilledData = $true
        }   else {
            $SubscriptionPECEnabledOnUnbilledData = $false
        }


        $subscription.CostUSD = [math]::Round($subscription.CostUSD, 0)
        $subscription.Cost = [math]::Round($subscription.Cost, 0)
        $Subscription | Add-Member -MemberType NoteProperty -Name "Country" -Value $adminAgentGroup.CredentialID
        $Subscription | Add-Member -MemberType NoteProperty -Name "SubscriptionPECEnabledOnLastInvoice" -Value $SubscriptionPECEnabledOnLastInvoice
        $Subscription | Add-Member -MemberType NoteProperty -Name "SubscriptionBilledLastMonth" -Value $SubscriptionBilledLastMonth
        $Subscription | Add-Member -MemberType NoteProperty -Name "BilledLastMonthCheckSuccess" -Value $BilledLastMonthCheckSuccess
        $Subscription | Add-Member -MemberType NoteProperty -Name "InvoiceID" -Value $invoiceID
        $Subscription | Add-Member -MemberType NoteProperty -Name "SubscriptionPECEnabledOnUnbilledData" -Value $SubscriptionPECEnabledOnUnbilledData
        $Subscription | Add-Member -MemberType NoteProperty -Name "NewestUnbilledDate" -Value $UnbilledDateString
        $SubscriptionsWithoutPEC += $Subscription
        write-host ($Subscription | ConvertTo-Json -Depth 100)
        

    
        #endregion Role Assigment
    }
}

#Report to slack

#flatten the array
#
$SubscriptionsWithoutPEC = $SubscriptionsWithoutPEC | Sort-Object -Descending CostUSD
$SubscriptionsWithoutPEC = $SubscriptionsWithoutPEC  | Where-Object { $_.CostUSD -gt 0 } 
$Message = "The following subscriptions do not have PEC enabled. See attached file for full list`n`n"
$Message += $SubscriptionsWithoutPEC | Where-Object { $_.CostUSD -gt 5 } | Select-Object Country, CostUSD, CustomerName, SubscriptionName, SubscriptionPECEnabledOnLastInvoice | Format-Table | Out-String

$TempFolder = $env:temp   
$filename = "SubscriptionsWithoutPEC_$(Get-Date -Format 'yyyy-MM-dd_HH-mm-ss').csv"
$SubscriptionsWithoutPEC | export-csv -Path "$TempFolder\$filename" -NoTypeInformation -encoding unicode -Delimiter ";"
$SlackCreds = Get-SafeCredentials Slacktoken
$Slacktoken = Decrypt-SecureString $SlackCreds.Password
Send-SlackFile -Token $Slacktoken -Channel "#microsoft-pec-reporter" -Comment $Message  -Path "$TempFolder\$filename" 