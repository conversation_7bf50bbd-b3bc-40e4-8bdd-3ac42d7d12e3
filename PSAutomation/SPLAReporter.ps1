$ErrorActionPreference = "Stop"
#region functions
function Wait-DropboxIdle {
    param (
        [int]$IdleThresholdSeconds = 60,
        [int]$PollIntervalSeconds = 1
    )

    $filesToWatch = @(
        "\\dk01ts03\Dropbox\instance1\sync_history.db",
        "\\dk01ts03\Dropbox\instance1\config.dbx",
        "\\dk01ts03\Dropbox\instance1\sync\nucleus.sqlite3-wal"
    )

    $filesToWatch = $filesToWatch | Where-Object { Test-Path $_ }

    if (-not $filesToWatch) {
        Write-Error "None of the specified Dropbox files exist on \\dk01ts03"
        return
    }

    Write-Host "Waiting for Dropbox to be idle for $IdleThresholdSeconds seconds..." -ForegroundColor Yellow

    while ($true) {
        $latestWrite = $null

        foreach ($file in $filesToWatch) {
            $writeTime = (Get-Item $file).LastWriteTime
            if (-not $latestWrite -or $writeTime -gt $latestWrite) {
                $latestWrite = $writeTime
            }
        }

        if ($latestWrite) {
            $idleSeconds = [int](New-TimeSpan -Start $latestWrite -End (Get-Date)).TotalSeconds
            Write-Host ("Current idle time: {0} seconds" -f $idleSeconds) -NoNewline
            if ($idleSeconds -ge $IdleThresholdSeconds) {
                Write-Host "`nDropbox has been idle for $IdleThresholdSeconds seconds. Continuing..." -ForegroundColor Green
                return
            } else {
                Write-Host " ...waiting"
            }
        } else {
            Write-Host "Could not determine latest write time." -ForegroundColor Red
            return
        }

        Start-Sleep -Seconds $PollIntervalSeconds
    }
}
function Get-SafeFolderName {
    param (
        [string]$Name
    )

    # Define invalid filename characters
    $invalidChars = [System.IO.Path]::GetInvalidFileNameChars() -join ''
    $escapedInvalid = [regex]::Escape($invalidChars)

    # Replace all invalid characters with underscore
    $safe = $Name -replace "[$escapedInvalid]", "_"

    # Trim trailing spaces or dots
    $safe = $safe.TrimEnd('.', ' ')

    # Avoid reserved names
    $reservedNames = @('CON','PRN','AUX','NUL','COM1','COM2','COM3','COM4','COM5','COM6','COM7','COM8','COM9',
                       'LPT1','LPT2','LPT3','LPT4','LPT5','LPT6','LPT7','LPT8','LPT9')

    if ($reservedNames -contains $safe.ToUpper()) {
        $safe = "${safe}_folder"
    }

    return $safe
}
#endregion functions

 
#$nutanixvms = Get-NutanixVMs
$NBVMs = Get-NetboxVMs 
$NBVMs = $NBVMs | Where-Object { $_.status.value -eq "Active" } 
$NBVMs = $NBVMs | Where-Object { $_.custom_fields.WindowsInstalled }


$NBTenants = Get-NetboxTenants
$NBTenants = $NBTenants | Where-Object { $_.id -in $NBVMs.tenant.id } #only tenants with vms


$NBTenantGroups = Get-NetboxTenantGroups
$NBTenantGroups = $NBTenantGroups | Where-Object { $_.id -in $NBTenants.group.Id } #only tenant groups with tenants
<#
#create Temp folder

$TempFolder = Join-Path -Path $env:TEMP -ChildPath "4.0 Inventory Tool (PS)"
if (-not (Test-Path -Path $TempFolder -PathType Container)) {
    New-Item -Path $TempFolder -ItemType Directory
}
else {
    #delete old folder and recreate
    Remove-Item -Path $TempFolder -Recurse -Force
    New-Item -Path $TempFolder -ItemType Directory
}
#download content from dropbox
$dropboxurl = "https://www.dropbox.com/scl/fo/jo3e98jonpxmp43945nv9/APaNN9TljV6EiMUHqwLa6S4?rlkey=615ksbqqbi9ff01hg5ht1k5qq&dl=1"
$dropboxfile = Join-Path -Path $TempFolder -ChildPath "scanresult.zip"
Invoke-WebRequest -Uri $dropboxurl -OutFile $dropboxfile
#unzip content
Expand-Archive -Path $dropboxfile -DestinationPath $TempFolder
#>
$TempFolder="\\DK01TS03\Cloud Factory SPLA Audit - Upload"
#Get all zip files and expand those as well
$MaxNestedZIPFiles = 10
$i = 0
$ZipFiles = Get-ChildItem -Recurse -Filter "*.zip"  -Path $TempFolder | Where-Object { $_.Extension -eq ".zip" }
while ($ZipFiles) {
    if ($i -ge $MaxNestedZIPFiles) {
        throw "Max nested zip files count reached ($MaxNestedZIPFiles)"
    }
    $i++
    foreach ($ZipFile in $ZipFiles) {
        Expand-Archive -Path $ZipFile.FullName -DestinationPath ("$($ZipFile.FullName)_Expanded") -Force
        Remove-Item -Path $ZipFile.FullName -Force
    }
    $ZipFiles = Get-ChildItem -Recurse -Filter "*.zip" -Path $TempFolder
}
write-host "Waiting for Dropbox to be idle for 120 seconds..." -ForegroundColor Yellow
Start-Sleep -Seconds 120 # give dropbox time to start syncing. In case the script continues faster than dropbox registers a change.

Wait-DropboxIdle -IdleThresholdSeconds 120

#$ScanResultFiles = Get-ChildItem -Recurse scanresult-*.csv -Path $TempFolder
$TenantGroupFoldername="TenantGroups"
$ScanResultFiles = Get-ChildItem -Recurse scanresult-*.csv -Path $TempFolder -Exclude $TenantGroupFoldername

$FailedScans = @()
$NonNutanixVMs = @()

$i = 0
#parse csv report files.
$ScannedVMs = foreach ($ScanResultFile in $ScanResultFiles) {
    $i++
    try {
        Write-Progress -Activity "Checking scan results" -Status "$i of $($ScanResultFiles.Count) completed" -PercentComplete ($i / $ScanResultFiles.Count * 100)
    }
    catch {}
    $ScanResult = Get-Content -LiteralPath $ScanResultFile.FullName

    $Computername = $ScanResult | select-string "\[HARDWARE-CONTENT\];(.+?);" | Select-Object -ExpandProperty Matches | Select-Object -ExpandProperty Groups | Select-Object -Last 1 -ExpandProperty value
    
    $ComputerManufacturer = $ScanResult | select-string "\[HARDWARE-CONTENT\];(.+?;){10}(.+?);" | Select-Object -ExpandProperty Matches | Select-Object -ExpandProperty Groups | ? name -eq 2 | select -ExpandProperty value
    
    $NutanixModelNames = @(
        "Red Hat"
        "Nutanix"
    )
    if ($ComputerManufacturer -notin $NutanixModelNames) {
        Write-Warning "Computer $Computername is not a Nutanix VM. Manufacturer: $ComputerManufacturer"
        $NonNutanixVMs += "Computer $Computername is not a Nutanix VM. Manufacturer: $ComputerManufacturer - File: $($ScanResultFile.FullName)"
        continue
    }
    $SerialnumberLine = $ScanResult | select-string "OS-CONTENT"
    if ($SerialnumberLine -match "([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})") {
        $Serialnumber = $matches[1]
        $NBVM = $NBVMs | Where-Object { $_.custom_fields.VM_ID -eq $Serialnumber }
        #$NutanixVM = $nutanixvms | Where-Object { $_.metadata.UUID -eq $Serialnumber }
        
        if ($null -eq $NBVM) {
            
        }
        if ($NBVM) {
            $TenantGroup = $NBTenants | Where-Object { $_.id -eq $NBVM.tenant.id } | Select-Object -ExpandProperty group
        }
        else {
            $TenantGroup = ""
            Write-Warning "Serial number $Serialnumber not found in Nutanix inventory. Computername: $Computername"
            $FailedScans += "Serial number $Serialnumber not found in Nutanix inventory. Computername: $Computername - File:  $($ScanResultFile.FullName)"
        }
        
        #Get the parent folder.
        #examples follows. I have marked the folders with () that must be captured
        #'$TempFolder\(nyit-Unifi)\DIT Results - 20250320090003\NYIT-UNIFI\scanResult-NYIT-UNIFI.csv'
        #'$TempFolder\(nyit-Unifi)\NYIT-UNIFI2\scanResult-NYIT-UNIFI.csv'
        
        $tempFolderRegex = [regex]::Escape($TempFolder)
        if($ScanResultFile.FullName  -match "($tempFolderRegex\\.+?)\\"){
            $parentfolder=$matches[1]
        }else{
            throw "Failed to extract parent folder from $($ScanResultFile.FullName)"
        }

        [PSCustomObject]@{
            Computername  = $Computername
            UUID          = $Serialnumber
            #NutanixVM     = $NutanixVM
            NetboxVM      = $NBVM
            NBTenantGroup = $TenantGroup
            ScanResultFile = $ScanResultFile.FullName
            ParentFolder = $parentfolder

        }
    }
    else {
        Write-Warning "Serial number not found in $ScanResultFile"
        $FailedScans += "Serial number not found in $($ScanResultFile.FullName)"
    }



}

#Move all folders with a known tenantgrup.
#Create a folder $TempFolder\TenantGroups\<TenantGroup>
$parentfolders=$ScannedVMs | Group-Object -Property ParentFolder
#exclude $tenantGroupFoldername
$parentfolders = $parentfolders | Where-Object { $_.Name -notlike "*\$TenantGroupFoldername*" }

foreach ($parentfolder in $parentfolders) {
    #verify only 1 tenantgroup in parentfolder
    $tenantgroups = $parentfolder.Group | Select-Object -ExpandProperty NBTenantGroup | ? name | Select-Object -ExpandProperty name -Unique
    if ($tenantgroups.Count -gt 1) {
        throw "Multiple tenant groups found in $($parentfolder.name) `n$($tenantgroups.name)"
    }elseif ($tenantgroups.Count -eq 0) {
        Write-Warning "No tenant group found in $($parentfolder.name)"
        continue
    }
    $tenantgroupName = $tenantgroups
    $sourcefolder = $parentfolder.name  

    #Dont touch folders that have been created within 1 hour
    $createdTime = (Get-Item -Path $sourcefolder).CreationTime
    if ($createdTime -gt (Get-Date).AddHours(-1)) {
        Write-Warning "Folder $sourcefolder was created within the last hour. Skipping."
        continue
    }


    $destinationfolderRoot = "$TempFolder\TenantGroups"
    $destinationfolderSafeChild = Get-SafeFolderName -Name  $tenantgroupName 

    $destinationfolder = Join-Path -Path $destinationfolderRoot -ChildPath $destinationfolderSafeChild
    if (-not (Test-Path -Path $destinationfolder -PathType Container)) {
        New-Item -Path $destinationfolder -ItemType Directory
        wait-dropboxidle -IdleThresholdSeconds 10
    }
    Write-Warning "Moving folder $sourcefolder to $destinationfolder"
    Move-Item -Path $sourcefolder -Destination $destinationfolder -Force
}

#Check if all VMs have been scanned.
$i = 0
$PartnerOverview = foreach ($NBTenantGroup in $NBTenantGroups) {
    $i++
    try {
        Write-Progress -Activity "Checking if all VMs have been scanned" -Status "$i of $($NBTenantGroups.Count) completed" -PercentComplete($i / $NBTenantGroups.Count * 100)
    }
    catch {
        
    }
    $NBTenantGroupTenants = $NBTenants | Where-Object { $_.group.id -eq $NBTenantGroup.id }
    $NBTenantGroupVms = $NBVMs | Where-Object { $_.tenant.id -in $NBTenantGroupTenants.id }
    $NBTenantGroupScannedVMs = $NBTenantGroupVms | Where-Object { $_.custom_fields.VM_ID -in $ScannedVMs.UUID }
    #$NBTenantGroupScannedVMs=$ScannedVMs | Where-Object { $_.uuid -in $NBTenantGroupVms.custom_fields.VM_ID }
    
    if ($NBTenantGroupVms) {
        #Tenant group has vms.
        try {
            $PercentVMsScanned = [math]::Round($NBTenantGroupScannedVMs.count / $NBTenantGroupVms.count * 100,1)
        }
        catch {
            $PercentVMsScanned = 0
        }
        [PSCustomObject]@{
            TenantGroup     = $NBTenantGroup.name
            TotalVMsCount   = $NBTenantGroupVms.count
            ScannedVMsCount = $NBTenantGroupScannedVMs.count
            MissingVMsCount = $NBTenantGroupVms.count - $NBTenantGroupScannedVMs.count
            PercentScanned  = $PercentVMsScanned
            MissingVMNames  = ($NBTenantGroupVms | Where-Object { $_.custom_fields.VM_ID -notin $NBTenantGroupScannedVMs.custom_fields.VM_ID }).name -join ","
        }
    }
}
$TotalScannedVMs = ($PartnerOverview.ScannedVMsCount | Measure-Object -sum).sum
$TotalVMs = ($PartnerOverview.TotalVMsCount | Measure-Object -sum).sum
$TotalScanProgress = [math]::Round($TotalScannedVMs / $TotalVMs * 100, 1)
$PartnersCompletedCount = ($PartnerOverview | Where-Object { $_.PercentScanned -eq 100 }).count


#verbose Report
$attempt = 0
$maxAttempts = 10
$success = $false
$errorMessage = ""
$filename="PartnerOverview"
while (-not $success -and $attempt -lt $maxAttempts) {
    $attempt++
    try {
        $PartnerOverview | Sort-Object PercentScanned -Descending | Export-Csv -Path "$TempFolder\$filename.csv" -Delimiter "," -NoTypeInformation
        $success = $true
    }
    catch {
        $errorMessage = $_.Exception.Message
        Write-Warning "Failed to export PartnerOverview.csv. Attempt $attempt of $maxAttempts. Error: $errorMessage"
        $filename="PartnerOverview_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Start-Sleep -Seconds 2
    }
}

if (-not $success) {
    Write-Error "Failed to export PartnerOverview.csv after $maxAttempts attempts"
    throw "Failed to export PartnerOverview.csv. Error: $errorMessage"
}

#summary
$FailedScans = $FailedScans -replace ".:\.+?4.0 Inventory Tool \(PS\)"

#summary
$FailedScans = $FailedScans -replace ".:\\.+?4.0 Inventory Tool \(PS\)"
$Message = "Scan progress: $TotalScanProgress%"
$Message += "`nPartners completed: $PartnersCompletedCount"
$Message += "`nTotal VMs scanned: $TotalScannedVMs"
$Message += "`nTotal VMs: $TotalVMs"
$Message += "`n`Non-Nutanix VMs: $($NonNutanixVMs.count)"
$Message += "`n`nFailed scans:`n"
$Message += $FailedScans | Out-String

$SlackCreds = Get-SafeCredentials Slacktoken
$Slacktoken = Decrypt-SecureString $SlackCreds.Password
Send-SlackFile -Token $Slacktoken -Channel "#spla-review-progress"  -Path "$TempFolder\$filename.csv" -Comment $Message