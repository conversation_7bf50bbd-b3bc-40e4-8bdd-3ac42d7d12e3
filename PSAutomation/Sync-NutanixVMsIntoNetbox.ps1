﻿try {
    $ErrorActionPreference = "Stop"
    $VerbosePreference = "Continue"

    Write-Output "`nRunning on: $env:COMPUTERNAME"
   
    #Credentials
    $NetboxCredentials = Get-SafeCredentials -CredentialName "NetboxToken"
    $NutanixCredentials = Get-SafeCredentials -CredentialName "PrismCentral"

    #Authentication
    $NetboxParams = Get-NetboxHeader -Credentials $NetboxCredentials
    $NutanixParams = Get-NutanixHeader -Credentials $NutanixCredentials

    #Ignore list for specified VMs.
    $IgnoreVMUUID = @(                                     
        "937b81a0-3aa2-4b61-9942-023fea9c9938" # PC-Node-1                                   
        "dc2672e8-07a2-473d-815e-989421b861d4" # PC-Node-2                                                        
        "e2251fad-c498-4f93-9199-7b41ec87eb4e" # PC-Node-3               
        "bf73e4e4-23ae-4d32-a925-f2260a695c59" # PC-Node-1    
        "0e2caa44-51b8-4aea-aef7-6acaf367b737" # PC-Node-2  
        "d794e44e-16fa-4231-8c02-407214f32bfe" # PC-Node-3                     
        "6937d226-edd9-4196-bac8-4f2d0d51fca9" # PC-Node-1    
        "2ce971c2-95b3-48c2-b1c0-76c0760da7cd" # PC-Node-2  
        "faab67e3-cfd8-416d-aca8-ff326cfc0753" # PC-Node-3                               
    )

    #Getting data from Prism Central
    Write-Output "Getting all necessary data from Prism Central"
    $NutanixVMs = Invoke-CFNutanixCommand -server "ntx.cloudfactory.dk:9440" -method POST -command "vms/list" -Credentials $NutanixCredentials
    $NutanixSubnets = Invoke-CFNutanixCommand -server "ntx.cloudfactory.dk:9440" -method POST -command "subnets/list" -Credentials $NutanixCredentials
    $NutanixHosts = Invoke-CFNutanixCommand -server "ntx.cloudfactory.dk:9440" -method POST -command "hosts/list" -Credentials $NutanixCredentials
    $NutanixClusters = Invoke-CFNutanixCommand -server "ntx.cloudfactory.dk:9440" -method POST -command "clusters/list" -Credentials $NutanixCredentials
    $NutanixImages = Invoke-CFNutanixCommand -server "ntx.cloudfactory.dk:9440" -method POST -command "images/list" -Credentials $NutanixCredentials
    
    #Getting data from Netbox
    Write-Output "Getting all necessary data from Netbox"
    $NetboxClusters = Invoke-NBAPI -baseuri $NetboxParams.baseuri -Command "virtualization/clusters/" -header $NetboxParams.header
    $NutanixNetboxClusters = $NetboxClusters | ? { $_.group.id -eq 1 } #Nutanix
    $String = ""
    $NutanixNetboxClusters | % { $String += "cluster_id=$($_.id)&" }
    
    $NetboxVMs = Invoke-NBAPI -baseuri $NetboxParams.baseuri -Command "virtualization/virtual-machines/" -header $NetboxParams.header -Inputs $String
    
    $NetboxTenants = Invoke-NBAPI -baseuri $NetboxParams.baseuri -Command "tenancy/tenants/" -header $NetboxParams.header
    $NetboxVlans = Invoke-NBAPI -baseuri $NetboxParams.baseuri -Command "ipam/vlans/" -header $NetboxParams.header
    $NetboxInterfaces = Invoke-NBAPI -baseuri $NetboxParams.baseuri -Command "virtualization/interfaces/" -header $NetboxParams.header

    #Comparing NutanixVMs with NetboxVMs
    $compareguids = Compare-Object $NutanixVMs.metadata.UUID $NetboxVMs.custom_fields.VM_ID -IncludeEqual | Group-Object -Property SideIndicator
    $NewVMGUIDs = $compareguids | ? { $_.Name -eq "<=" } | Select -ExpandProperty Group | Select -ExpandProperty InputObject
    $ExistingVMGUIDs = $compareguids | ? { $_.Name -eq "==" } | Select -ExpandProperty Group | Select -ExpandProperty InputObject
    $DeletedVMGUIDs = $compareguids | ? { $_.Name -eq "=>" } | Select -ExpandProperty Group | Select -ExpandProperty InputObject
    
    #Updating Deleted VMs from Nutanix to Netbox
    Write-Output "`nUpdating Deleted VMs from Nutanix to Netbox.."
    foreach ($DeletedVMGUID in $DeletedVMGUIDs) {
        try {
            $NetboxVM = $NetboxVMs | ? { $_.custom_fields.VM_ID -eq $DeletedVMGUID }
            $LastUpdateOfVM = Get-Date $NetboxVM.custom_fields.Last_Update
        
            $Interfaces = $NetboxInterfaces | ? { $_.virtual_machine.id -eq $NetboxVM.id }
            $NetboxVM.vcpus = [int]$NetboxVM.vcpus
        
            if ($NetboxVM.status.value -ne "decommissioning") {
                $randomguid = (([guid]::NewGuid().ToString()).ToCharArray() | Select -First 8) -join ""
                if ($NetboxVM.name -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))") {
                    $body = [PSCustomObject]@{
                        name          = "$($NetboxVM.name) #$randomguid"
                        status        = "decommissioning"
                        cluster       = $NetboxVM.cluster.id
                        tenant        = $NetboxVM.tenant.id
                        vcpus         = $NetboxVM.vcpus
                        disk          = $NetboxVM.disk
                        memory        = $NetboxVM.memory
                        custom_fields = [PSCustomObject]@{
                            WindowsInstalled = $NetboxVM.custom_fields.WindowsInstalled
                            Deleted          = $true
                            Host             = $NetboxVM.custom_fields.Host
                            VM_ID            = $NetboxVM.custom_fields.VM_ID
                            VM_STATE         = $NetboxVM.custom_fields.VM_STATE
                            Last_Update      = (Get-Date -Format "yyyy-MM-dd hh:mm:ss")
                        }
                    }
            
                    $VMResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/virtual-machines/$($NetboxVM.id)/") -Method PUT -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($body | ConvertTo-Json -Depth 5)
                    Write-Verbose "$($VMResult.name) now updated.."
                }
            }
            else {
                if ($LastUpdateOfVM -lt (Get-Date).AddDays(-30)) {
                    $VMResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/virtual-machines/$($NetboxVM.id)/") -Method DELETE -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header
                    Write-Verbose "$($NetboxVM.name) have been deleted"
                }
                else {
                    Write-Verbose "$($NetboxVM.Name) already decommissioning"
                }
            }
        }
        catch {
            Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text "Failed to update decommissioning status`n`nVM: $($body.name)`nCluster: $($NetboxVM.cluster.name)"
            Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text ($_ | Out-String)
            Continue
        }
        
    }

    #Adding new Nutanix VMs to Netbox
    Write-Output "Adding new Nutanix VMs to Netbox.."
    foreach ($NewVMGUID in $NewVMGUIDs) {
        try {
            if ($NewVMGUID -in $IgnoreVMUUID) { Continue }
        
            $NutanixVM = $NutanixVMs | ? { $_.metadata.UUID -eq $NewVMGUID }
            $NetboxCluster = $NetboxClusters | ? { $_.custom_fields.uuid -eq $NutanixVM.spec.cluster_reference.uuid }

            $ClusterSite = $NetboxCluster.site.id

            $NetboxTenant = $NetboxTenants | ? slug -eq $NutanixVM.metadata.project_reference.uuid

            #Checking for already exist
            $Check = $NetboxVMs | ? { $_.name -eq $NutanixVM.status.name -and $_.cluster.id -eq $NetboxCluster.id -and $_.tenant.id -eq $NetboxTenant.id -and $_.status -eq "active" }
            if ($Check) {
                #Send-SlackMessage -Token $SlackToken -Channel "#psautomation-messages" -Username "Sync-NutanixVMsIntoNetbox" -Text "Trying to create new VM object in Netbox, but there is already another VM object wih same Name, Tenant and Cluster!`nVM: $($NutanixVM.spec.name)`nTenant: $($NetboxTenant.Name)`nCluster: $($NetboxCluster.name)"
                Send-MessageToSlack -channel "#psautomation-messages" -Username "Azure Runbook Error" -text "Runbook: Sync-NutanixVMsIntoNetbox`nMessage: Trying to create new VM object in Netbox, but there is already another VM object wih same Name, Tenant and Cluster!`nVM: $($NutanixVM.spec.name)`nTenant: $($NetboxTenant.Name)`nCluster: $($NetboxCluster.name)"
                Continue
            }

            #region Check if another vm exists with same name on cluster
            
            $VMNameInNetbox = ((($NutanixVM.spec.name).ToCharArray() | select -first 64) -join "")
            
            $i = 0
            while ($NetboxVMs | ? { $_.name -eq $VMNameInNetbox -and $_.cluster.id -eq $NetboxCluster.id }) {
                #if this loop runs we need to keep renaming
                $i++
                if ($i -gt 99) { throw "failed creating a unique name for new netbox vm object" }
                
                #set initial name from source.
                $VMNameInNetbox = ((($NutanixVM.spec.name).ToCharArray() | select -first 61) -join "")
                
                #add $i suffix
                $VMNameInNetbox = $VMNameInNetbox + "($i)"
            }
          
            #endregion
          

            #Resources
            $vCPU = $NutanixVM.status.resources.num_sockets * $NutanixVM.status.resources.num_vcpus_per_socket
            $DiskSpace = [Math]::Round(($NutanixVM.status.resources.disk_list | where { $_.device_properties.device_type -ne "CDROM" } | measure disk_size_bytes -sum).sum / 1GB)
            $Memory = $NutanixVM.status.resources.memory_size_mib                     
            $NutanixHost = ($NutanixHosts | ? { $_.metadata.uuid -eq $NutanixVM.status.resources.host_reference.uuid } | Select -Unique).status.name
            
            if ($VMNameInNetbox -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))") {
                
                $body = [PSCustomObject]@{
                    name          = $VMNameInNetbox
                    site          = $ClusterSite
                    status        = "active"
                    cluster       = $NetboxCluster.id
                    tenant        = $NetboxTenant.id
                    vcpus         = $vCPU
                    disk          = $DiskSpace
                    memory        = $Memory
                    custom_fields = [PSCustomObject]@{
                        WindowsInstalled = if($NutanixVM.metadata.categories.WindowsInstalled -eq "True") { $true } else { $false }
                        Deleted          = $false
                        Host             = $NutanixHost
                        VM_ID            = $NutanixVM.metadata.uuid
                        VM_STATE         = $NutanixVM.spec.resources.power_state
                        Last_Update      = (Get-Date -Format "yyyy-MM-dd hh:mm:ss")
                        Source_Image     = ""
                    }
                }
                
                # Check if the VM has disks from an image and set the Source_Image field for comparison
                foreach ($disk in $NutanixVM.status.resources.disk_list | ? {$_.device_properties.device_type -eq "DISK"} ) {
                    if ($disk.data_source_reference.kind -eq "image") {
                        $imageUuid = $disk.data_source_reference.uuid
                        $matchingImage = $NutanixImages | Where-Object {
                            $_.metadata.uuid -eq $imageUuid
                        }
                        
                        if ($matchingImage) {
                            $body.custom_fields.Source_Image = $matchingImage.status.name
                            break # Use the first image found
                        }
                    }
                }
            
                Write-Verbose "`nVM: $($VMResult.Name) adding to netbox.."
                $VMResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/virtual-machines/") -Method POST -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($body | ConvertTo-Json -Depth 5)
            }

            #Take it easy..
            Start-Sleep -Milliseconds 500

            #Nics gets added later.

        }
        catch {
            Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text "Failed to add new VM Object`n`nVM: $($body.name)`nCluster: $($NetboxCluster.name)"
            Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text ($_ | Out-String)
            Continue
        }
        
        
    }

    #Updating Existing VMs from Nutanix to Netbox
    Write-Output "Updating Existing VMs from Nutanix to Netbox.."
    foreach ($ExistingVMGUID in $ExistingVMGUIDs) {
        if ($ExistingVMGUID -in $IgnoreVMUUID) { Continue }

        $NutanixVM = $NutanixVMs | ? { $_.metadata.UUID -eq $ExistingVMGUID }

        $NetboxVM = $NetboxVMs | ? { $_.custom_fields.VM_ID -eq $ExistingVMGUID }
        $Interfaces = $NetboxInterfaces | ? { $_.virtual_machine.id -eq $NetboxVM.id }
        
        $NetboxVM.cluster = $NetboxVM.cluster.id
        $NetboxVM.status = $NetboxVM.status.value
        $NetboxVM.tenant = $NetboxVM.tenant.id
        $NetboxCluster = $NetboxClusters | ? { $_.custom_fields.uuid -eq $NutanixVM.spec.cluster_reference.uuid }

        #Resources
        $vCPU = $NutanixVM.status.resources.num_sockets * $NutanixVM.status.resources.num_vcpus_per_socket
        $DiskSpace = [Math]::Round(($NutanixVM.status.resources.disk_list | where { $_.device_properties.device_type -ne "CDROM" } | measure disk_size_bytes -sum).sum / 1GB)
        $Memory = $NutanixVM.status.resources.memory_size_mib
        $NutanixHost = ($NutanixHosts | ? { $_.metadata.uuid -eq $NutanixVM.status.resources.host_reference.uuid } | Select -Unique).status.name
        
        $TenantID = ($NetboxTenants | ? { $_.Name -eq $NutanixVM.metadata.project_reference.name }).id
        if (!($TenantID)) {
            $TenantID = ($NetboxTenants | ? { $_.slug -eq $NutanixVM.metadata.project_reference.uuid }).id
        }
        
        #Check if vm name in netbox have suffix in case of conflict. like "Server1(1)"

        $VMNameInNetbox = ((($NutanixVM.spec.name).ToCharArray() | select -first 64) -join "")
        
        #Check if no difference. Continue. 
        if ($VMNameInNetbox -eq $netboxvm.name) {
            #vms have same name. Do nothing
        }
        #check if the only difference is the suffix. This means the server was not renamed.
        else {
            $NameDiff = $netboxvm.name -replace $VMNameInNetbox
            if ($NameDiff -match "\(\d{1,2}\)") {
                #The only difference is the suffix. We Set the name as it already is
                $VMNameInNetbox = $netboxvm.name
            }
            else {
                #There are other differences in the vm name, we need to rename the server and also check if its unique
                $i = 0
                while ($NetboxVMs | ? { $_.name -eq $VMNameInNetbox -and $_.cluster.id -eq $NetboxCluster.id }) {
                    #if this loop runs we need to keep renaming until its unique
                    $i++
                    if ($i -gt 99) { throw "failed creating a unique name for new netbox vm object" }
                
                    #set initial name from source.
                    $VMNameInNetbox = ((($NutanixVM.spec.name).ToCharArray() | select -first 61) -join "")
                
                    #add $i suffix
                    $VMNameInNetbox = $VMNameInNetbox + "($i)"
                }
            }
        }
        
        
        



        if ($VMNameInNetbox -notmatch "VeeamTmp-(([0-9A-Fa-f]{8}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{4}[-]?[0-9A-Fa-f]{12}))") {
            $body = [PSCustomObject]@{
                name          = $VMNameInNetbox
                status        = "active"
                cluster       = $NetboxCluster.id
                tenant        = $TenantID
                vcpus         = $vCPU
                disk          = $DiskSpace
                memory        = $Memory
                custom_fields = [PSCustomObject]@{
                    WindowsInstalled = if($NutanixVM.metadata.categories.WindowsInstalled -eq "True") { $true } else { $false }
                    Deleted          = $false
                    Host             = $NutanixHost
                    VM_ID            = $NutanixVM.metadata.uuid
                    VM_STATE         = $NutanixVM.status.resources.power_state
                    #Last_Update = (Get-Date -Format "yyyy-MM-dd hh:mm:ss")
                    Source_Image     = ""
                }
            }

            # Check if the VM has disks from an image and set the Source_Image field for comparison
            foreach ($disk in $NutanixVM.status.resources.disk_list | ? {$_.device_properties.device_type -eq "DISK"} ) {
                if ($disk.data_source_reference.kind -eq "image") {
                    $imageUuid = $disk.data_source_reference.uuid
                    $matchingImage = $NutanixImages | Where-Object {
                        $_.metadata.uuid -eq $imageUuid
                    }
                    
                    if ($matchingImage) {
                        $body.custom_fields.Source_Image = $matchingImage.status.name
                        break # Use the first image found
                    }
                }
            }
            
            if (ISObjectsEqual -ReferenceObject $body -DifferenceObject $NetboxVM) {
                Write-Verbose "$($NutanixVM.status.name) - VM propeties are the same."
            }
            else {
                $body = [PSCustomObject]@{
                    name          = $VMNameInNetbox
                    status        = "active"
                    cluster       = $NetboxCluster.id
                    tenant        = $TenantID
                    vcpus         = $vCPU
                    disk          = $DiskSpace
                    memory        = $Memory
                    custom_fields = [PSCustomObject]@{
                        WindowsInstalled = if($NutanixVM.metadata.categories.WindowsInstalled -eq "True") { $true } else { $false }
                        Deleted          = $false
                        Host             = $NutanixHost
                        VM_ID            = $NutanixVM.metadata.uuid
                        VM_STATE         = $NutanixVM.status.resources.power_state
                        Last_Update      = (Get-Date -Format "yyyy-MM-dd hh:mm:ss")
                        Source_Image     = ""
                    }
                }
                
                # Check if the VM has disks from an image and set the Source_Image field for comparison
                foreach ($disk in $NutanixVM.status.resources.disk_list | ? {$_.device_properties.device_type -eq "DISK"} ) {
                    if ($disk.data_source_reference.kind -eq "image") {
                        $imageUuid = $disk.data_source_reference.uuid
                        $matchingImage = $NutanixImages | Where-Object {
                            $_.metadata.uuid -eq $imageUuid
                        }
                        
                        if ($matchingImage) {
                            $body.custom_fields.Source_Image = $matchingImage.status.name
                            break # Use the first image found
                        }
                    }
                }
                $VMResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/virtual-machines/$($NetboxVM.id)/") -Method Patch -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($body | ConvertTo-Json -Depth 5)
                Write-Verbose "`n$($VMResult.Name) have been updated.."
            }
        }
    }

#region Updating interfaces from Nutanix to Netbox
Write-Output "Updating interfaces from Nutanix to Netbox.."

# Get all IP addresses from Netbox for comparison and synchronization
Write-Output "Getting IP addresses from Netbox"
$NetboxIPs = Invoke-NBAPI -baseuri $NetboxParams.baseuri -Command "ipam/ip-addresses/" -header $NetboxParams.header

foreach ($ExistingVMGUID in $ExistingVMGUIDs) {
    if ($ExistingVMGUID -in $IgnoreVMUUID) { Continue }

    $NutanixVM = $NutanixVMs | ? { $_.metadata.UUID -eq $ExistingVMGUID }
    $NetboxVM = $NetboxVMs | ? { $_.custom_fields.VM_ID -eq $ExistingVMGUID }
    $Cluster = $NetboxClusters | ? { $_.id -eq $NetboxVM.cluster }
    $Interfaces = $NetboxInterfaces | ? { $_.virtual_machine.id -eq $NetboxVM.id }
    $NICs = $NutanixVM.status.resources.nic_list

    # Get existing IP addresses for this VM's interfaces
    $VMIPs = $NetboxIPs | Where-Object { 
        $_.assigned_object_type -eq "virtualization.vminterface" -and 
        $Interfaces.id -contains $_.assigned_object_id 
    }

    Write-Verbose "Processing interfaces for VM $($NetboxVM.name) ($ExistingVMGUID)"

    #foreach Netbox interface, check if it needs to be deleted
    foreach ($Interface in $Interfaces) {
        $NeedsUpdate = $false
        $NTXInterface = $NICs | ? { $_.UUID -eq $Interface.name }
        if ($NTXInterface) {
            Write-Verbose "Interface $($Interface.name) exists on VM $($NetboxVM.name)"
        }
        else {
            # Delete IP addresses assigned to this interface first
            $InterfaceIPs = $VMIPs | ? { $_.assigned_object_id -eq $Interface.id }
            foreach ($IP in $InterfaceIPs) {
                try {
                    $IPResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/ipam/ip-addresses/$($IP.id)/") -Method DELETE -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header
                    Write-Verbose "Removed IP $($IP.address) from interface $($Interface.name) before interface deletion"
                } catch {
                    Write-Verbose "Failed to remove IP $($IP.address) from interface $($Interface.name): $_"
                }
            }
            
            # Then delete the interface
            $request = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/interfaces/$($Interface.id)/") -Method Delete -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header
            Write-Verbose "Interface $($Interface.name) deleted from VM $($NetboxVM.name)"
        }
    }
    
    # Track newly created/updated IP addresses for primary IP selection
    $VMIPAddresses = @()
    
    #foreach Nutanix interface create/update in netbox
    foreach ($Nic in $NICs) {
        #region interface sync

        #get access vlanid
        $VLANID = ($NutanixSubnets | ? { $_.metadata.uuid -eq $Nic.subnet_reference.uuid }).spec.resources.vlan_id
        If ($VLANID -eq 0) {
            Continue
        }
        
        #get netbox vlan object for untagged vlan
        $untaggedVlan = $NetboxVlans | ? { $_.vid -eq $VLANID -and $_.site.id -eq $NetboxVM.site.id } | sort { $_.group.id } | Select -First 1
        if (-not $untaggedVlan) {
            Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text "(VLAN ID $VLANID not created in netbox) - (Site: $($NetboxVM.site.id))"
            Continue
        }

        #get all tagged vlan ids from nutanix interface
        $taggedVlans = $nic.trunked_vlan_list
        
        #get netbox vlans for tagged vlans
        $NBtaggedVlans = $NetboxVlans | ? { $_.vid -in $taggedVlans -and $_.site.id -eq $NetboxVM.site.id -and $_.group.id -eq 3 }

        #TODO: Remove this function after its online in cf module (pushed 16/3-2023)
        function Convert-NutanixNICToNetboxVLANMode {
            param($Nic)
            
            if ($Nic.vlan_mode -eq "ACCESS") {
                $String = "access"
            }
            elseif ($Nic.vlan_mode -eq "TRUNKED") {
                #check if tagged or tagged (all)
                if ($nic.trunked_vlan_list) {
                    $String = "tagged"
                }
                else {
                    $String = "tagged-all"
                }
            }
            return $string
        }

        #Get the netbox interface (if it exists)
        $Interface = $Interfaces | ? { $_.Name -eq $Nic.UUID }
        
        # Interface body based on Nutanix data
        $body = [PSCustomObject]@{
            virtual_machine = $NetboxVM.id
            name            = $Nic.UUID
            mode            = Convert-NutanixNICToNetboxVLANMode -nic $NIC
            mac_address     = $Nic.mac_address
            untagged_vlan   = $untaggedVlan.id
        }
        
        if ($NBtaggedVlans) {
            $body | Add-Member -MemberType NoteProperty -Name "tagged_vlans" -Value @($NBtaggedVlans.id)
        }

        $interfaceUpdated = $false
        $currentInterface = $Interface

        if ($Interface) {
            #interface exists in netbox. check if we need to update it.

            #flatten mode or else netbox will decline the request
            $Interface.mode = $Interface.mode.value
            $Interface.virtual_machine = $Interface.virtual_machine.id
            $Interface.untagged_vlan = $Interface.untagged_vlan.id
            $Interface.tagged_vlans = $Interface.tagged_vlans.id

            #Check if nutanix and netbox interfaces are identical
            if (ISObjectsEqual -ReferenceObject $body -DifferenceObject $Interface) {
                Write-Verbose "VM $($NetboxVM.name) - NIC properties are the same for interface $($Nic.UUID)"
            }
            else {
                Write-Verbose "Interface $($Nic.UUID) on VM $($NetboxVM.name) has changed - updating"
                $currentInterface = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/interfaces/$($Interface.id)/") -Method Put -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($body | ConvertTo-Json -Depth 5)
                $interfaceUpdated = $true
            }
        }
        else {
            #create interface in netbox
            Write-Verbose "Creating new interface $($Nic.UUID) on VM $($NetboxVM.name)"
            $currentInterface = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/interfaces/") -Method Post -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($body | ConvertTo-Json -Depth 5)
            $interfaceUpdated = $true
        }

        # Process IP addresses for this interface
        if ($currentInterface) {
            # Get IP addresses for this interface from Nutanix
            $NutanixIPAddresses = @($Nic.ip_endpoint_list.ip | Where-Object { ![string]::IsNullOrWhiteSpace($_) })
            
            # Get existing Netbox IP addresses for this interface
            $InterfaceIPs = $VMIPs | Where-Object { $_.assigned_object_id -eq $currentInterface.id }
            
            Write-Verbose "Syncing $($NutanixIPAddresses.Count) IP address(es) for interface $($Nic.UUID) on VM $($NetboxVM.name)"
            
            # Process each IP address from Nutanix
            foreach ($NutanixIPAddress in $NutanixIPAddresses) {
                # Skip empty IPs
                if ([string]::IsNullOrWhiteSpace($NutanixIPAddress)) { continue }
                
                # Determine CIDR notation
                $IPWithCIDR = if ($NutanixIPAddress -match ":") {
                    # IPv6
                    "$NutanixIPAddress/128"
                } else {
                    # IPv4
                    "$NutanixIPAddress/32"
                }
                
                # Check if IP already exists in Netbox for this interface
                $ExistingIP = $InterfaceIPs | Where-Object { 
                    $_.address -eq $IPWithCIDR -or 
                    ($_.address -replace "/\d+$", "") -eq $NutanixIPAddress 
                }
                
                if ($ExistingIP) {
                    Write-Verbose "IP $IPWithCIDR already exists for interface $($Nic.UUID)"
                    # Add to our list of IPs for primary IP selection
                    $VMIPAddresses += $ExistingIP
                } else {
                    # Create new IP
                    $IPbody = @{
                        address = $IPWithCIDR
                        assigned_object_type = "virtualization.vminterface"
                        assigned_object_id = $currentInterface.id
                        status = "active"
                    }
                    
                    try {
                        $IPResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/ipam/ip-addresses/") -Method POST -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($IPbody | ConvertTo-Json)
                        Write-Verbose "Added IP $IPWithCIDR to interface $($Nic.UUID) on VM $($NetboxVM.name)"
                        # Add to our list of IPs for primary IP selection
                        $VMIPAddresses += $IPResult
                    } catch {
                        Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text "Failed to add IP $IPWithCIDR to interface $($Nic.UUID) on VM $($NetboxVM.name)"
                        Write-Verbose "Failed to add IP $IPWithCIDR $_"
                    }
                }
            }
            
            # Remove IPs that no longer exist in Nutanix
            foreach ($ExistingIP in $InterfaceIPs) {
                $IPAddress = $ExistingIP.address -replace "/\d+$", ""
                if ($IPAddress -notin $NutanixIPAddresses) {
                    try {
                        $IPResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/ipam/ip-addresses/$($ExistingIP.id)/") -Method DELETE -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header
                        Write-Verbose "Removed IP $($ExistingIP.address) from interface $($Nic.UUID) on VM $($NetboxVM.name)"
                        
                        # Remove from our list of IPs if it's there
                        $VMIPAddresses = $VMIPAddresses | Where-Object { $_.id -ne $ExistingIP.id }
                    } catch {
                        Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text "Failed to remove IP $($ExistingIP.address) from interface $($Nic.UUID) on VM $($NetboxVM.name)"
                        Write-Verbose "Failed to remove IP $($ExistingIP.address): $_"
                    }
                }
            }
        }
        #endregion interface sync
    }
    
    # After processing all interfaces, set primary IP for VM
    if ($VMIPAddresses.Count -gt 0) {
        # Refresh VM IPs to ensure we have the latest data
        $VMIPs = $NetboxIPs | Where-Object { 
            $_.assigned_object_type -eq "virtualization.vminterface" -and 
            $Interfaces.id -contains $_.assigned_object_id 
        }
        
        # Filter IPv4 and IPv6 addresses
        $IPv4Addresses = $VMIPAddresses | Where-Object { $_.address -notmatch ":" }
        $IPv6Addresses = $VMIPAddresses | Where-Object { $_.address -match ":" }
        
        $VMUpdateNeeded = $false
        $VMUpdateBody = @{}
        
        # Set primary IPv4 if available
        if ($IPv4Addresses.Count -gt 0) {
            $PrimaryIPv4 = $IPv4Addresses | Select-Object -First 1
            
            # Check if it's already set as primary
            if ($NetboxVM.primary_ip4.id -ne $PrimaryIPv4.id) {
                $VMUpdateBody.primary_ip4 = $PrimaryIPv4.id
                $VMUpdateNeeded = $true
                Write-Verbose "Setting primary IPv4 address to $($PrimaryIPv4.address) for VM $($NetboxVM.name)"
            }
        }
        
        # Set primary IPv6 if available
        if ($IPv6Addresses.Count -gt 0) {
            $PrimaryIPv6 = $IPv6Addresses | Select-Object -First 1
            
            # Check if it's already set as primary
            if ($NetboxVM.primary_ip6.id -ne $PrimaryIPv6.id) {
                $VMUpdateBody.primary_ip6 = $PrimaryIPv6.id
                $VMUpdateNeeded = $true
                Write-Verbose "Setting primary IPv6 address to $($PrimaryIPv6.address) for VM $($NetboxVM.name)"
            }
        }
        
        # Update VM if needed
        if ($VMUpdateNeeded) {
            try {
                $VMResult = Invoke-RestMethod -Uri ($NetboxParams.baseuri + "/virtualization/virtual-machines/$($NetboxVM.id)/") -Method PATCH -ContentType "application/json; charset=utf-8" -Headers $NetboxParams.header -Body ($VMUpdateBody | ConvertTo-Json)
                Write-Verbose "Updated primary IP(s) for VM $($NetboxVM.name)"
            } catch {
                Write-Verbose "Failed to update primary IP for VM $($NetboxVM.name): $_"
                Send-MessageToSlack -Channel "#psautomation-messages" -Username "PSAutomation - Sync-NutanixVMsIntoNetbox" -Text "Failed to update primary IP for VM $($NetboxVM.name)"
            }
        }
    }
}
#endregion
}
catch {
    throw $_
}
