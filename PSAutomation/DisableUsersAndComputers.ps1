#requires -Modules ActiveDirectory

Param(
    $SlackChannel="#dc-reports",
    $ActiveDirectories=@("iaas.cloudfactory.dk"),
    $InactiveDays = 180
)

$ErrorActionPreference="Stop"

#SIDs to Ignore
$IgnoreUserSIDs=@(
    "S-1-5-21-686828138-3873335906-3870440237-2452" #NutanixDefaultProjectUser
    "S-1-5-21-*********-**********-**********-500" #Administrator
    "S-1-5-21-*********-**********-**********-1143"
    "S-1-5-21-*********-**********-**********-1167"
    "S-1-5-21-*********-**********-**********-1154"
    "S-1-5-21-*********-**********-**********-1147"
    "S-1-5-21-*********-**********-**********-1138"
    "S-1-5-21-*********-**********-**********-1147"
)

$Date = (Get-Date).AddDays(-($InactiveDays))
    
foreach ($ActiveDirectorie in $ActiveDirectories){
    $InactiveComputers = Get-ADComputer -Server $ActiveDirectorie -Filter {LastLogonTimeStamp -lt $Date -And Enabled -eq $true} -Properties Name, OperatingSystem, SamAccountName, DistinguishedName, LastLogonDate
    $InactiveUsers = Get-ADUser -Server $ActiveDirectorie -Filter * -Properties LastLogonDate | Where-Object {$_.LastLogonDate -lt $Date -and $_.Enabled -eq $true -and $_.DistinguishedName -notlike "*OU=WAPAdmins,OU=Hosting,DC=access,DC=cloudfactory,DC=dk*" -and $_.DistinguishedName -notlike "*OU=team.blue,OU=Users,OU=CF,DC=iaas,DC=cloudfactory,DC=dk*" -and $_.Name -notmatch '\$$' -and $_.SID.Value -notin $IgnoreUserSIDs}
    
    $Credential = Get-SafeCredentials -CredentialName "SerPSROT"
    
    if ($InactiveComputers){
        foreach ($InactiveComputer in $InactiveComputers){
            Set-ADComputer -Server $ActiveDirectorie -Identity "$($InactiveComputer.Name)" -Enabled $false -Credential $Credential
        }

        #Sending Slack Message
        $ComputerList = $InactiveComputers.name -join ", "
        $text = "Message: Following Computers have been disabled!`n`nActive Directory: $ActiveDirectorie`nComputers: $ComputerList"
        Send-MessageToSlack -Channel $SlackChannel -UserName "PSAutomation - Active Directories Update" -Text $text
    }

    if ($InactiveUsers){
        foreach ($InactiveUser in $InactiveUsers){
            Disable-ADAccount -Server $ActiveDirectorie -Identity $InactiveUser -Credential $Credential
        }

        #Sending Slack Message
        $UserList = $InactiveUsers.name -join ", "
        $text = "Message: Following Users have been disabled!`n`nActive Directory: $ActiveDirectorie`nUsers: $UserList"
        Send-MessageToSlack -Channel $SlackChannel -UserName "PSAutomation - Active Directories Update" -Text $text
    }
}
