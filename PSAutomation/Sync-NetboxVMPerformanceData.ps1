
[Parameter(Mandatory = $false)]
$StartDate = ((Get-Date).AddDays(-31))
    

$ErrorActionPreference = "Stop"

$NTXvms = Get-NutanixVMs
$Clusters = Get-NutanixClusters
#exclude prism central cluster
$Clusters = $Clusters | ? { $_.status.resources.config.service_list -eq "AOS" } #value can also be "PRISM_CENTRAL"

#Getting Test-SafeCredentials Objects
#$Clusters = @("DK01NTXC01";"DK01NTXC02";"DK01NTXC03";"DK01NTXC04";"DK01NTXC05";"DK01NTXC06")
$Credentials = foreach ($Cluster in $Clusters.status.name) {
    [PSCustomObject]@{
        Name       = $Cluster
        Credential = Test-SafeCredentials -Application $Cluster
    }
}
$NBVms = Get-NetboxVMs
#Getting all Netbox Tenants
$Credential = Test-SafeCredentials -Application NetboxToken
$NetboxsTenants = Invoke-NBAPI -baseuri $Credential.baseuri -Command "tenancy/tenants/" -header $Credential.header


$i = 0
$result = @()
#Sort on customfield Performance_Data_Last_Update. Start with the vm that has the oldest data
#| ? {$_.id -eq 18891}
foreach ($NBVm in $NBVms  | Sort-Object -Property custom_fields.Performance_Data_Last_Update) {
        
    $i++
    Write-Progress -Activity "Calculating Recommendations" -Status $NBVm.display -PercentComplete ($i / $NBVms.count * 100)

    #region initial checks

    #check if vm is active in netbox
    if ($NBVm.status.value -eq "decommissioning") {
        Write-Verbose "VM $($NBVm.display) is decommissioning. Skipping it"
        continue
    }
        
    #check if vm is updated with 24 hours. If yes, skip it. Make sure to handle null values from netbox.
    #if console is interactive then we always run
    if ($NBVm.custom_fields.Performance_Data_Last_Update -and (!([System.Environment]::UserInteractive))) {
        # if script is run interactively then we always run
 

        $LastUpdate = Get-Date $NBVm.custom_fields.Performance_Data_Last_Update
        if ($LastUpdate -gt (Get-Date).AddDays(-1)) {
            Write-Verbose "VM $($NBVm.display) is updated within 24 hours. Skipping it"
            continue
        }
        
        
    }
    #Check if VM exists in Nutanix
    $NTXVM = $NTXVMs | ? { $_.metadata.uuid -eq $NBVm.custom_fields.VM_ID }

    #continue if VM dosent exist in Nutanix
    if (-not $NTXVM) {
        Write-Verbose "VM $($NBVm.display) does not exist in Nutanix. Skipping it"
        continue
    }


        
    #skip if vms is powered off
    if ($NTXVM.spec.resources.power_state -eq "OFF") {
        Write-Verbose "VM $($NTXVM.spec.name) is powered off. Skipping it"
        continue
    }
    #endregion initial checks

    #region Get performance Data from vm
        
    #region CPU
    $CPUMetricParams = @{
        vmUUID                = $ntxvm.metadata.uuid
        clusterName           = $ntxvm.spec.cluster_reference.name
        metric                = "hypervisor_cpu_usage_ppm"
        startDate             = Get-Date $StartDate -Format "dd-MM-yyyy hh:mm:ss"
        endDate               = Get-Date -Format "dd-MM-yyyy hh:mm:ss"
        intervalInSecs        = "300"
        TestCredentialsObject = ($Credentials | ? { $_.Name -eq $ntxvm.spec.cluster_reference.name } | Select -ExpandProperty Credential)
    }
    try {
        $CPUMetricData = Get-NutanixMetricVM @CPUMetricParams

        $Top95PercentCPU = $CPUMetricData | Sort-Object -Descending | Select-Object -First 1 -skip ($CPUMetricData.Count * 0.05)
        $Top95PercentCPU = $Top95PercentCPU / 1000000
        
        $VMCoreCount = $NTXVM.spec.resources.num_sockets * $NTXVM.spec.resources.num_vcpus_per_socket
        $CoresConsumed = [math]::Round($Top95PercentCPU * $VMCoreCount, 1)
    }
    catch {
        #Write-Warning "Could not get CPU data for $($NTXVM.spec.name)"
        $CoresConsumed = -1
    }   
    #endregion CPU

    #region IOPS

    $IOPSMetricParams = @{
        vmUUID                = $ntxvm.metadata.uuid
        clusterName           = $ntxvm.spec.cluster_reference.name
        metric                = "controller_num_iops"
        startDate             = Get-Date $StartDate -Format "dd-MM-yyyy hh:mm:ss"
        endDate               = Get-Date -Format "dd-MM-yyyy hh:mm:ss"
        intervalInSecs        = "300"
        TestCredentialsObject = ($Credentials | ? { $_.Name -eq $ntxvm.spec.cluster_reference.name } | Select -ExpandProperty Credential)
    }
    try {
        $IOPSMetricData = Get-NutanixMetricVM @IOPSMetricParams

        $Top95PercentIOPS = $IOPSMetricData | Sort-Object -Descending | Select-Object -First 1 -skip ($CPUMetricData.Count * 0.05)

    }
    catch {
        #Write-Warning "Could not get IOPS data for $($NTXVM.spec.name)"
        $Top95PercentIOPS = -1
    }
    #endregion IOPS

    #region IO Latency
    #controller_avg_io_latency_usecs
    $IOLatencyMetricParams = @{
        vmUUID                = $ntxvm.metadata.uuid
        clusterName           = $ntxvm.spec.cluster_reference.name
        metric                = "controller_avg_io_latency_usecs"
        startDate             = Get-Date $StartDate -Format "dd-MM-yyyy hh:mm:ss"
        endDate               = Get-Date -Format "dd-MM-yyyy hh:mm:ss"
        intervalInSecs        = "300"
        TestCredentialsObject = ($Credentials | ? { $_.Name -eq $ntxvm.spec.cluster_reference.name } | Select -ExpandProperty Credential)
    }
    try {
        $IOLatencyMetricData = Get-NutanixMetricVM @IOLatencyMetricParams

        $Top95PercentIOLatency = $IOLatencyMetricData | Sort-Object -Descending | Select-Object -First 1 -skip ($CPUMetricData.Count * 0.05)
        $Top95PercentIOLatency = [math]::Round($Top95PercentIOLatency / 1000, 1)
    }
    catch {
        #Write-Warning "Could not get IO Latency data for $($NTXVM.spec.name)"
        $Top95PercentIOLatency = -1
    }
    #endregion IO Latency

    #region Memory
    #hypervisor_memory_usage_ppm
    $MemoryMetricParams = @{
        vmUUID                = $ntxvm.metadata.uuid
        clusterName           = $ntxvm.spec.cluster_reference.name
        metric                = "memory_usage_ppm"
        startDate             = Get-Date $StartDate -Format "dd-MM-yyyy hh:mm:ss"
        endDate               = Get-Date -Format "dd-MM-yyyy hh:mm:ss"
        intervalInSecs        = "300"
        TestCredentialsObject = ($Credentials | ? { $_.Name -eq $ntxvm.spec.cluster_reference.name } | Select -ExpandProperty Credential)
    }
    try {
        $MemoryMetricData = Get-NutanixMetricVM @MemoryMetricParams

        $Top95PercentMemory = $MemoryMetricData | Sort-Object -Descending | Select-Object -First 1 -skip ($CPUMetricData.Count * 0.05)
        $Top95PercentMemory = $Top95PercentMemory / 1000000
        $VMMemoryGiBAssigned = $ntxvm.spec.resources.memory_size_mib / 1024
        $VMMemoryGiBUsed = [math]::Round($Top95PercentMemory * $VMMemoryGiBAssigned, 1)
    }
    catch {
        #Write-Warning "Could not get Memory data for $($NTXVM.spec.name)"
        $VMMemoryGiBUsed = -1
    }
    #endregion Memory
   
    #region Data Written pr day
    #using: controller_write_io_bandwidth_kBps

    $DataWrittenMetricParams = @{
        vmUUID                = $ntxvm.metadata.uuid
        clusterName           = $ntxvm.spec.cluster_reference.name
        metric                = "controller_write_io_bandwidth_kBps"
        startDate             = Get-Date $StartDate -Format "dd-MM-yyyy hh:mm:ss"
        endDate               = Get-Date -Format "dd-MM-yyyy hh:mm:ss"
        intervalInSecs        = 60 * 60 * 24
        TestCredentialsObject = ($Credentials | ? { $_.Name -eq $ntxvm.spec.cluster_reference.name } | Select -ExpandProperty Credential)
    }
    try {
        $DataWrittenMetricData = Get-NutanixMetricVM @DataWrittenMetricParams
        $avgKBps = $DataWrittenMetricData | Measure-Object -Average | select -ExpandProperty Average
        $avgDailyKB = $avgKBps * 60 * 60 * 24
        $avgDailyGB = [math]::Round($avgDailyKB / 1024 / 1024)

    }
    catch {
        #Write-Warning "Could not get Data Written pr day data for $($NTXVM.spec.name)"
        $DataWrittenPrDay = -1
    }


    #endregion Data Written pr day


    #region Disk Usage GB
    #using: controller_user_bytes

    $DiskUsageMetricParams = @{
        vmUUID                = $ntxvm.metadata.uuid
        clusterName           = $ntxvm.spec.cluster_reference.name
        metric                = "controller_user_bytes"
        startDate             = Get-Date ((get-date).AddMinutes(-2)) -Format "dd-MM-yyyy hh:mm:ss"
        endDate               = Get-Date -Format "dd-MM-yyyy hh:mm:ss"
        intervalInSecs        = 60 
        TestCredentialsObject = ($Credentials | ? { $_.Name -eq $ntxvm.spec.cluster_reference.name } | Select -ExpandProperty Credential)
    }
    try {
        $DiskUsageMetricData = Get-NutanixMetricVM @DiskUsageMetricParams
        $DiskUsageBytes = $DiskUsageMetricData | select -Last 1
        $DiskUsageGB = [math]::Round($DiskUsageBytes / 1GB, 0)
    }
    catch {
        #Write-Warning "Could not get Data Written pr day data for $($NTXVM.spec.name)"
        $DataWrittenPrDay = -1
    }


    #endregion Disk Usage GB

    #endregion Get performance Data from vm

    #region Update to Netbox
    try {
        $body = [PSCustomObject]@{
            custom_fields = [PSCustomObject]@{
                Disk_GiB_Daily_Write         = $avgDailyGB
                Disk_Usage_GB                = $DiskUsageGB
                Memory_GiB_Used_95p          = $VMMemoryGiBUsed
                IO_Latency_95p               = $Top95PercentIOLatency
                IOPS_95p                     = $Top95PercentIOPS
                CPU_Cores_Used_95p           = $CoresConsumed
                Performance_Data_Last_Update = (Get-Date -Format "yyyy-MM-dd")
            }
        }
        Invoke-RestMethod -Uri ($Credential.baseuri + "/virtualization/virtual-machines/$($NbVM.id)/") -Method Patch -ContentType "application/json; charset=utf-8" -Headers $Credential.header -Body ($body | ConvertTo-Json -Depth 5)
    }
    catch {
        Write-Warning "Could not update Netbox VM $($NBVm.display). `n`n Error: `n`n $($_|Out-String)`n`n Body: `n`n $($body | ConvertTo-Json -Depth 5)"
    }
    
    #endregion Update to Netbox   

}