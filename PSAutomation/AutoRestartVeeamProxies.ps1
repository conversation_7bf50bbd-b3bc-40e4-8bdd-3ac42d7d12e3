#Requires -Version 5
$errorActionPreference = "Stop"
try{


<#######################################################################
# Check all Veeam proxies for job configuration compliance.
# - Check if jobs are running
# - Check if there is no bandwidth usage for 1 hour +
# - If yes, do restart veeamahvbackup.service on that proxy
  #######################################################################>


# --- Main Logic ---
write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] <PERSON><PERSON><PERSON> started" 
write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Initializing TLS certificates" 
Invoke-TrustAllCertificates

write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Loading proxy server configuration" 
$ProxyServers = @"
"ClusterName","ProxyIP","RepoServer"
"DK01NTXC01", "***********"
"DK01NTXC02", "***********"
"DK01NTXC03", "***********"
"DK01NTXC04", "***********"
"DK01NTXC05", "***********"
"DK01NTXC06", "************"
"@ | ConvertFrom-Csv -Delimiter ","

write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Getting credentials for Veeam Proxy servers" 
$Credential = Get-SafeCredentials -CredentialName "VeeamProxy"


try {
    if (Get-VbrServer -ErrorAction SilentlyContinue){
        Disconnect-VBRServer
    }
}
catch {
}

write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Connecting Veeam servers" 
$Servers=invoke-command -ComputerName "DK01VBR01.iaas.cloudfactory.dk" -ScriptBlock {
    Connect-VBRServer -Server "DK01VBR01.iaas.cloudfactory.dk"
    Get-VbrServer
}


<# Test data
$ProxyServer = $ProxyServers[3]
#>
write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Connecting to PRTG" 
Connect-PRTG

write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Getting PRTG devices" 
$PRTGDevices=get-device 

write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Starting proxy server checks" 
foreach ($ProxyServer in $ProxyServers) {
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Processing proxy: $($ProxyServer.ClusterName) ($($ProxyServer.ProxyIP))" 

    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Connecting to API for $($ProxyServer.ClusterName)" 
    $proxy_ip = $ProxyServer.ProxyIP
    $baseURI = "https://$proxy_ip/api/"
    $LoginBody = @{
        grantType = "Password"
        Username  = $Credential.UserName
        Password  = (Decrypt-SecureString -Securestring $Credential.Password)
    }
    
    try {
        # Create a callback to bypass SSL certificate validation
        $certCallback = @"
        using System;
        using System.Net;
        using System.Net.Security;
        using System.Security.Cryptography.X509Certificates;
        public class ServerCertificateValidationCallback
        {
            public static void Ignore()
            {
                ServicePointManager.ServerCertificateValidationCallback += 
                    delegate
                    (
                        Object obj, 
                        X509Certificate certificate, 
                        X509Chain chain, 
                        SslPolicyErrors errors
                    )
                    {
                        return true;
                    };
            }
        }
"@
        
        # Only add the type if it doesn't exist
        if (-not ([System.Management.Automation.PSTypeName]'ServerCertificateValidationCallback').Type) {
            Add-Type -TypeDefinition $certCallback
        }
        
        # Call the ignore method
        [ServerCertificateValidationCallback]::Ignore()
        
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Attempting API connection with certificate validation disabled" 
        $AccessToken = Invoke-RestMethod -Method Post -Uri ($baseURI + "oauth2/token") -Body $LoginBody
        $header = @{Authorization = "Bearer $($AccessToken.accessToken)" }
        
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Getting jobs for $($ProxyServer.ClusterName)" 
        $jobs = Invoke-RestMethod -Method Get -Uri ($baseURI + "v6/jobs") -Headers $header | Select-Object -ExpandProperty results
        
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Checking for running jobs on $($ProxyServer.ClusterName)" 
        $RunningJobs=$jobs | Where-Object { $_.status -eq "Running" } 
        if (!($RunningJobs)) {
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] No jobs are running for $($ProxyServer.ClusterName)" 
            continue
        }
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Found $($RunningJobs.Count) running jobs on $($ProxyServer.ClusterName)" 
    }
    catch {
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Error connecting to $($ProxyServer.ClusterName): $_" 
        continue
    }

    
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Checking repository server for $($ProxyServer.ClusterName)" 
    #Check if repo is using bandwidth. Repo is a windows server
    $Reposerver=$Servers | Where-Object { $_.description -match $ProxyServer.ClusterName }
    #validate 1 entry in variable
    if (($Reposerver| Measure-Object | Select-Object -ExpandProperty Count) -ne 1) {
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ERROR: Repo server count not 1 for $($ProxyServer.ClusterName)." 
        throw "Repo server count not 1 for $($ProxyServer.ClusterName)"
    }
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Found repository server: $($Reposerver.description)" 
    
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Looking up PRTG device for $($Reposerver.description)" 
    #Get performance counter for server on bandwidth utilization
    $PRTGDevice=$PRTGDevices | ? name -match $Reposerver.description
    #validate 1 entry
    if ($PRTGDevice.count -ne 1) {
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ERROR: Multiple devices found for $($Reposerver.description)" 
        throw "Multiple devices found for $($Reposerver.description)"
    }
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Found PRTG device: $($PRTGDevice.name)" 

    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Looking up bandwidth sensor for $($PRTGDevice.name)" 
    $PRTGDeviceBandwidthSensor=get-sensor -device $PRTGDevice -name "Nutanix VirtIO Ethernet Adapter"
    #validate 1 entry
    if ($PRTGDeviceBandwidthSensor.count -ne 1) {
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ERROR: Multiple sensors found for $($Reposerver.description)" 
        throw "Multiple sensors found for $($Reposerver.description)"
    }   
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Found bandwidth sensor: $($PRTGDeviceBandwidthSensor.name) (ID: $($PRTGDeviceBandwidthSensor.id))" 
    
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Retrieving bandwidth history for the last hour" 
    #Get last 1 hour of data
    $PRTGDeviceBandwidthSensorData = Get-SensorHistory -Id $PRTGDeviceBandwidthSensor.id -StartDate (Get-Date) -EndDate (Get-Date).AddHours(-1)
    $BandwitchMbps=$PRTGDeviceBandwidthSensorData."Total(Speed)"
    
    $BandwitdhMeasure=$BandwitchMbps | measure -Average -Maximum
    write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Bandwidth stats - Avg: $($BandwitdhMeasure.Average) Mbps, Max: $($BandwitdhMeasure.Maximum) Mbps" 
    
    #check if bandwitdh avg. is below 100 Mbps and peak below 100Mbps. Also the first 10 minutes of the hour should be below 10Mbps
    if ($BandwitdhMeasure.Average -lt 10 -and ($BandwitchMbps[0..9] -lt 100).Count -eq 10) {
        

        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] No significant bandwidth usage for $($ProxyServer.ClusterName) in the last hour" 
        #Stop jobs by calling proxy api
        foreach ($job in $RunningJobs) {
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Stopping job $($job.name) on $($ProxyServer.ClusterName)" 

            #/api/v7/jobs/{id}/stop
            #check if $AccessToken.accessTokenExpiresAt is expired
            if ((get-date ($AccessToken.accessTokenExpiresAt)) -lt ((Get-Date).AddMinutes(-1))) {
                write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Refreshing access token for $($ProxyServer.ClusterName)" 
                $AccessToken = Invoke-RestMethod -Method Post -Uri ($baseURI + "oauth2/token") -Body $LoginBody
                $header = @{Authorization = "Bearer $($AccessToken.accessToken)" }
            }
            
            Invoke-RestMethod -Method Post -Uri ($baseURI + "v7/jobs/$($job.id)/stop") -Headers $header
        }

        #wait for jobs to stop. timeout after 30 minutes
        $StartTime = Get-Date
        while ($RunningJobs) {
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Waiting for jobs to stop on $($ProxyServer.ClusterName)" 
           
            #check if $AccessToken.accessTokenExpiresAt is expired
            if ((get-date ($AccessToken.accessTokenExpiresAt)) -lt ((Get-Date).AddMinutes(-1))) {
                write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Refreshing access token for $($ProxyServer.ClusterName)" 
                $AccessToken = Invoke-RestMethod -Method Post -Uri ($baseURI + "oauth2/token") -Body $LoginBody
                $header = @{Authorization = "Bearer $($AccessToken.accessToken)" }
            }
            
            $RunningJobs = Invoke-RestMethod -Method Get -Uri ($baseURI + "v6/jobs") -Headers $header | Select-Object -ExpandProperty results | Where-Object { $_.status -eq "Running" -or $_.status -eq "Stopping" }
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Jobs still running or stopping on $($ProxyServer.ClusterName): $($RunningJobs | ft name,status | out-string)" 
            if ((Get-Date) - $StartTime -gt (New-TimeSpan -Minutes 30)) {
                write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Timed out waiting for jobs to stop on $($ProxyServer.ClusterName)" 
                break
            }
            Start-Sleep -Seconds 30
        }
        
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] RESTARTING SERVICE on $($ProxyServer.ClusterName)" 
        $PlaintextPassword=Decrypt-SecureString -Securestring $Credential.Password
        #$SSHCommand = 'echo "Stopping service..." && echo ' + $PlaintextPassword +' | sudo -S systemctl stop veeamahvbackup.service && echo "Waiting 10 seconds..." && sleep 10 && echo "Starting service..." && sudo systemctl start veeamahvbackup.service && echo "Done."'
        $SSHCommand = 'echo ' + $PlaintextPassword +' | sudo -S systemctl restart veeamahvbackup.service'
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Executing SSH command on $($ProxyServer.ProxyIP)" 
        try {
            $CommandOutput=Invoke-SSHCommandInStream -Computername $ProxyServer.ProxyIP -credential $Credential -Command $SSHCommand
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Service restart completed successfully for $($ProxyServer.ClusterName). Sleeping for 300 seconds to allow service to stabilize" 
            Start-Sleep -Seconds 300
        } catch {
            Write-Error "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ERROR: Failed to restart service on $($ProxyServer.ClusterName): $_" 
            return
        }

        #Retry failed veeamjobs

        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Restarting failed veeamjobs on $($ProxyServer.ClusterName)" 
          #check if $AccessToken.accessTokenExpiresAt is expired
        if ((get-date ($AccessToken.accessTokenExpiresAt)) -lt ((Get-Date).AddMinutes(-1))) {
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Refreshing access token for $($ProxyServer.ClusterName)" 
            $AccessToken = Invoke-RestMethod -Method Post -Uri ($baseURI + "oauth2/token") -Body $LoginBody
            $header = @{Authorization = "Bearer $($AccessToken.accessToken)" }
        }
        
        $CancelledJobs = Invoke-RestMethod -Method Get -Uri ($baseURI + "v6/jobs") -Headers $header | Select-Object -ExpandProperty results | Where-Object { $_.status -eq "Canceled"}
        
        foreach ($job in $CancelledJobs) {
            write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Restarting job $($job.name) on $($ProxyServer.ClusterName)" 
            #/api/v7/jobs/{id}/retry
            Invoke-RestMethod -Method Post -Uri ($baseURI + "v7/jobs/$($job.id)/retry") -Headers $header
        }
        


    } else {
        write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Significant bandwidth usage detected for $($ProxyServer.ClusterName) - NOT restarting service" 
    }
    
}

write-output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Script execution completed" 
}
catch{
    $errorstring=$_ | out-string 
    throw $errorstring
}