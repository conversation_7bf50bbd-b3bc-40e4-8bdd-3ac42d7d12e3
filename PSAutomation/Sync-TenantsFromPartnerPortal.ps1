﻿function ISObjectsEqual {
    [CmdletBinding()]
    param (
        
        [PSCustomObject]$ReferenceObject,
        [PSCustomObject]$DifferenceObject,
        [switch]$ShowEqualVerbose
    )
    Function Get-PropertyPaths {
        param(
            [PSCustomObject]$Object,
            [string[]]$path
        )
        if (!($path)) { $path = @() }
    
        $Members = $Object | gm | ? { $_.MemberType -eq "NoteProperty" }
    
        Foreach ($Member in $members) {
            if ($Member.Definition -like "*PSCustomObject*") {
                
                Get-PropertyPaths -Object $Object.($Member.name) -path ($Path + $Member.name)
            }
            else {
                
                [PSCustomObject]@{
                    Path = $path + $Member.name
                }
                
    
            }
        }
    }
    

    function Get-PropertyValue {
        param (
            $PropertyPath = $PropertyPaths[7].path,
            $Object = $object1
        )
        #Strip .path 
        if ("Path" -eq ($PropertyPath | gm | ? MemberType -eq "NoteProperty").name) { $PropertyPath = $PropertyPath.Path }
    
        $output = $Object
        foreach ($entry in $PropertyPath) {
            $output = $output.$entry
        }
    
        $output
        
    }


    #Get Properties To compare
    $PropertyPaths = Get-PropertyPaths $ReferenceObject

    $IsEqual = $true
    
    foreach ($PropertyPath in $PropertyPaths) {
        
        $RefValue = Get-PropertyValue -PropertyPath $PropertyPath -Object $ReferenceObject
        $DiffValue = Get-PropertyValue -PropertyPath $PropertyPath -Object $DifferenceObject
        

        
        if ($RefValue -ne $DiffValue) {
            Write-Verbose "Properties are NOT Equal: $($PropertyPath.Path -join ".")"
            Write-Verbose "RefValue: $RefValue"
            Write-Verbose "DiffValue: $DiffValue"
            
            $IsEqual = $false
        }
        else {
            if ($ShowEqualVerbose) {
                Write-Verbose "Properties are Equal: $($PropertyPath.Path -join ".")"
                Write-Verbose "RefValue: $RefValue"
                Write-Verbose "DiffValue: $DiffValue"
            }
        }

    }


    return $IsEqual
}

$ErrorActionPreference = "Stop"
Write-Output "Running on: $env:COMPUTERNAME"

#Netbox Authentication
$netboxCredentials = Get-SafeCredentials -CredentialName "NetboxToken"
$global:Params = Get-NetboxHeader -Credentials $NetboxCredentials


#Portal.api.cloudfactory.dk Authentication
$portalURI = "https://portal.api.cloudfactory.dk"
$refreshToken = "bleLFrOlW9Qt9xSYAdbKrNNA-K3Jvg1fy5lMHHlikR9WA"
$accesstoken = Invoke-RestMethod -Uri "$portalURI/Authenticate/ExchangeRefreshToken/$refreshtoken"
$portalHeader = @{Authorization = "Bearer $($accesstoken.access_token)" }

$portalProjects = Invoke-RestMethod -Uri "$($portalURI)/infrastructure/projects" -Headers $portalHeader
$portalPartners = Invoke-RestMethod -Uri "$($portalURI)/partners" -Headers $portalHeader
$PortalCustomerAccounts = Get-CFPortalAccounts -AccountType customer
$PortalCustomerAccountsWithIaaS = $PortalCustomerAccounts | ? { $_.roles.name -contains "Infrastructure" }
$ActivePortalCustomerAccountsWithIaaSAuth0IDs = @(
    "auth0|60c3491f1af3c00070040ca6"
    "auth0|61827cee73367b00713ba7a4"
    "auth0|618ba6b5a080bc006a895009"
    "auth0|618cdfe21f6a43006b15e422"
    "auth0|619366006d1f32006b380e27"
    "auth0|62d911b3789c8af838053977"
    "auth0|63033a05e2782ce304f8b383"
    "auth0|63467ca316b54c51947b0689"
    "auth0|639ae7065ee1de4a94c7241f"
    "auth0|63bbc448faf2a15f118003c1"
    "auth0|63bbc48ae46d0bcaf6a6dab8"
    "auth0|6411b4f2cdefe2e5f78c6384"
    "auth0|6411b51c64a8c709633e8a00"
    "auth0|6437c9ed56a355505be5a511"
    "auth0|64c0e0acb5de4d99a01cabd5"
    "auth0|64e6fed578dd207350a6d51c"
    "auth0|655750855dae3f3e59cfcfff"
    "auth0|6576f38f4988ad5b44007dcd"
    "auth0|657c01be8754e0a39e8426b3"
    "auth0|658048b8aa786a6d28613759"
    "auth0|65ba61c41f640e5eb650bd43"
    "auth0|663b23cf6a31c2ef5f0d947a"
    "auth0|663b32fddcb0daa0c26a05ea"
    "auth0|66548b0ab9a88b44ac97cba0"
    "auth0|6674077b81912b1b7fde7cb5"
    "auth0|6683a477ae5889408dd91887"
    "auth0|66aca2e1c6c5e2076f46ca2c"
    "auth0|66aca3d1effd7d106a59f260"
    "auth0|67222665d08dca7d3c8777f2"
    "auth0|679246066d4287c3c17c476d"
    "auth0|67a1dd3c3afcc2367b4b135c"
    "auth0|67b832135a9d71f4f4deb93a"
    "auth0|67f4d8ff3a3f4c5e2d53b3ea"
)
<#
Here is the code to generate the list above if we ever need to automate it. The query takes a long time to rung
To pppulate: $ActivePortalCustomerAccountsWithIaaSAuth0IDs
Add the AuthId's from $PortalCustomerAccountsWithIaaS.id


SELECT DISTINCT [AuthId]
  FROM [UtilityPortal_Log].[dbo].[LogRequests] with (NOLOCK)
  where 
  Method != 'GET' AND
 [Timestamp] >= DATEADD(DAY, -365, GETDATE()) AND
  Path like '/projects/%' and
  AuthId in (
'auth0|63bbc48ae46d0bcaf6a6dab8',
'auth0|672c5908918aded4b85478db',
'auth0|60521541f3739e006feccc9c',
'auth0|67f4d8ff3a3f4c5e2d53b3ea',
'auth0|6811ce174ad87c2ef52758bd',
'auth0|67a1dd3c3afcc2367b4b135c',
'auth0|663b32fddcb0daa0c26a05ea',
'auth0|680e01c7acfd9334c8ad001a',
'auth0|679246066d4287c3c17c476d',
'auth0|63033a05e2782ce304f8b383',
'auth0|64c0e0acb5de4d99a01cabd5',
'auth0|6683a477ae5889408dd91887',
'auth0|6674077b81912b1b7fde7cb5',
'auth0|67cfe57abcfbca5ba9a0eac2',
'auth0|67c99fa22ca556c2a3f5769e',
'auth0|657c01be8754e0a39e8426b3',
'auth0|60c888bda792d4006f910515',
'auth0|655c7f62707b1a94eaccc00d',
'auth0|63bbc448faf2a15f118003c1',
'auth0|66e9987fcf905aa1cec6e972',
'auth0|66e99bd81293b2e0dccff295',
'auth0|655f647fddb7753043f43bc4',
'auth0|63bbc4a8ceb10aafc7bd03f0',
'auth0|6389dccb72cd4fbb3617bac8',
'auth0|61d449c35c4760006f30838d',
'auth0|618280cbb3f423006a746f36',
'auth0|6177c2f8fd06220069be7d38',
'auth0|618ba6b5a080bc006a895009',
'auth0|6389aba0da1d4623637dabcc',
'auth0|67222665d08dca7d3c8777f2',
'auth0|60c3491f1af3c00070040ca6',
'auth0|677da087c434daf6b922ca31',
'auth0|6437c9ed56a355505be5a511',
'auth0|653b93bbd30619aabdf2f987',
'auth0|6411b4f2cdefe2e5f78c6384',
'auth0|642c112b8d75ed37fae0ad99',
'auth0|63c93301caf2d1ca334260cb',
'auth0|67d11f3d834e8fa69d08af0a',
'auth0|61d2a9c565c59f006fa804b3',
'auth0|62210753f0c3ec0069f7d19a',
'auth0|620f84b2e903e6006ae64e01',
'auth0|61d449f465c59f006fa8de07',
'auth0|6173fad9be378400711fb846',
'auth0|6177c2cda3966d0069fea48f',
'auth0|61502e5f84b01400714f0ce5',
'auth0|6811cf9d1428f0bec2060cf5',
'auth0|6808df411f9a70153f13f4f6',
'auth0|6808df0886710c79600ca951',
'auth0|66deab0fc1a5c194491fb183',
'auth0|636b37cfc1293528b35a5383',
'auth0|66dead4e12b85b00dc3e5fb6',
'auth0|619366006d1f32006b380e27',
'auth0|66aca3d1effd7d106a59f260',
'auth0|67a469d29648dde834f31006',
'auth0|66aca2e1c6c5e2076f46ca2c',
'auth0|66aca5957cb88958780f83a0',
'auth0|64d9e1f8365d2f8b4da7fc4f',
'auth0|618cdfe21f6a43006b15e422',
'auth0|664f4047833d344ee97374d5',
'auth0|65e04cccc9970973ab41989f',
'auth0|619cf020064bb600713201fd',
'auth0|64e6fed578dd207350a6d51c',
'auth0|65dc918c5c5f6167ee66e457',
'auth0|65a8fa3793f728451cd3ca34',
'auth0|62e23e4a9b178f10991f0f24',
'auth0|64e33abe0bed7d15d6657cc9',
'auth0|64c776480ea93ddca108c090',
'auth0|651a6d402f7fe87ee52343f9',
'auth0|64e6fb901a21d613cbe8af65',
'auth0|64d9dd0379a3955bee9af42b',
'auth0|6171625af4c52c006892630b',
'auth0|6388b25c587a94fd3d1963a7',
'auth0|63bbc46d216a67a12542f8ef',
'auth0|649d26a089f86ee10c868da5',
'auth0|649c284e8df5e480741926f6',
'auth0|622f0154a49ded00719a96a9',
'auth0|5fd882121c2467006a70141d',
'auth0|622f01002a09110069575bba',
'auth0|6388b201c44bd9c3979d9f38',
'auth0|6389dc8473f8c2f9e8db8f8f',
'auth0|622f0035a49ded00719a94c0',
'auth0|627cc6efcb435a0069bcab73',
'auth0|615e05270924c4006a493f4c',
'auth0|63467ca316b54c51947b0689',
'auth0|659e91dbb2cc5b50790e5c82',
'auth0|61b73858a1c2d100695af127',
'auth0|658048b8aa786a6d28613759',
'auth0|66548b0ab9a88b44ac97cba0',
'auth0|63bbc410b29a9353cbd6802d',
'auth0|66c84913cc1fa624c011c94e',
'auth0|6411b51c64a8c709633e8a00',
'auth0|6750892448c5cdf135f9246e',
'auth0|60cc66756e0b3f006acd87ec',
'auth0|6576f38f4988ad5b44007dcd',
'auth0|6530e3f73268c5ceec3f9877',
'auth0|61827cee73367b00713ba7a4',
'auth0|62d911b3789c8af838053977',
'auth0|67b832135a9d71f4f4deb93a',
'auth0|655750855dae3f3e59cfcfff',
'auth0|65ba61c41f640e5eb650bd43',
'auth0|6090f56d5ef01000690cb2cb',
'auth0|65f18bea4c4b7c985fff9066',
'auth0|660d0d945226dc6377f91803',
'auth0|6683e54e4ce7b076ba14e268',
'auth0|6722198a55483c9e2f74ee0f',
'auth0|663b23cf6a31c2ef5f0d947a',
'auth0|66b9f2d91c8f57b2c3993376',
'auth0|639ae7065ee1de4a94c7241f',
'auth0|67bdbcc83d8c877174c9e801',
'auth0|66d6d8a4e0cdd887e7175120'
)
#>
$ActivePortalCustomerAccountsWithIaaS = $PortalCustomerAccountsWithIaaS | ? id -in $ActivePortalCustomerAccountsWithIaaSAuth0IDs

$netboxTenants = Invoke-NBAPI -Command "tenancy/tenants/"
$netboxTenantGroups = Invoke-NBAPI -Command "tenancy/tenant-groups/"
$PortalCustomers = @()
$i = 0
$Projects = foreach ($portalproject in $portalprojects) {
    $i++
    $percent = [math]::Round(($i / ($portalprojects | Measure-Object).Count * 100), 0)
    Write-Progress -PercentComplete ($i / ($portalprojects | Measure-Object).Count * 100) -Status "$percent % - $($portalproject.name)" -Activity "Processing Customers"
    $partner = $portalPartners | ? { $_.id -eq $portalproject.partnerId }
    
    $PortalCustomer = $null
    if ($portalproject.customerid) {
        write-verbose "Project has customer assigned"
        $PortalCustomer = $PortalCustomers | Where-Object { $_.legacyId -eq $portalproject.customerId } | select -First 1
        if ($PortalCustomer) {
            Write-Verbose "Found customer in existing customers list"
        }
        else {
            Write-Verbose "Customer not found, retrieving customers for partner $($partner.name) (ID: $($partner.id))"
            try {
                if (!($PortalCustomers | ? partnerLegacyId -eq $partner.id)) {
                    #if the partners customers are not already in the list, retrieve them
                    $newCustomers = Get-cfportalEndCustomers -partnerID $partner.id
                }
                else {
                    $newCustomers = $null
                }

                if ($newCustomers) {
                    $PortalCustomers += $newCustomers
                    Write-Verbose "Added $(($newCustomers | Measure-Object).Count) customers to customer list"
                
                    # Check again if customer exists now
                    $PortalCustomer = $PortalCustomers | Where-Object { $_.legacyId -eq $portalproject.customerId } | select -First 1
                    if (-not  $PortalCustomer) {
                        throw "Customer with ID $($portalproject.customerId) not found after retrieving customer list for partner $($partner.name)"
                    }
                    Write-Verbose "Customer found after refresh"
                }
                else {
                    Write-Warning "No customers returned for partner $($partner.name) (ID: $($partner.id))"
                }
            }
            catch {
                Write-Warning "Failed to retrieve customer data: $_"
                
            }
        }
    
    }
    $CustomerisActivelyUsingPortal=$false
    if ($PortalCustomer) {
        # we found the customer. now check if the customer is actively using the portal
        
        $CustomerPortalAccountsWithIaaS = $ActivePortalCustomerAccountsWithIaaS | ? endCustomerId -eq $PortalCustomer.legacyId 
        if ($CustomerPortalAccountsWithIaaS) {
            $CustomerisActivelyUsingPortal = if ($CustomerPortalAccountsWithIaaS) { $true }else { $false }
            
        }
    }

    [PSCustomObject]@{
        partnerName    = $partner.name
        partnerId      = [string]$partner.id
        customerid     = $portalproject.customerId
        customername   = $PortalCustomer.name
        CustomerActive = $CustomerisActivelyUsingPortal
        projectName    = $portalproject.id
        type           = $portalproject.type
        Name           = $portalproject.name
    }
}

$NetboxIgnoreList = @(
    "<none>"
    "Fabric VLANS"
)

$netboxTenantsCompare = foreach ($netboxTenant in $netboxTenants) {
    #Skipping projects in ignore list
    if ($netboxTenant.name -in $NetboxIgnoreList) { Continue; Write-Verbose "$($netboxTenant.name) are in the ignore list" }
    
    if ($netboxTenant.name -like "*@*") { $Name = $netboxTenant.name }else { $Name = $netboxTenant.slug }
    [PSCustomObject]@{
        projectName = $Name
    }
}

$compareguids = Compare-Object $Projects.projectname $netboxTenantsCompare.projectname -IncludeEqual | Group-Object -Property SideIndicator
$NewProjects = $compareguids | ? { $_.Name -eq "<=" } | Select -ExpandProperty Group | Select -ExpandProperty InputObject
$ExistingProjects = $compareguids | ? { $_.Name -eq "==" } | Select -ExpandProperty Group | Select -ExpandProperty InputObject
$DeletedProjects = $compareguids | ? { $_.Name -eq "=>" } | Select -ExpandProperty Group | Select -ExpandProperty InputObject

$Encoding = [System.Text.Encoding]::UTF8

$FailedToDelete = @()
Write-Output "Deleting olds Projects"
#Delete old projects in Netbox
foreach ($DeletedProject in $DeletedProjects) {
    try {
        
        if ($DeletedProject -like "*@*") {
            $NetboxProject = $netboxTenants | ? { $_.name -eq $DeletedProject }
        }
        else {
            $NetboxProject = $netboxTenants | ? { $_.slug -eq $DeletedProject }
        }
        
        Invoke-RestMethod -Method Delete -Uri ($global:Params.baseuri + "/tenancy/tenants/$($NetboxProject.id)/") -Headers $global:Params.header -ContentType "application/json"
    }
    catch {
        $FailedToDelete += $NetboxProject.Name
        $_
    }
}

$FailedToCreate = @()
Write-Output "Creating new Projects"
#Create new Projects in Netbox
foreach ($NewProject in $NewProjects) {
    try {
        $Project = $Projects | ? { $_.ProjectName -eq $NewProject }
        if (($Project | measure).count -gt 1) { Continue; Write-Output "There is 2 Projects with same name" }

        #Define slug value
        if ($Project.type -eq "Hyperv") { $slug = (new-guid).Guid }else { $slug = $Project.projectName }

        #Define name value
        if ($Project.type -eq "Hyperv") { $name = $Project.projectName }else { $name = $Project.Name }

        #Define PartnerID value
        $1Partner = $netboxTenantGroups | ? { $_.slug -eq $Project.partnerId }
    
        $CreateNetboxTenantBody = @{
            name          = $name
            slug          = $slug
            custom_fields = [PSCustomObject]@{
                CustomerName = $Project.customername
                CustomerActive = $project.CustomerActive
            }
        }

        if ($1partner) {
            $CreateNetboxTenantBody += @{
                group = $1Partner.id
            }
        }
            
        Invoke-RestMethod -Method Post -Uri ($global:Params.baseuri + "/tenancy/tenants/") -ContentType "application/json" -Headers $global:Params.header -Body $Encoding.GetBytes(($CreateNetboxTenantBody | ConvertTo-Json))
    }
    catch {
        $CreateNetboxTenantBody
    }
    
}


#Update existing Projects in Netbox
Write-Output "Updating Projects"
foreach ($ExistingProject in $ExistingProjects) {
    $PortalProject = $Projects | ? { $_.projectName -eq $ExistingProject }
    
    if ($PortalProject.type -eq "Hyperv") {
        $NetboxProject = $netboxTenants | ? { $_.name -eq $ExistingProject }
        $PortalProjectCompare = [PSCustomObject]@{projectName = $PortalProject.projectName; partnerId = $PortalProject.partnerId; name = $portalproject.projectName }
        $NetboxProjectCompare = [PSCustomObject]@{projectName = $NetboxProject.name; partnerId = [string]$NetboxProject.group.slug; name = $NetboxProject.name }

    }
    
    if ($PortalProject.type -eq "Nutanix") {
        $NetboxProject = $netboxTenants | ? { $_.slug -eq $ExistingProject }
        $PortalProjectCompare = [PSCustomObject]@{
            projectName   = $PortalProject.projectName
            partnerId     = $PortalProject.partnerId
            name          = $portalproject.name 
            custom_fields = [PSCustomObject]@{
                CustomerName = $portalproject.customername
                CustomerActive = $portalproject.CustomerActive
            }
            
        }
        $NetboxProjectCompare = [PSCustomObject]@{
            projectName   = $NetboxProject.slug
            partnerId     = [string]$NetboxProject.group.slug
            name          = $NetboxProject.name 
            custom_fields = [PSCustomObject]@{
                CustomerName = $NetboxProject.custom_fields.CustomerName
                CustomerActive = $NetboxProject.custom_fields.CustomerActive
            }
        }
    }

    #$PortalProjectCompare = [PSCustomObject]@{projectName = $ProjectName; partnerId = [string]$NetboxProject.group.slug; name = $NetboxProject.name}
    #$NetboxProjectCompare = [PSCustomObject]@{projectName = $ProjectName; partnerId = [string]$NetboxProject.group.slug; name = $NetboxProject.name}

    if (($PortalProject | measure).count -gt 1) { Write-Verbose "There is 2 Projects with same name"; Continue }
    
    if (ISObjectsEqual -ReferenceObject $PortalProjectCompare -DifferenceObject $NetboxProjectCompare) {
        Write-Verbose "$($PortalProject.projectName) - Propeties are the same."
    }
    else {
        $Partner = $netboxTenantGroups | ? { $_.slug -eq $PortalProject.partnerId }
        
        $UpdateTenantBody = [PSCustomObject]@{
            name          = $PortalProjectCompare.name
            slug          = $NetboxProject.slug
            group         = $partner.id
            custom_fields = [PSCustomObject]@{
                CustomerName = $portalproject.customername
                CustomerActive = $portalproject.CustomerActive
            }
        }

        #if ($null -eq $partner){
        #    $UpdateTenantBody = $UpdateTenantBody | select name, slug
        #}

        Invoke-RestMethod -Method Put -Uri ($global:Params.baseuri + "/tenancy/tenants/$($NetboxProject.id)/") -ContentType "application/json" -Headers $global:Params.header -Body $Encoding.GetBytes(($UpdateTenantBody | ConvertTo-Json))
    }
    
}