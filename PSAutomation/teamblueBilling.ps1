#Requires -Modules SqlServer
$ErrorActionPreference = "Stop"
$NBVMs=Get-NetboxVMs
$NBTenants=Get-NetboxTenants


#Output to insert into SQL
$Output=[PSCustomObject]@{
    Date=get-date
    TBvmMemoryGB = $null
    TBvmCount = $null
}
#region TB and NON TB
$TBExcludeTenantsGroups=@(
    "Advania Danmark A/S",
    "itm8 | JDM A/S"
    "We got your Back"
    "Levitrax AI ApS"
)
$TBExcludeTenants=$NBTenants | Where-Object { $_.group.name -in $TBExcludeTenantsGroups }


#get all vms that are not in tenantgroups
$VMsFiltered=$NBVMs | Where-Object {$_.status.value -eq "Active" -and $_.tenant.id -notIn $TBExcludeTenants.id -and $_.custom_fields.vm_state -eq "on"}
$VMCountFiltered=$VMsFiltered | Measure-Object | select -ExpandProperty count
$SumMemoryMBFiltered=$VMsFiltered | Measure-Object -Sum -Property memory| select -ExpandProperty sum
$SumMemoryGBFiltered=[math]::Round($SumMemoryMBFiltered / 1024, 0)
$output.TBvmCount=$VMCountFiltered
$output.TBvmMemoryGB=$SumMemoryGBFiltered

#endregion TB and NON TB

#region Get all Tieto VMs
# Get all tenants that are part of the 'Tieto' tenant group
$TenantsInTietoGroup = $NBTenants | Where-Object { $_.group.name -eq "Tieto" }

# Get all VMs that belong to tenants within the Tieto group
$TietoGroupVMs = $NBVMs | Where-Object { 
    $_.status.value -eq "Active" -and 
    $_.tenant.id -in $TenantsInTietoGroup.id -and 
    $_.custom_fields.vm_state -eq "on"
}

# Calculate statistics for Tieto group VMs
$TietoGroupVMCount = $TietoGroupVMs | Measure-Object | Select-Object -ExpandProperty Count
$TietoGroupMemoryMB = $TietoGroupVMs | Measure-Object -Sum -Property memory | Select-Object -ExpandProperty Sum
$TietoGroupMemoryGB = [math]::Round($TietoGroupMemoryMB / 1024, 0)

# Add to output object
$Output | Add-Member -MemberType NoteProperty -Name "TietoGroupVMCount" -Value $TietoGroupVMCount -Force
$Output | Add-Member -MemberType NoteProperty -Name "TietoGroupMemoryGB" -Value $TietoGroupMemoryGB -Force

Write-Host "Tieto Group VMs: $TietoGroupVMCount, Total Memory: $($TietoGroupMemoryGB)GB"
#endregion Get all Tieto VMs

#insert data into SQL Table
$1password = op item get "portalbackend-prod.database.windows.net - PortalAdmin" --vault "PSAutomation" --reveal --format json  | ConvertFrom-Json 
$Username=$1password.fields | Where-Object label -eq "Username" | Select-Object -ExpandProperty value
$Password=$1password.fields | Where-Object label -eq "Password" | Select-Object -ExpandProperty value | ConvertTo-SecureString -AsPlainText -Force
$Credential = [PSCredential]::new($Username, $Password)

$appregparams = @{
    ServerInstance         = $1password.fields | Where-Object label -eq "Server" | Select-Object -ExpandProperty value
    Database     = $1password.fields | Where-Object id -eq "database" | Select-Object -ExpandProperty value
    Credential = $Credential
}
$Query = "INSERT INTO teamblue (Date, TBvmCount, TBvmMemoryGB, TietoGroupVMCount, TietoGroupMemoryGB) VALUES ('$($output.Date)', $($output.TBvmCount), $($output.TBvmMemoryGB), $($output.TietoGroupVMCount), $($output.TietoGroupMemoryGB))"
Invoke-Sqlcmd @appregparams -Query $Query