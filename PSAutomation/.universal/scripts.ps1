﻿New-PSUScript -Name "VDOMUdtræk.ps1" -Path "VDOMUdtræk.ps1" -InformationAction "Continue" -Credential "<EMAIL>" 
New-PSUScript -Name "PRTG-Maintenance.ps1" -Path "PRTG-Maintenance.ps1" -InformationAction "Continue" -Credential "<EMAIL>" 
New-PSUScript -Name "RunNetboxSync.ps1" -Path "RunNetboxSync.ps1" -InformationAction "Continue" -Credential "<EMAIL>" 
New-PSUScript -Name "TestMaskine.ps1" -Description "DA" -Path "TestMaskine.ps1" -InformationAction "Continue" 
New-PSUScript -Name "TraceRouteLog.ps1" -Path "TraceRouteLog.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Rebalance-CSV.ps1" -ManualTime "10" -Path "Rebalance-CSV.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "Get-CrashedVMs.ps1" -ManualTime "5" -Path "Get-CrashedVMs.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "RebootHostsInSpecificCluster.ps1" -Path "RebootHostsInSpecificCluster.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "UpdateHostCluster.ps1" -Path "UpdateHostCluster.ps1" -Environment "Windows PowerShell 5.1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "Get-InactiveFabricComputers.ps1" -Path "Get-InactiveFabricComputers.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Get-InactiveAccessComputers.ps1" -Path "Get-InactiveAccessComputers.ps1" -InformationAction "Continue" -Credential "<EMAIL>" 
New-PSUScript -Name "Get-InactiveAccessUsers.ps1" -Path "Get-InactiveAccessUsers.ps1" -InformationAction "Continue" 
New-PSUScript -Name "VMM_JobsLog_sender.ps1" -Path "VMM_JobsLog_sender.ps1" -InformationAction "Continue" 
New-PSUScript -Name "UnusedWapAdmins.ps1" -Path "UnusedWapAdmins.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Set-VMHostsMacPool.ps1" -Path "Set-VMHostsMacPool.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Get-CSVMoveTime.ps1" -Path "Get-CSVMoveTime.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Get-VMCriticalPausedTime.ps1" -Path "Get-VMCriticalPausedTime.ps1" -InformationAction "Continue" 
New-PSUScript -Name "CM_Events_Sender.ps1" -Path "CM_Events_Sender.ps1" -InformationAction "Continue" 
New-PSUScript -Name "AcronisData_ROT.ps1" -Path "AcronisData_ROT.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Get-TenantsWithoutAddons.ps1" -Path "Get-TenantsWithoutAddons.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Get-MacAddrDuplicates.ps1" -Path "Get-MacAddrDuplicates.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Collect-VMM Data New.ps1" -Path "Collect-VMM Data New.ps1" -InformationAction "Continue" 
New-PSUScript -Name "CLusterErrorReport-v2.ps1" -Path "CLusterErrorReport-v2.ps1" -InformationAction "Continue" 
New-PSUScript -Name "NutanixCosmosDbSync.ps1" -Path "NutanixCosmosDbSync.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "CosmosDBHostDataSync.ps1" -Path "CosmosDBHostDataSync.ps1" -InformationAction "Continue" 
New-PSUScript -Name "CosmosDBVMSync.ps1" -Path "CosmosDBVMSync.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "DrainHost.ps1" -Path "DrainHost.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Import-vLANsFromWAPToNetbox.ps1" -Path "Import-vLANsFromWAPToNetbox.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Sync-Netbox-New.ps1" -Path "Sync-Netbox-New.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Sync-NutanixVlansToNetbox.ps1" -Path "Sync-NutanixVlansToNetbox.ps1" -Environment "Windows PowerShell 5.1" -InformationAction "Continue" -Credential "<EMAIL>" 
New-PSUScript -Name "Get-InactiveFabricUsers.ps1" -Path "Get-InactiveFabricUsers.ps1" -InformationAction "Continue" 
New-PSUScript -Name "HowManyVMs.ps1" -Path "HowManyVMs.ps1" -InformationAction "Continue" 
New-PSUScript -Name "SetKerberosDelegation.ps1" -Path "SetKerberosDelegation.ps1" -Environment "Windows PowerShell 5.1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "Invoke-Reclaim.ps1" -Path "Invoke-Reclaim.ps1" -InformationAction "Continue" 
New-PSUScript -Name "RebootUnhealthyNodes.ps1" -Path "RebootUnhealthyNodes.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "Sync-TenantsFromPartnerPortal.ps1" -Path "Sync-TenantsFromPartnerPortal.ps1" -InformationAction "Continue" 
New-PSUScript -Name "UpdateEndpoints.ps1" -TimeOut "1" -Path "UpdateEndpoints.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "RefreshAllVMs.ps1" -Path "RefreshAllVMs.ps1" -InformationAction "Continue" 
New-PSUScript -Name "SyncPartnersFromPartnerPortal.ps1" -Path "SyncPartnersFromPartnerPortal.ps1" -InformationAction "Continue" 
New-PSUScript -Name "RebootHosts.ps1" -Path "RebootHosts.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "SaveVMLocation.ps1" -Path "SaveVMLocation.ps1" -InformationAction "Continue" 
New-PSUScript -Name "CFPORTALSQL Delete Bodies.ps1" -Path "CFPORTALSQL Delete Bodies.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "Sync-NutanixVMsIntoNetbox.ps1" -Path "Sync-NutanixVMsIntoNetbox.ps1" -InformationAction "Continue" -Credential "<EMAIL>" 
New-PSUScript -Name "Sync-VMMVMsIntoNetbox.ps1" -Path "Sync-VMMVMsIntoNetbox.ps1" -InformationAction "Continue" 
New-PSUScript -Name "Sync-NutanixHosts.ps1" -Path "Nutanix\Sync-NutanixHosts.ps1.ps1" -ErrorAction "Stop" -InformationAction "Continue" 
New-PSUScript -Name "Update-VMsOnDefaultProject.ps1" -Path "Nutanix\Update-VMsOnDefaultProject.ps1.ps1" -Environment "Windows PowerShell 5.1" -InformationAction "Continue" 
New-PSUScript -Name "Sync-VDOMs.ps1" -Description "Sync VDOMs into Netbox (1500D)" -Path "Sync-VDOMs.ps1.ps1" -Environment "Windows PowerShell 5.1" -InformationAction "Continue" 
New-PSUScript -Name "Sync-VDOMs500E.ps1" -Description "Sync VDOMs into Netbox (500E)" -Path "Sync-VDOMs500E.ps1.ps1" -Environment "Windows PowerShell 5.1" -InformationAction "Continue" 
New-PSUScript -Name "VDOM-Usage.ps1" -Description "Udtræk af VDOMs til slack" -Path "VDOM-Usage.ps1" -Environment "Windows PowerShell 5.1" -InformationAction "Continue" 
New-PSUScript -Name "RackUForbrug.ps1" -Description "Rack U Forbrug" -Path "RackUForbrug.ps1.ps1" -InformationAction "Continue"