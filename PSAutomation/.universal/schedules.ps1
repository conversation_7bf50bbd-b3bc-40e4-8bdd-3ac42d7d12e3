﻿New-PSUSchedule -<PERSON><PERSON> "00 11 * * *" -<PERSON>ript "Get-InactiveFabricComputers.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "00 11 * * *" -Script "Get-InactiveAccessComputers.ps1" -TimeZone "Europe/Paris" -Credential "AccessAuth" 
New-PSUSchedule -Cron "00 11 * * *" -Script "Get-InactiveFabricUsers.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "00 11 * * *" -Script "Get-InactiveAccessUsers.ps1" -TimeZone "Europe/Paris" -Credential "AccessAuth" 
New-PSUSchedule -Cron "0 0 * * *" -Script "Set-VMHostsMacPool.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 * * * *" -Script "Get-CSVMoveTime.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 * * * *" -<PERSON><PERSON><PERSON> "Get-VMCriticalPausedTime.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 08 * * *" -<PERSON><PERSON><PERSON> "CM_Events_Sender.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 08 * * *" -Script "VMM_JobsLog_sender.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 07 * * *" -Script "Get-MacAddrDuplicates.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 7 * * *" -Script "CLusterErrorReport-v2.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "20 00 * * *" -Script "CosmosDBHostDataSync.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 0 * * *" -Script "Invoke-Reclaim.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 0 * * *" -Script "SetKerberosDelegation.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "15,35,55 * * * *" -Script "Sync-NutanixVlansToNetbox.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Script "UpdateHostCluster.ps1" -TimeZone "Europe/Copenhagen" -OneTime '2021-09-21T10:13:00.0000000Z'-HostClusterNamesToUpgrade 'DK01S01C08' 
New-PSUSchedule -Cron "*/30 * * * *" -Script "RebootUnhealthyNodes.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Script "UpdateHostCluster.ps1" -TimeZone "Europe/Copenhagen" -OneTime '2021-09-29T07:31:12.0000000Z'-HostClusterNamesToUpgrade 'DK01S01C06' 
New-PSUSchedule -Cron "0 0 * * *" -Script "RefreshAllVMs.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "00 23 * * *" -Script "NutanixCosmosDbSync.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "00 23 * * *" -Script "Collect-VMM Data New.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "00 23 * * *" -Script "CosmosDBVMSync.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Script "UpdateHostCluster.ps1" -TimeZone "Europe/Copenhagen" -OneTime '2021-10-05T07:58:04.0000000Z'-HostClusterNamesToUpgrade 'DK01S01C06' 
New-PSUSchedule -Script "UpdateHostCluster.ps1" -TimeZone "Europe/Copenhagen" -OneTime '2021-10-05T09:03:26.0000000Z'-HostClusterNamesToUpgrade 'DK01S01C06' 
New-PSUSchedule -Script "UpdateHostCluster.ps1" -TimeZone "Europe/Copenhagen" -OneTime '2021-10-05T11:25:12.0000000Z'-HostClusterNamesToUpgrade 'DK01S01C06' 
New-PSUSchedule -Script "UpdateHostCluster.ps1" -TimeZone "Europe/Copenhagen" -OneTime '2021-10-06T06:56:00.0000000Z'-HostClusterNamesToUpgrade 'DK01S01C06' 
New-PSUSchedule -Cron "0 * * * *" -Script "SyncPartnersFromPartnerPortal.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "*/10 * * * *" -Script "SaveVMLocation.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "0 0 * * *" -Script "CFPORTALSQL Delete Bodies.ps1" -TimeZone "Europe/Paris" 
New-PSUSchedule -Cron "0 0 * * *" -Script "Rebalance-CSV.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "*/20 * * * *" -Script "Sync-VMMVMsIntoNetbox.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "0,20,40 * * * *" -Script "Sync-NutanixVMsIntoNetbox.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "10,30,50 * * * *" -Script "Nutanix\Sync-NutanixHosts.ps1.ps1" -TimeZone "Europe/Copenhagen" -Name "Sync-NutanixHosts" 
New-PSUSchedule -Cron "*/10 * * * *" -Script "Nutanix\Update-VMsOnDefaultProject.ps1.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "0 18 * * *" -Script "Sync-VDOMs.ps1.ps1" -TimeZone "Europe/Copenhagen" -Name "Sync VDOMs 1500D" 
New-PSUSchedule -Cron "0 18 * * *" -Script "Sync-VDOMs500E.ps1.ps1" -TimeZone "Europe/Copenhagen" -Name "Sync VDOMs 500E" 
New-PSUSchedule -Cron "0 7 25 * *" -Script "VDOM-Usage.ps1" -TimeZone "Europe/Copenhagen" 
New-PSUSchedule -Cron "0 7 25 * *" -Script "RackUForbrug.ps1.ps1" -TimeZone "Europe/Copenhagen" -Name "RackUForbrug"
