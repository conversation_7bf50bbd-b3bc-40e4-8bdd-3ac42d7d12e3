$ErrorActionPreference="Stop"

Write-Output $env:COMPUTERNAME
Write-Output "Whoami: $(whoami.exe)"
$VBRServers = @(
    "DK01VBR01.iaas.cloudfactory.dk"
    "DK01VBR02.iaas.cloudfactory.dk"
)
$concred = Get-SafeCredentials -CredentialName SerPSROT

$LicenseData=Invoke-Command -ComputerName $VBRServers -Credential $concred -ScriptBlock {
    $license = Get-VBRInstalledLicense
    $LicenseData= Get-VBRInstanceLicenseSummary -License $license
    $StringOutput = $LicenseData.Object | ConvertTo-Json
    $billigfor = "Cloud Factory"
    if($env:COMPUTERNAME -match "DK01VBR01"){$billigfor = "team.blue"}
    Write-Output $StringOutput
    #send email
    Send-MailMessage -From "<EMAIL>" -to "<EMAIL>" -cc "<EMAIL>" -Body $StringOutput -Subject "Cloud Factory Veeam License Consumption Report for $billigfor" -SmtpServer "cloudfactory-dk.mail.protection.outlook.com" 
}