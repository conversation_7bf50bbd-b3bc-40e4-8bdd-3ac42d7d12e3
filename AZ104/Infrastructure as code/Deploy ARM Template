Connect-AzAccount

$SubscriptionID = Get-AzSubscription -SubscriptionName "Concierge Subscription" | select -ExpandProperty id

$ResourceGroupName="learn-07225c32-4e71-4f47-ab80-f42a5618e8f4" #hardcoded name for learning
$context = Get-AzSubscription -SubscriptionId $SubscriptionID
Set-AzContext $context
Set-AzDefault -ResourceGroupName $ResourceGroupName

$templateFile="azuredeploy.json"
$today=Get-Date -Format "MM-dd-yyyy"
$deploymentName="blanktemplate-"+"$today"

New-AzResourceGroupDeployment -Name $deploymentName -TemplateFile $templateFile -ResourceGroupName $ResourceGroupName -storagename "omgdeterenfedstor"

Get-AzVMSize -Location "westus" | Format-Table

Get-AzVM | Format-Table