{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "storagename": {
            "type": "string",
            "minLength": 3,
            "maxLength": 24,
            "metadata": {
                "description": "The name of the azure storage resource"
            }
        },
        "storageSKU": {
            "type": "string",
            "defaultValue": "Standard_GRS",
            "allowedValues": [
                "Standard_LRS", //local redundant
                "Standard_GRS", //Geo Redundant
                "Standard_ZRS"  //Zone redundant
            ]
        }
    },
    "functions": [],
    "variables": {},
    "resources": [
        {
            "name": "[parameters('storagename')]",
            "type": "Microsoft.Storage/storageAccounts",
            "apiVersion": "2023-05-01",
            "tags": {
                "displayName": "[parameters('storagename')]"
            },
            "location": "[resourceGroup().location]",
            "kind": "StorageV2",
            "sku": {
                "name": "[parameters('storageSKU')]",
                "tier": "Standard"
            }
        }
    ],
    "outputs": {
        "output1": {
            "type": "secureobject",
            "value": "[reference(parameters('storagename')).primaryEndpoints]"
        }
    }
}