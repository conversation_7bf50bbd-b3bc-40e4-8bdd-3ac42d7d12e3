$ErrorActionPreference = "Stop"

#region Functions
function Get-DeviceCodeAuthentication {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$Scope = "https://management.azure.com/.default offline_access"
    )
    
    $deviceCodeUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/devicecode"
    
    $body = @{
        client_id = $ClientId
        scope     = $Scope
    }
    
    try {
        $deviceCodeResponse = Invoke-RestMethod -Uri $deviceCodeUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        
        # Display the message to the user
        Write-Host $deviceCodeResponse.message -ForegroundColor Cyan
        
        # Poll for token
        $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
        $tokenBody = @{
            grant_type  = "device_code"
            device_code = $deviceCodeResponse.device_code
            client_id   = $ClientId
        }
        
        $interval = $deviceCodeResponse.interval
        $expiresOn = (Get-Date).AddSeconds($deviceCodeResponse.expires_in)
        
        while ((Get-Date) -lt $expiresOn) {
            try {
                Start-Sleep -Seconds $interval
                $tokenResponse = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $tokenBody -ContentType "application/x-www-form-urlencoded"
                return $tokenResponse
            }
            catch {
                # Check if this is an authorization_pending error, which is expected during polling
                $errorObj = $null
                try {
                    if ($_.ErrorDetails.Message) {
                        $errorObj = $_.ErrorDetails.Message | ConvertFrom-Json
                    }
                }
                catch {}
                
                # If it's authorization_pending, continue polling
                if ($errorObj -and $errorObj.error -eq "authorization_pending") {
                    Write-Host "Waiting for authentication..." -ForegroundColor Yellow
                    continue
                }
                
                # For any other error, throw
                Write-Error "Error during token polling: $_"
                throw
            }
        }
        
        throw "Authentication timed out"
    }
    catch {
        Write-Error "Error during device code authentication: $_"
        throw
    }
}

function Get-AzureCostAnalyticsSubscriptions {
    param (
        [string]$AccessToken,
        [string]$BillingAccountId,
        [datetime]$StartDate,
        [datetime]$EndDate,
        [int]$ResolutionDays = 1
    )
    
    # Ensure ResolutionDays is at least 1
    if ($ResolutionDays -lt 1) {
        $ResolutionDays = 1
    }

    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Accept"        = "application/json"
    }

    # Generate date range based on resolution
    $dateRange = @()
    $currentDate = $StartDate.Date
    while ($currentDate -lt $EndDate.Date) {
        $dateRange += $currentDate
        $currentDate = $currentDate.AddDays($ResolutionDays)
    }
    
    # Ensure we don't miss the last day if ResolutionDays is large
    if (($dateRange | Select-Object -Last 1) -lt $EndDate.Date) {
        $dateRange += $EndDate.Date
    }

    $allResults = @()

    # Process each period based on resolution
    for ($i = 0; $i -lt $dateRange.Count - 1; $i++) {
        $periodStart = $dateRange[$i]
        $periodEnd = $dateRange[$i + 1].AddSeconds(-1)  # End of the period
        
        # If this is the last period, make sure it includes the end date
        if ($i -eq $dateRange.Count - 2) {
            $periodEnd = $EndDate
        }

        $body = @"
        {
          "type": "ActualCost",
          "dataSet": {
            "granularity": "None",
            "aggregation": {
              "totalCost": {
                "name": "Cost",
                "function": "Sum"
              },
              "totalCostUSD": {
                "name": "CostUSD",
                "function": "Sum"
              }
            },
            "sorting": [
              {
                "direction": "ascending",
                "name": "UsageDate"
              }
            ],
            "grouping": [
              {
                "type": "Dimension",
                "name": "CustomerTenantId"
              },
              {
                "type": "Dimension",
                "name": "CustomerName"
              },
              {
                "type": "Dimension",
                "name": "SubscriptionId"
              },
              {
                "type": "Dimension",
                "name": "SubscriptionName"
              },
              {
                "type": "Dimension",
                "name": "PartnerEarnedCreditApplied"
              }
            ],
            "filter": {
              "Dimensions": {
                "Name": "Frequency",
                "Operator": "In",
                "Values": [
                  "UsageBased"
                ]
              }
            }
          },
          "timeframe": "Custom",
          "timePeriod": {
            "from": "$($periodStart.ToString('yyyy-MM-ddTHH:mm:sszzz'))",
            "to": "$($periodEnd.ToString('yyyy-MM-ddTHH:mm:sszzz'))"
          }
        }
"@

        $rows = @()
        $uri = "https://management.azure.com/providers/Microsoft.Billing/billingAccounts/$BillingAccountId/providers/Microsoft.CostManagement/query?api-version=2021-10-01"
        
        do {
            $maxRetries = 10
            $retryCount = 0
            $success = $false
            
            while (-not $success -and $retryCount -lt $maxRetries) {
                try {
                    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body -ContentType "application/json"
                    $success = $true
                }
                catch {
                    if ($_.Exception.Response.StatusCode -eq 429) {
                        $retryCount++
                        $sleepDuration = [Math]::Pow(2, $retryCount) # Exponential backoff: 2, 4, 8, 16, etc.
                        Write-Warning "Rate limit (429) encountered. Retry attempt $retryCount of $maxRetries. Waiting for $sleepDuration seconds..."
                        Start-Sleep -Seconds $sleepDuration
                    }
                    else {
                        # For non-rate limit errors, rethrow the exception
                        Write-Error "Error processing date $($date.ToString('yyyy-MM-dd')): $_"
                        $retryCount = $maxRetries # Skip to next date on non-rate limit errors
                    }
                }
            }
            
            if (-not $success) {
                Write-Warning "Skipping date $($date.ToString('yyyy-MM-dd')) after $maxRetries retry attempts due to rate limiting."
                continue
            }
            
            $columns = $response.properties.columns
            $rows += $response.properties.rows
            $uri = $response.properties.nextLink
        } while ($uri)

        $table = $rows | ForEach-Object {
            $row = $_
            $properties = @{}
            for ($i = 0; $i -lt $columns.Count; $i++) {
                $columnName = $columns[$i].name
                $value = $row[$i]
                
                # Handle specific column types
                if ($columnName -eq "PartnerEarnedCreditApplied") {
                    $value = if ($value -eq 0) { "false" } elseif ($value -eq 1) { "true" } else { "unknown" }
                }
                
                $properties[$columnName] = $value
            }
            
            # Add the period start date to each record
            $properties["UsageDate"] = $periodStart
            [PSCustomObject]$properties
        }
        
        if ($rows.Count -gt 0) {
            $allResults += $table
        }
    }
    
    return $allResults
}

function Get-RoleAssignments {
    param (
        [string]$AccessToken,
        [string]$SubscriptionId
    )
    
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "Content-Type"  = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://management.azure.com/subscriptions/$SubscriptionId/providers/Microsoft.Authorization/roleAssignments?api-version=2022-04-01" -Method Get -Headers $headers
        return $response.value
    }
    catch {
        Write-Error "Error retrieving role assignments: $_"
        throw
    }
}

function Get-AccessTokenForTargetTenant {
    param (
        [string]$TargetTenantId,
        [string]$ClientId,
        [string]$RefreshToken,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TargetTenantId/oauth2/v2.0/token"
    
    $body = @{
        client_id     = $ClientId
        scope         = $Scope
        refresh_token = $RefreshToken
        grant_type    = "refresh_token"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token for target tenant: $_"
        throw $_
    }
}

function Get-AccessTokenAppRegistration {
    param (
        [string]$TenantId,
        [string]$ClientId,
        [string]$ClientSecret,
        [string]$Scope = "https://management.azure.com/.default"
    )
    
    $tokenUrl = "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token"
    
    $body = @{
        grant_type    = "client_credentials"
        client_id     = $ClientId
        client_secret = $ClientSecret
        scope         = $Scope
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response
    }
    catch {
        Write-Error "Error obtaining access token using app registration: $_"
        throw $_
    }
}

function Get-CosmosDbCFDocuments {
    param (
        $cosmosDbContext,
        $CollectionId = 'BilledUsage',
        $documentsPerRequest = 20,
        $query = "",
        $MaxItemCount = 999

    )
    $ErrorActionPreference = 'Stop'

    $continuationToken = $null
    $documents = @()

    do {
        try {
            Write-Progress -Activity "Getting CosmosDB Documents $($cosmosDbContext.account)" -status "Documents: $($documents.count) / $MaxItemCount" -PercentComplete ($documents.count / $MaxItemCount * 100)
        }
        catch {
            
        }

        $responseHeader = $null
        $getCosmosDbDocumentParameters = @{
            Context        = $cosmosDbContext
            CollectionId   = $CollectionId
            MaxItemCount   = $documentsPerRequest
            ResponseHeader = ([ref] $responseHeader)
            query          = $query
        }

        if ($continuationToken) {

            $getCosmosDbDocumentParameters += @{
                ContinuationToken = $continuationToken
            }
        }
        $documents += Get-CosmosDbDocument @getCosmosDbDocumentParameters -QueryEnableCrossPartition:$true
        $continuationToken = [System.String] $responseHeader.'x-ms-continuation'
        #Write-Verbose $continuationToken 
    } while (-not [System.String]::IsNullOrEmpty($continuationToken) -and $documents.count -lt $MaxItemCount)
    return $documents
}

#endregion Functions



#authenticate CosmosDB
$OPData = op item get "cf-prod-db1" --vault "PSAutomation" --reveal --format json  | ConvertFrom-Json 
$primaryKey = $OPData.fields | Where-Object id -eq "password" | Select-Object -ExpandProperty value |  ConvertTo-SecureString -AsPlainText -Force 
$CosmosDbContext = New-CosmosDbContext -Account 'cf-prod-db1' -Database 'Microsoft' -Key $primaryKey
$CosmosParams=@{
    CosmosDBContext        = $cosmosDbContext
    CollectionId   ="UnbilledUsage"
    documentsPerRequest = 100000
    MaxItemCount   = 999999
}


$StartDate = (Get-Date).AddDays( -2).Date
$EndDate = (Get-Date).AddDays( -1).Date
$query = "SELECT * FROM c WHERE c.UsageDate >= '$($StartDate.ToString("yyyy-MM-dd"))' AND c.UsageDate <= '$($EndDate.ToString("yyyy-MM-dd"))'"


$StartDate = Get-Date "1. april 2025"
$EndDate = Get-Date "30. april 2025"
$query = "SELECT * FROM c WHERE c.UsageDate >= '$($StartDate.ToString("yyyy-MM-dd"))' AND c.UsageDate <= '$($EndDate.ToString("yyyy-MM-dd"))'"



$Documents = Get-CosmosDbCFDocuments @CosmosParams -query $query 

$Documents | Group-Object ResourceURI | % {
    try {
        $PriceFirst=$_.Group | Sort-Object UsageDate | Select-Object -First 1 PricingPreTaxTotal
        $PriceLast=$_.Group | Sort-Object UsageDate | Select-Object -Last 1 PricingPreTaxTotal
        $PriceDifference=$PriceLast.PricingPreTaxTotal - $PriceFirst.PricingPreTaxTotal
        $PriceDifferencePercent=$PriceDifference / $PriceFirst.PricingPreTaxTotal * 100
    }
    catch {
        write-warning $_.group | Out-String

    }
   
    
    
    [PSCustomObject]@{
        SubscriptionID = $_.Name
        SubscriptionName = $_.Group.SubscriptionName
        StartDate = $_.Group | Sort-Object UsageDate | Select-Object -First 1 -expand UsageDate
        EndDate = $_.Group | Sort-Object UsageDate | Select-Object -Last 1 -expand UsageDate
        PriceFirst = $PriceFirst.PricingPreTaxTotal
        PriceLast = $PriceLast.PricingPreTaxTotal
        PriceDifference = $PriceDifference
        PriceDifferencePercent = $PriceDifferencePercent
    }
}


$Resources = $Documents | Group-Object @{ 
    Expression = {
        #"$($_.ResourceURI)|$($_.ProductId)|$($_.EntitlementId)"
        "$($_.ResourceURI)"
        #"$($_.ResourceURI)|$($_.MeterName)"
    }
}


$PriceResults=$Resources | Where-Object {($_.Group | Select-Object -ExpandProperty UsageDate | Select-Object -Unique | Measure-Object | Select-Object -ExpandProperty Count) -ge 2}  | ForEach-Object {
   $GroupedDates=$_.Group |Group-Object UsageDate | Sort-Object name
   $LastDay=$null
   foreach ($CurrentDate in $GroupedDates){ 
    if (!$($LastDay)){
        $lastDay=$CurrentDate
        continue
    }
    #throw ""
    $SumUSDCurrentDate=$CurrentDate.Group | Measure-Object PricingPreTaxTotal -Sum | select -ExpandProperty Sum
    $SumUSDLastDate=$LastDay.Group | Measure-Object PricingPreTaxTotal -Sum | select -ExpandProperty Sum
    $PriceDifference=$SumUSDCurrentDate - $SumUSDLastDate
    $PriceDifferencePercent=$PriceDifference / $SumUSDLastDate * 100
    

   }

    [PSCustomObject]@{
        CustomerCountry = $_.Group | Select-Object -ExpandProperty CustomerCountry -Unique
        CustomerName = $_.Group | Select-Object -ExpandProperty CustomerName -Unique

        
        SumUSDLastDate = $SumUSDLastDate
        SumUSDCurrentDate = $SumUSDCurrentDate
        PriceDifference = $PriceDifference
        PriceDifferencePercent = $PriceDifferencePercent
        ResourceURI = $_.Name
        ProductName = $_.Group | Select-Object -ExpandProperty ProductName -Unique
        MeterName = $_.Group | Select-Object -ExpandProperty MeterName -Unique
        Resources=$_.Group
    }

}

$PriceResults | Where-Object { $_.SumUSDCurrentDate -gt 50 } | Sort-Object PriceDifferencePercent -Descending | Select-Object -First 10 -ExcludeProperty ResourceURI | ft
